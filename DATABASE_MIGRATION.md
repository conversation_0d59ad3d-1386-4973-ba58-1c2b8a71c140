# Database Migration Guide

## Issue
The review queue features require a new `last_interval` column in the `reviews` table that may not exist in existing databases.

## Quick Fix

### Option 1: Automatic Migration (Recommended)
The application will automatically attempt to add the missing column when it starts. If you see errors related to `column "last_interval" does not exist`, try restarting the application.

### Option 2: Manual Migration Script
Run the migration script manually:

```bash
node scripts/migrate-database.js
```

### Option 3: SQL Migration
Connect to your database and run:

```sql
-- Add the missing column
ALTER TABLE reviews ADD COLUMN last_interval INTEGER DEFAULT 1;

-- Update existing reviews
UPDATE reviews SET last_interval = 1 WHERE last_interval IS NULL;
```

### Option 4: Docker Rebuild
If using Docker, rebuild the containers to ensure the latest schema:

```bash
docker compose down
docker compose up --build
```

## Verification
After running the migration, you should be able to:
- Add papers to review queue
- Add collections to review queue
- See review status indicators
- Use the review functionality without errors

## Troubleshooting
If you continue to see errors:
1. Check that the `last_interval` column exists: `\d reviews` in psql
2. Verify the column has a default value of 1
3. Restart the application after running the migration
4. Check the application logs for any remaining database errors

The application is designed to work with or without the `last_interval` column, so basic functionality should work even if the migration fails.
