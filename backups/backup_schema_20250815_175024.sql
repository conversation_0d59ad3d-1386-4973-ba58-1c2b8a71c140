--
-- PostgreSQL database dump
--

-- Dumped from database version 15.13
-- Dumped by pg_dump version 15.13

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

--
-- Name: uuid-ossp; Type: EXTENSION; Schema: -; Owner: -
--

CREATE EXTENSION IF NOT EXISTS "uuid-ossp" WITH SCHEMA public;


--
-- Name: EXTENSION "uuid-ossp"; Type: COMMENT; Schema: -; Owner: 
--

COMMENT ON EXTENSION "uuid-ossp" IS 'generate universally unique identifiers (UUIDs)';


--
-- Name: update_updated_at_column(); Type: FUNCTION; Schema: public; Owner: papernugget
--

CREATE FUNCTION public.update_updated_at_column() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$;


ALTER FUNCTION public.update_updated_at_column() OWNER TO papernugget;

--
-- Name: update_user_updated_at_column(); Type: FUNCTION; Schema: public; Owner: papernugget
--

CREATE FUNCTION public.update_user_updated_at_column() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$;


ALTER FUNCTION public.update_user_updated_at_column() OWNER TO papernugget;

SET default_tablespace = '';

SET default_table_access_method = heap;

--
-- Name: audit_logs; Type: TABLE; Schema: public; Owner: papernugget
--

CREATE TABLE public.audit_logs (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    user_id uuid,
    action character varying(100) NOT NULL,
    resource_type character varying(50),
    resource_id character varying(255),
    ip_address inet,
    user_agent text,
    details jsonb DEFAULT '{}'::jsonb,
    created_at timestamp with time zone DEFAULT now()
);


ALTER TABLE public.audit_logs OWNER TO papernugget;

--
-- Name: collections; Type: TABLE; Schema: public; Owner: papernugget
--

CREATE TABLE public.collections (
    id character varying(255) NOT NULL,
    name character varying(255) NOT NULL,
    paper_ids text[] DEFAULT '{}'::text[],
    user_id uuid NOT NULL,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    zotero_library_type character varying(10),
    zotero_library_id character varying(255),
    CONSTRAINT collections_zotero_library_type_check CHECK (((zotero_library_type)::text = ANY ((ARRAY['user'::character varying, 'group'::character varying])::text[])))
);


ALTER TABLE public.collections OWNER TO papernugget;

--
-- Name: COLUMN collections.zotero_library_type; Type: COMMENT; Schema: public; Owner: papernugget
--

COMMENT ON COLUMN public.collections.zotero_library_type IS 'Type of Zotero library for this collection: user (My Library) or group';


--
-- Name: COLUMN collections.zotero_library_id; Type: COMMENT; Schema: public; Owner: papernugget
--

COMMENT ON COLUMN public.collections.zotero_library_id IS 'ID of the Zotero library/group. NULL for user library, group ID for group libraries';


--
-- Name: email_verification_tokens; Type: TABLE; Schema: public; Owner: papernugget
--

CREATE TABLE public.email_verification_tokens (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    user_id uuid NOT NULL,
    token_hash character varying(255) NOT NULL,
    expires_at timestamp with time zone NOT NULL,
    used boolean DEFAULT false,
    created_at timestamp with time zone DEFAULT now()
);


ALTER TABLE public.email_verification_tokens OWNER TO papernugget;

--
-- Name: notes; Type: TABLE; Schema: public; Owner: papernugget
--

CREATE TABLE public.notes (
    id character varying(255) NOT NULL,
    paper_id character varying(255) NOT NULL,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    quick_summary text,
    key_ideas text[] DEFAULT '{}'::text[]
);


ALTER TABLE public.notes OWNER TO papernugget;

--
-- Name: papers; Type: TABLE; Schema: public; Owner: papernugget
--

CREATE TABLE public.papers (
    id character varying(255) NOT NULL,
    title text NOT NULL,
    authors text[] DEFAULT '{}'::text[],
    venue character varying(255),
    year integer,
    doi character varying(255),
    url text,
    tags text[] DEFAULT '{}'::text[],
    starred boolean DEFAULT false,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    abstract text,
    citation_count integer,
    reference_count integer,
    publication_date date,
    journal character varying(500),
    volume character varying(50),
    issue character varying(50),
    pages character varying(50),
    user_id uuid NOT NULL,
    zotero_item_key character varying(255),
    zotero_note_key character varying(255),
    zotero_last_synced timestamp with time zone,
    zotero_sync_status character varying(50) DEFAULT 'not_synced'::character varying,
    CONSTRAINT papers_zotero_sync_status_check CHECK (((zotero_sync_status)::text = ANY ((ARRAY['not_synced'::character varying, 'synced'::character varying, 'error'::character varying, 'pending'::character varying])::text[])))
);


ALTER TABLE public.papers OWNER TO papernugget;

--
-- Name: password_history; Type: TABLE; Schema: public; Owner: papernugget
--

CREATE TABLE public.password_history (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    user_id uuid NOT NULL,
    password_hash character varying(255) NOT NULL,
    created_at timestamp with time zone DEFAULT now()
);


ALTER TABLE public.password_history OWNER TO papernugget;

--
-- Name: password_reset_tokens; Type: TABLE; Schema: public; Owner: papernugget
--

CREATE TABLE public.password_reset_tokens (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    user_id uuid NOT NULL,
    token_hash character varying(255) NOT NULL,
    expires_at timestamp with time zone NOT NULL,
    used boolean DEFAULT false,
    created_at timestamp with time zone DEFAULT now()
);


ALTER TABLE public.password_reset_tokens OWNER TO papernugget;

--
-- Name: reviews; Type: TABLE; Schema: public; Owner: papernugget
--

CREATE TABLE public.reviews (
    paper_id character varying(255) NOT NULL,
    ease numeric(3,2) DEFAULT 2.5,
    next_due timestamp with time zone DEFAULT now(),
    last_interval integer DEFAULT 1,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now()
);


ALTER TABLE public.reviews OWNER TO papernugget;

--
-- Name: user_sessions; Type: TABLE; Schema: public; Owner: papernugget
--

CREATE TABLE public.user_sessions (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    user_id uuid NOT NULL,
    token_hash character varying(255) NOT NULL,
    expires_at timestamp with time zone NOT NULL,
    created_at timestamp with time zone DEFAULT now(),
    ip_address inet,
    user_agent text
);


ALTER TABLE public.user_sessions OWNER TO papernugget;

--
-- Name: users; Type: TABLE; Schema: public; Owner: papernugget
--

CREATE TABLE public.users (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    email character varying(255) NOT NULL,
    password_hash character varying(255) NOT NULL,
    display_name character varying(100),
    role character varying(50) DEFAULT 'user'::character varying,
    email_verified boolean DEFAULT false,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    last_login timestamp with time zone,
    is_active boolean DEFAULT true,
    privacy_settings jsonb DEFAULT '{}'::jsonb,
    preferences jsonb DEFAULT '{}'::jsonb,
    CONSTRAINT users_role_check CHECK (((role)::text = ANY ((ARRAY['admin'::character varying, 'user'::character varying, 'readonly'::character varying])::text[])))
);


ALTER TABLE public.users OWNER TO papernugget;

--
-- Name: audit_logs audit_logs_pkey; Type: CONSTRAINT; Schema: public; Owner: papernugget
--

ALTER TABLE ONLY public.audit_logs
    ADD CONSTRAINT audit_logs_pkey PRIMARY KEY (id);


--
-- Name: collections collections_pkey; Type: CONSTRAINT; Schema: public; Owner: papernugget
--

ALTER TABLE ONLY public.collections
    ADD CONSTRAINT collections_pkey PRIMARY KEY (id);


--
-- Name: email_verification_tokens email_verification_tokens_pkey; Type: CONSTRAINT; Schema: public; Owner: papernugget
--

ALTER TABLE ONLY public.email_verification_tokens
    ADD CONSTRAINT email_verification_tokens_pkey PRIMARY KEY (id);


--
-- Name: notes notes_pkey; Type: CONSTRAINT; Schema: public; Owner: papernugget
--

ALTER TABLE ONLY public.notes
    ADD CONSTRAINT notes_pkey PRIMARY KEY (id);


--
-- Name: papers papers_pkey; Type: CONSTRAINT; Schema: public; Owner: papernugget
--

ALTER TABLE ONLY public.papers
    ADD CONSTRAINT papers_pkey PRIMARY KEY (id);


--
-- Name: password_history password_history_pkey; Type: CONSTRAINT; Schema: public; Owner: papernugget
--

ALTER TABLE ONLY public.password_history
    ADD CONSTRAINT password_history_pkey PRIMARY KEY (id);


--
-- Name: password_reset_tokens password_reset_tokens_pkey; Type: CONSTRAINT; Schema: public; Owner: papernugget
--

ALTER TABLE ONLY public.password_reset_tokens
    ADD CONSTRAINT password_reset_tokens_pkey PRIMARY KEY (id);


--
-- Name: reviews reviews_pkey; Type: CONSTRAINT; Schema: public; Owner: papernugget
--

ALTER TABLE ONLY public.reviews
    ADD CONSTRAINT reviews_pkey PRIMARY KEY (paper_id);


--
-- Name: user_sessions user_sessions_pkey; Type: CONSTRAINT; Schema: public; Owner: papernugget
--

ALTER TABLE ONLY public.user_sessions
    ADD CONSTRAINT user_sessions_pkey PRIMARY KEY (id);


--
-- Name: users users_email_key; Type: CONSTRAINT; Schema: public; Owner: papernugget
--

ALTER TABLE ONLY public.users
    ADD CONSTRAINT users_email_key UNIQUE (email);


--
-- Name: users users_pkey; Type: CONSTRAINT; Schema: public; Owner: papernugget
--

ALTER TABLE ONLY public.users
    ADD CONSTRAINT users_pkey PRIMARY KEY (id);


--
-- Name: idx_audit_logs_action; Type: INDEX; Schema: public; Owner: papernugget
--

CREATE INDEX idx_audit_logs_action ON public.audit_logs USING btree (action);


--
-- Name: idx_audit_logs_created_at; Type: INDEX; Schema: public; Owner: papernugget
--

CREATE INDEX idx_audit_logs_created_at ON public.audit_logs USING btree (created_at);


--
-- Name: idx_audit_logs_user_id; Type: INDEX; Schema: public; Owner: papernugget
--

CREATE INDEX idx_audit_logs_user_id ON public.audit_logs USING btree (user_id);


--
-- Name: idx_collections_created_at; Type: INDEX; Schema: public; Owner: papernugget
--

CREATE INDEX idx_collections_created_at ON public.collections USING btree (created_at);


--
-- Name: idx_collections_name; Type: INDEX; Schema: public; Owner: papernugget
--

CREATE INDEX idx_collections_name ON public.collections USING btree (name);


--
-- Name: idx_collections_updated_at; Type: INDEX; Schema: public; Owner: papernugget
--

CREATE INDEX idx_collections_updated_at ON public.collections USING btree (updated_at);


--
-- Name: idx_collections_user_id; Type: INDEX; Schema: public; Owner: papernugget
--

CREATE INDEX idx_collections_user_id ON public.collections USING btree (user_id);


--
-- Name: idx_collections_zotero_destination; Type: INDEX; Schema: public; Owner: papernugget
--

CREATE INDEX idx_collections_zotero_destination ON public.collections USING btree (zotero_library_type, zotero_library_id) WHERE (zotero_library_type IS NOT NULL);


--
-- Name: idx_email_verification_tokens_expires_at; Type: INDEX; Schema: public; Owner: papernugget
--

CREATE INDEX idx_email_verification_tokens_expires_at ON public.email_verification_tokens USING btree (expires_at);


--
-- Name: idx_email_verification_tokens_token_hash; Type: INDEX; Schema: public; Owner: papernugget
--

CREATE INDEX idx_email_verification_tokens_token_hash ON public.email_verification_tokens USING btree (token_hash);


--
-- Name: idx_email_verification_tokens_user_id; Type: INDEX; Schema: public; Owner: papernugget
--

CREATE INDEX idx_email_verification_tokens_user_id ON public.email_verification_tokens USING btree (user_id);


--
-- Name: idx_notes_created_at; Type: INDEX; Schema: public; Owner: papernugget
--

CREATE INDEX idx_notes_created_at ON public.notes USING btree (created_at);


--
-- Name: idx_notes_paper_id; Type: INDEX; Schema: public; Owner: papernugget
--

CREATE INDEX idx_notes_paper_id ON public.notes USING btree (paper_id);


--
-- Name: idx_notes_updated_at; Type: INDEX; Schema: public; Owner: papernugget
--

CREATE INDEX idx_notes_updated_at ON public.notes USING btree (updated_at);


--
-- Name: idx_papers_created_at; Type: INDEX; Schema: public; Owner: papernugget
--

CREATE INDEX idx_papers_created_at ON public.papers USING btree (created_at);


--
-- Name: idx_papers_starred; Type: INDEX; Schema: public; Owner: papernugget
--

CREATE INDEX idx_papers_starred ON public.papers USING btree (starred);


--
-- Name: idx_papers_tags; Type: INDEX; Schema: public; Owner: papernugget
--

CREATE INDEX idx_papers_tags ON public.papers USING gin (tags);


--
-- Name: idx_papers_user_id; Type: INDEX; Schema: public; Owner: papernugget
--

CREATE INDEX idx_papers_user_id ON public.papers USING btree (user_id);


--
-- Name: idx_papers_year; Type: INDEX; Schema: public; Owner: papernugget
--

CREATE INDEX idx_papers_year ON public.papers USING btree (year);


--
-- Name: idx_papers_zotero_item_key; Type: INDEX; Schema: public; Owner: papernugget
--

CREATE INDEX idx_papers_zotero_item_key ON public.papers USING btree (zotero_item_key);


--
-- Name: idx_papers_zotero_last_synced; Type: INDEX; Schema: public; Owner: papernugget
--

CREATE INDEX idx_papers_zotero_last_synced ON public.papers USING btree (zotero_last_synced);


--
-- Name: idx_papers_zotero_sync_status; Type: INDEX; Schema: public; Owner: papernugget
--

CREATE INDEX idx_papers_zotero_sync_status ON public.papers USING btree (zotero_sync_status);


--
-- Name: idx_password_history_created_at; Type: INDEX; Schema: public; Owner: papernugget
--

CREATE INDEX idx_password_history_created_at ON public.password_history USING btree (created_at);


--
-- Name: idx_password_history_user_id; Type: INDEX; Schema: public; Owner: papernugget
--

CREATE INDEX idx_password_history_user_id ON public.password_history USING btree (user_id);


--
-- Name: idx_password_reset_tokens_expires_at; Type: INDEX; Schema: public; Owner: papernugget
--

CREATE INDEX idx_password_reset_tokens_expires_at ON public.password_reset_tokens USING btree (expires_at);


--
-- Name: idx_password_reset_tokens_token_hash; Type: INDEX; Schema: public; Owner: papernugget
--

CREATE INDEX idx_password_reset_tokens_token_hash ON public.password_reset_tokens USING btree (token_hash);


--
-- Name: idx_password_reset_tokens_user_id; Type: INDEX; Schema: public; Owner: papernugget
--

CREATE INDEX idx_password_reset_tokens_user_id ON public.password_reset_tokens USING btree (user_id);


--
-- Name: idx_reviews_next_due; Type: INDEX; Schema: public; Owner: papernugget
--

CREATE INDEX idx_reviews_next_due ON public.reviews USING btree (next_due);


--
-- Name: idx_user_sessions_expires_at; Type: INDEX; Schema: public; Owner: papernugget
--

CREATE INDEX idx_user_sessions_expires_at ON public.user_sessions USING btree (expires_at);


--
-- Name: idx_user_sessions_token_hash; Type: INDEX; Schema: public; Owner: papernugget
--

CREATE INDEX idx_user_sessions_token_hash ON public.user_sessions USING btree (token_hash);


--
-- Name: idx_user_sessions_user_id; Type: INDEX; Schema: public; Owner: papernugget
--

CREATE INDEX idx_user_sessions_user_id ON public.user_sessions USING btree (user_id);


--
-- Name: idx_users_created_at; Type: INDEX; Schema: public; Owner: papernugget
--

CREATE INDEX idx_users_created_at ON public.users USING btree (created_at);


--
-- Name: idx_users_email; Type: INDEX; Schema: public; Owner: papernugget
--

CREATE INDEX idx_users_email ON public.users USING btree (email);


--
-- Name: idx_users_is_active; Type: INDEX; Schema: public; Owner: papernugget
--

CREATE INDEX idx_users_is_active ON public.users USING btree (is_active);


--
-- Name: idx_users_role; Type: INDEX; Schema: public; Owner: papernugget
--

CREATE INDEX idx_users_role ON public.users USING btree (role);


--
-- Name: collections update_collections_updated_at; Type: TRIGGER; Schema: public; Owner: papernugget
--

CREATE TRIGGER update_collections_updated_at BEFORE UPDATE ON public.collections FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();


--
-- Name: notes update_notes_updated_at; Type: TRIGGER; Schema: public; Owner: papernugget
--

CREATE TRIGGER update_notes_updated_at BEFORE UPDATE ON public.notes FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();


--
-- Name: papers update_papers_updated_at; Type: TRIGGER; Schema: public; Owner: papernugget
--

CREATE TRIGGER update_papers_updated_at BEFORE UPDATE ON public.papers FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();


--
-- Name: users update_users_updated_at; Type: TRIGGER; Schema: public; Owner: papernugget
--

CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON public.users FOR EACH ROW EXECUTE FUNCTION public.update_user_updated_at_column();


--
-- Name: audit_logs audit_logs_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: papernugget
--

ALTER TABLE ONLY public.audit_logs
    ADD CONSTRAINT audit_logs_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id) ON DELETE SET NULL;


--
-- Name: collections collections_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: papernugget
--

ALTER TABLE ONLY public.collections
    ADD CONSTRAINT collections_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id) ON DELETE CASCADE;


--
-- Name: email_verification_tokens email_verification_tokens_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: papernugget
--

ALTER TABLE ONLY public.email_verification_tokens
    ADD CONSTRAINT email_verification_tokens_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id) ON DELETE CASCADE;


--
-- Name: notes notes_paper_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: papernugget
--

ALTER TABLE ONLY public.notes
    ADD CONSTRAINT notes_paper_id_fkey FOREIGN KEY (paper_id) REFERENCES public.papers(id) ON DELETE CASCADE;


--
-- Name: papers papers_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: papernugget
--

ALTER TABLE ONLY public.papers
    ADD CONSTRAINT papers_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id) ON DELETE CASCADE;


--
-- Name: password_history password_history_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: papernugget
--

ALTER TABLE ONLY public.password_history
    ADD CONSTRAINT password_history_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id) ON DELETE CASCADE;


--
-- Name: password_reset_tokens password_reset_tokens_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: papernugget
--

ALTER TABLE ONLY public.password_reset_tokens
    ADD CONSTRAINT password_reset_tokens_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id) ON DELETE CASCADE;


--
-- Name: reviews reviews_paper_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: papernugget
--

ALTER TABLE ONLY public.reviews
    ADD CONSTRAINT reviews_paper_id_fkey FOREIGN KEY (paper_id) REFERENCES public.papers(id) ON DELETE CASCADE;


--
-- Name: user_sessions user_sessions_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: papernugget
--

ALTER TABLE ONLY public.user_sessions
    ADD CONSTRAINT user_sessions_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id) ON DELETE CASCADE;


--
-- PostgreSQL database dump complete
--

