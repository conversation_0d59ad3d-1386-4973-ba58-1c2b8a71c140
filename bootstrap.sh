#!/bin/bash

# PaperNugget Bootstrap Script
# One-command setup that starts Dock<PERSON>, applies schema, seeds data, and runs the app

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

log_step() {
    echo -e "${BLUE}🔄 $1${NC}"
}

# Function to check if a command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to wait for service to be ready
wait_for_service() {
    local service_name=$1
    local check_command=$2
    local max_attempts=30
    local attempt=1

    log_step "Waiting for $service_name to be ready..."
    
    while [ $attempt -le $max_attempts ]; do
        if eval "$check_command" >/dev/null 2>&1; then
            log_success "$service_name is ready!"
            return 0
        fi
        
        echo -n "."
        sleep 2
        attempt=$((attempt + 1))
    done
    
    log_error "$service_name failed to start within expected time"
    return 1
}

# Function to check Docker health
check_docker_health() {
    if ! command_exists docker; then
        log_error "Docker is not installed. Please install Docker first."
        echo "Visit: https://docs.docker.com/get-docker/"
        exit 1
    fi

    if ! docker info >/dev/null 2>&1; then
        log_error "Docker daemon is not running. Please start Docker."
        exit 1
    fi

    if ! command_exists docker-compose && ! docker compose version >/dev/null 2>&1; then
        log_error "Docker Compose is not available. Please install Docker Compose."
        echo "Visit: https://docs.docker.com/compose/install/"
        exit 1
    fi

    log_success "Docker and Docker Compose are available"
}

# Function to setup environment
setup_environment() {
    log_step "Setting up environment configuration..."
    
    if [ ! -f .env ]; then
        if [ -f .env.example ]; then
            cp .env.example .env
            log_success "Created .env file from template"
        else
            log_warning ".env.example not found, creating basic .env file"
            cat > .env << EOF
# Database Configuration
DATABASE_URL=postgresql://papernugget:password@localhost:5432/papernugget

# Application Configuration
NODE_ENV=development
PORT=3000
APP_URL=http://localhost:3000

# Security (change these in production)
POSTGRES_DB=papernugget
POSTGRES_USER=papernugget
POSTGRES_PASSWORD=password

# Email Configuration (Required for email verification)
EMAIL_FROM=<EMAIL>
SMTP_HOST=localhost
SMTP_PORT=1025
SMTP_USER=
SMTP_PASS=
SMTP_TLS=false
EOF
            log_success "Created basic .env file"
        fi
    else
        log_success ".env file already exists"
    fi
}

# Function to clean up previous containers
cleanup_previous() {
    log_step "Cleaning up any previous containers..."
    
    # Stop and remove containers if they exist
    if docker compose ps -q >/dev/null 2>&1; then
        docker compose down -v --remove-orphans >/dev/null 2>&1 || true
    fi
    
    # Remove any orphaned containers
    docker container prune -f >/dev/null 2>&1 || true
    
    log_success "Cleanup completed"
}

# Function to start services
start_services() {
    log_step "Starting Docker services..."
    
    # Build and start services
    docker compose up --build -d
    
    # Wait for database to be ready
    wait_for_service "PostgreSQL database" "docker compose exec -T db pg_isready -U papernugget -d papernugget"
    
    # Wait for application to be ready
    wait_for_service "PaperNugget application" "curl -f http://localhost:3000/api/health"
    
    log_success "All services are running"
}

# Function to run database migrations
run_migrations() {
    log_step "Running database migrations..."
    
    # Run migrations inside the app container
    docker compose exec -T app npm run migrate
    
    log_success "Database migrations completed"
}

# Function to seed test data
seed_data() {
    log_step "Seeding test data..."
    
    # Seed test users
    docker compose exec -T app npm run seed:test-users
    
    log_success "Test data seeded"
}

# Function to verify installation
verify_installation() {
    log_step "Verifying installation..."

    # Run comprehensive health check
    if docker compose exec -T app npm run health >/dev/null 2>&1; then
        log_success "Comprehensive health check passed"
    else
        log_warning "Health check had issues, running detailed check..."

        # Run detailed health check and show results
        docker compose exec -T app npm run health:detailed

        # Still continue - the detailed output will show what's wrong
        log_info "Detailed health check completed (see output above)"
    fi

    # Test email system
    log_step "Testing email system..."
    if docker compose exec -T app npm run email:test >/dev/null 2>&1; then
        log_success "Email system test passed"
    else
        log_warning "Email system test had issues, but continuing..."
        log_info "You can test email manually with: docker compose exec app npm run email:test"
    fi

    log_success "Installation verification completed"
}

# Function to display final information
show_final_info() {
    echo ""
    echo "🎉 PaperNugget Bootstrap Complete!"
    echo ""
    echo "📍 Access Points:"
    echo "   🌐 Main Application: http://localhost:3000"
    echo "   📧 Email Testing (Mailpit): http://localhost:8025"
    echo "   🗄️  Database: localhost:5432"
    echo ""
    echo "👤 Default Admin Account:"
    echo "   📧 Email: <EMAIL>"
    echo "   🔑 Password: admin123"
    echo "   ⚠️  Please change this password immediately!"
    echo ""
    echo "🛠️  Useful Commands:"
    echo "   📊 View logs: docker compose logs -f"
    echo "   🛑 Stop services: docker compose down"
    echo "   🔄 Restart: docker compose restart"
    echo "   🧹 Clean reset: docker compose down -v && ./bootstrap.sh"
    echo ""
    echo "📚 Documentation: README.md"
    echo ""
}

# Main bootstrap function
main() {
    echo "🚀 PaperNugget Bootstrap Script"
    echo "================================"
    echo ""
    
    # Check prerequisites
    check_docker_health
    
    # Setup environment
    setup_environment
    
    # Clean up any previous installation
    cleanup_previous
    
    # Start services
    start_services
    
    # Run database migrations
    run_migrations
    
    # Seed test data
    seed_data
    
    # Verify installation
    verify_installation
    
    # Show final information
    show_final_info
    
    log_success "Bootstrap completed successfully!"
}

# Handle script interruption
trap 'log_error "Bootstrap interrupted"; exit 1' INT TERM

# Run main function
main "$@"
