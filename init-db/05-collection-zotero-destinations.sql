-- Migration: Add Zotero destination fields to collections table
-- This allows each collection to have its own Zotero sync destination

-- Add Zotero destination fields to collections table
ALTER TABLE collections 
ADD COLUMN IF NOT EXISTS zotero_library_type VARCHAR(10) CHECK (zotero_library_type IN ('user', 'group')),
ADD COLUMN IF NOT EXISTS zotero_library_id VARCHAR(255);

-- Add index for better query performance when filtering by Zotero settings
CREATE INDEX IF NOT EXISTS idx_collections_zotero_destination 
ON collections(zotero_library_type, zotero_library_id) 
WHERE zotero_library_type IS NOT NULL;

-- Add comments for documentation
COMMENT ON COLUMN collections.zotero_library_type IS 'Type of Zotero library for this collection: user (My Library) or group';
COMMENT ON COLUMN collections.zotero_library_id IS 'ID of the Zotero library/group. NULL for user library, group ID for group libraries';

-- Note: Existing collections will have NULL values for these fields,
-- which means they will use the global Zotero settings from user preferences
-- This ensures backward compatibility
