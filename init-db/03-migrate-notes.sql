-- Migration script to update notes table structure
-- This script migrates from the old structure (bullets, why_it_matters, figure_refs)
-- to the new structure (quick_summary, key_ideas)

-- First, add the new columns if they don't exist
DO $$ 
BEGIN
    -- Add quick_summary column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'notes' AND column_name = 'quick_summary') THEN
        ALTER TABLE notes ADD COLUMN quick_summary TEXT;
    END IF;
    
    -- Add key_ideas column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'notes' AND column_name = 'key_ideas') THEN
        ALTER TABLE notes ADD COLUMN key_ideas TEXT[] DEFAULT '{}';
    END IF;
END $$;

-- Migrate existing data
UPDATE notes SET 
    quick_summary = why_it_matters,
    key_ideas = CASE 
        WHEN array_length(bullets, 1) IS NOT NULL THEN 
            bullets[1:3]  -- Take only first 3 bullets as key ideas
        ELSE 
            '{}'::TEXT[]
    END
WHERE quick_summary IS NULL OR key_ideas IS NULL;

-- Drop old columns if they exist
DO $$ 
BEGIN
    -- Drop bullets column
    IF EXISTS (SELECT 1 FROM information_schema.columns 
               WHERE table_name = 'notes' AND column_name = 'bullets') THEN
        ALTER TABLE notes DROP COLUMN bullets;
    END IF;
    
    -- Drop why_it_matters column
    IF EXISTS (SELECT 1 FROM information_schema.columns 
               WHERE table_name = 'notes' AND column_name = 'why_it_matters') THEN
        ALTER TABLE notes DROP COLUMN why_it_matters;
    END IF;
    
    -- Drop figure_refs column
    IF EXISTS (SELECT 1 FROM information_schema.columns 
               WHERE table_name = 'notes' AND column_name = 'figure_refs') THEN
        ALTER TABLE notes DROP COLUMN figure_refs;
    END IF;
END $$;
