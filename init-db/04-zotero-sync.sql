-- Migration: Add Zotero sync fields to papers table
-- This migration adds the necessary fields for Zotero synchronization

-- Add Zotero sync fields to papers table
ALTER TABLE papers 
ADD COLUMN IF NOT EXISTS zotero_item_key VARCHAR(255),
ADD COLUMN IF NOT EXISTS zotero_note_key VARCHAR(255),
ADD COLUMN IF NOT EXISTS zotero_last_synced TIMESTAMP WITH TIME ZONE,
ADD COLUMN IF NOT EXISTS zotero_sync_status VARCHAR(50) DEFAULT 'not_synced';

-- Add constraint for zotero_sync_status
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_constraint 
        WHERE conname = 'papers_zotero_sync_status_check'
    ) THEN
        ALTER TABLE papers 
        ADD CONSTRAINT papers_zotero_sync_status_check 
        CHECK (zotero_sync_status IN ('not_synced', 'synced', 'error', 'pending'));
    END IF;
END $$;

-- Create indexes for Zotero fields
CREATE INDEX IF NOT EXISTS idx_papers_zotero_item_key ON papers(zotero_item_key);
CREATE INDEX IF NOT EXISTS idx_papers_zotero_sync_status ON papers(zotero_sync_status);
CREATE INDEX IF NOT EXISTS idx_papers_zotero_last_synced ON papers(zotero_last_synced);

-- Update existing papers to have 'not_synced' status
UPDATE papers 
SET zotero_sync_status = 'not_synced' 
WHERE zotero_sync_status IS NULL;
