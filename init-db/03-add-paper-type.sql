-- Migration: Add paper_type field to papers table
-- This allows users to specify the type of paper for better Zotero integration

-- Add paper_type column with default value
ALTER TABLE papers 
ADD COLUMN paper_type VARCHAR(50) DEFAULT 'journalArticle' 
CHECK (paper_type IN (
    'journalArticle',
    'conferencePaper', 
    'book',
    'bookSection',
    'thesis',
    'report',
    'preprint',
    'manuscript',
    'webpage',
    'blogPost',
    'presentation',
    'patent',
    'dataset',
    'software',
    'artwork',
    'audioRecording',
    'videoRecording',
    'interview',
    'letter',
    'email',
    'forumPost',
    'encyclopediaArticle',
    'dictionaryEntry',
    'magazineArticle',
    'newspaperArticle',
    'radioBroadcast',
    'tvBroadcast',
    'podcast',
    'case',
    'statute',
    'bill',
    'hearing',
    'standard',
    'map',
    'film',
    'document'
));

-- Add index for paper_type for better query performance
CREATE INDEX IF NOT EXISTS idx_papers_paper_type ON papers(paper_type);

-- Update existing papers to have appropriate paper types based on venue
-- This is a best-effort migration based on common venue patterns
UPDATE papers SET paper_type = 'conferencePaper' 
WHERE venue IS NOT NULL AND (
    venue ILIKE '%conference%' OR
    venue ILIKE '%symposium%' OR
    venue ILIKE '%workshop%' OR
    venue ILIKE '%proceedings%' OR
    venue = 'NeurIPS' OR
    venue = 'ICML' OR
    venue = 'ICLR' OR
    venue = 'AAAI' OR
    venue = 'IJCAI' OR
    venue = 'CVPR' OR
    venue = 'ICCV' OR
    venue = 'ECCV' OR
    venue = 'ACL' OR
    venue = 'EMNLP' OR
    venue = 'NAACL' OR
    venue = 'SIGIR' OR
    venue = 'WWW' OR
    venue = 'CHI' OR
    venue = 'UIST' OR
    venue = 'CSCW'
);

-- Papers with journal-like venues remain as journalArticle (default)
-- Papers with arXiv DOIs could be preprints, but we'll keep them as journalArticle for now
-- Users can manually change the type if needed
