-- Seed data for PaperNugget
-- This file provides sample data for development and testing

-- Insert sample papers (assigned to system user)
INSERT INTO papers (id, title, authors, venue, year, doi, abstract, tags, starred, user_id, created_at, updated_at) VALUES
(
    '1',
    'Attention Is All You Need',
    ARRAY['<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>'],
    'NeurIPS',
    2017,
    '10.48550/arXiv.1706.03762',
    'The dominant sequence transduction models are based on complex recurrent or convolutional neural networks that include an encoder and a decoder. The best performing models also connect the encoder and decoder through an attention mechanism. We propose a new simple network architecture, the Transformer, based solely on attention mechanisms, dispensing with recurrence and convolutions entirely.',
    ARRAY['transformers', 'attention', 'nlp', 'neural-networks'],
    true,
    '00000000-0000-0000-0000-000000000000',
    '2024-01-01T00:00:00Z',
    '2024-01-01T00:00:00Z'
),
(
    '2',
    'BERT: Pre-training of Deep Bidirectional Transformers for Language Understanding',
    ARRAY['<PERSON>', '<PERSON>-<PERSON> <PERSON>', 'Kenton Lee', 'Kristina Toutanova'],
    'NAACL',
    2019,
    '10.18653/v1/N19-1423',
    'We introduce a new language representation model called BERT, which stands for Bidirectional Encoder Representations from Transformers. Unlike recent language representation models, BERT is designed to pre-train deep bidirectional representations from unlabeled text by jointly conditioning on both left and right context in all layers.',
    ARRAY['bert', 'transformers', 'nlp', 'pretraining', 'bidirectional'],
    false,
    '00000000-0000-0000-0000-000000000000',
    '2024-01-02T00:00:00Z',
    '2024-01-02T00:00:00Z'
),
(
    '3',
    'Language Models are Few-Shot Learners',
    ARRAY['Tom B. Brown', 'Benjamin Mann', 'Nick Ryder', 'Melanie Subbiah', 'Jared Kaplan', 'Prafulla Dhariwal', 'Arvind Neelakantan', 'Pranav Shyam', 'Girish Sastry', 'Amanda Askell'],
    'NeurIPS',
    2020,
    '10.48550/arXiv.2005.14165',
    'Recent work has demonstrated substantial gains on many NLP tasks and benchmarks by pre-training on a large corpus of text followed by fine-tuning on a specific task. While typically task-agnostic in architecture, this method still requires task-specific fine-tuning datasets of thousands or tens of thousands of examples. By contrast, humans can generally perform a new language task from only a few examples or from simple instructions.',
    ARRAY['gpt', 'language-models', 'few-shot', 'nlp', 'large-language-models'],
    true,
    '00000000-0000-0000-0000-000000000000',
    '2024-01-03T00:00:00Z',
    '2024-01-03T00:00:00Z'
);

-- Insert sample notes
INSERT INTO notes (id, paper_id, quick_summary, key_ideas, created_at, updated_at) VALUES
(
    'note-1',
    '1',
    'This paper revolutionized NLP by showing that attention mechanisms alone are sufficient for sequence modeling, leading to the development of BERT, GPT, and other transformer-based models.',
    ARRAY[
        'Introduces the Transformer architecture based solely on attention mechanisms',
        'Eliminates recurrence and convolutions entirely, achieving state-of-the-art results on machine translation tasks',
        'Multi-head attention allows the model to attend to different positions and is significantly faster to train than RNN-based models'
    ],
    '2024-01-01T00:00:00Z',
    '2024-01-01T00:00:00Z'
),
(
    'note-2',
    '2',
    'BERT showed that bidirectional pre-training significantly improves performance and established the pre-train then fine-tune paradigm that dominates modern NLP.',
    ARRAY[
        'Introduces bidirectional training of transformers using masked language modeling (MLM) and next sentence prediction (NSP)',
        'Achieves state-of-the-art on 11 NLP tasks, showing the importance of bidirectional context for language understanding',
        'Demonstrates transfer learning effectiveness through pre-training on large unlabeled corpus followed by task-specific fine-tuning'
    ],
    '2024-01-02T00:00:00Z',
    '2024-01-02T00:00:00Z'
),
(
    'note-3',
    '3',
    'GPT-3 demonstrated that large language models can perform many tasks through few-shot learning alone, without task-specific training, opening up new possibilities for general-purpose AI systems.',
    ARRAY[
        'Demonstrates few-shot learning capabilities without fine-tuning by scaling language models to 175 billion parameters',
        'Shows emergent abilities that appear with scale, enabling in-context learning through prompt engineering',
        'Achieves strong performance across diverse tasks with minimal examples, raising questions about the nature of intelligence and learning'
    ],
    '2024-01-03T00:00:00Z',
    '2024-01-03T00:00:00Z'
);

-- Insert sample collections (assigned to system user)
INSERT INTO collections (id, name, paper_ids, user_id, created_at, updated_at) VALUES
(
    'col-1',
    'Transformer Papers',
    ARRAY['1', '2'],
    '00000000-0000-0000-0000-000000000000',
    '2024-01-01T00:00:00Z',
    '2024-01-01T00:00:00Z'
),
(
    'col-2',
    'Foundation Models',
    ARRAY['2', '3'],
    '00000000-0000-0000-0000-000000000000',
    '2024-01-02T00:00:00Z',
    '2024-01-02T00:00:00Z'
),
(
    'col-3',
    'Must-Read NLP Papers',
    ARRAY['1', '2', '3'],
    '00000000-0000-0000-0000-000000000000',
    '2024-01-03T00:00:00Z',
    '2024-01-03T00:00:00Z'
);

-- Insert sample reviews (spaced repetition data)
INSERT INTO reviews (paper_id, ease, next_due, last_interval) VALUES
(
    '1',
    2.5,
    NOW() - INTERVAL '1 day',
    1
),
(
    '2',
    2.0,
    NOW() + INTERVAL '2 days',
    6
),
(
    '3',
    3.0,
    NOW() + INTERVAL '5 days',
    15
);
