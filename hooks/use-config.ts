"use client"

import { useState, useEffect } from 'react'
import { useLoggedFetch } from './use-logged-fetch'

interface PublicConfig {
  kofiUrl: string | null
}

interface UseConfigReturn {
  config: PublicConfig | null
  loading: boolean
  error: string | null
}

/**
 * Hook to fetch public configuration from the API with comprehensive logging
 */
export function useConfig(): UseConfigReturn {
  const [config, setConfig] = useState<PublicConfig | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const { fetch: loggedFetch } = useLoggedFetch('useConfig')

  useEffect(() => {
    async function fetchConfig() {
      try {
        setLoading(true)
        setError(null)

        const response = await loggedFetch('/api/config', {}, 'fetch_public_config')

        if (!response.ok) {
          throw new Error(`Failed to fetch configuration: ${response.status}`)
        }

        const data = await response.json()

        // Handle both direct data and wrapped response formats
        const configData = data.data || data
        setConfig(configData)
      } catch (err) {
        // Error is already logged by loggedFetch, just handle the UI state
        setError(err instanceof Error ? err.message : 'Failed to fetch configuration')
        // Set default config on error to prevent blocking the UI
        setConfig({ kofiUrl: null })
      } finally {
        setLoading(false)
      }
    }

    fetchConfig()
  }, [loggedFetch])

  return { config, loading, error }
}
