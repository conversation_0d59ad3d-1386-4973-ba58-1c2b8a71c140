"use client"

import { useCallback } from 'react'
import { loggedFetch, authenticatedFetch } from '@/lib/utils'

/**
 * Hook for making logged network requests with component context
 */
export function useLoggedFetch(componentName?: string) {
  const makeRequest = useCallback(async (
    url: string | URL,
    options: RequestInit = {},
    action?: string
  ): Promise<Response> => {
    const context = {
      component: componentName || 'unknown',
      action: action || 'fetch'
    }
    
    return loggedFetch(url, options, context)
  }, [componentName])

  const makeAuthenticatedRequest = useCallback(async (
    url: string | URL,
    options: RequestInit = {},
    action?: string
  ): Promise<Response> => {
    const context = {
      component: componentName || 'unknown',
      action: action || 'authenticated_fetch'
    }
    
    return authenticatedFetch(url, options, context)
  }, [componentName])

  return {
    fetch: makeRequest,
    authenticatedFetch: makeAuthenticatedRequest
  }
}

/**
 * Enhanced hook with common error handling patterns
 */
export function useApiRequest(componentName?: string) {
  const { fetch: loggedFetch, authenticatedFetch } = useLoggedFetch(componentName)

  const handleRequest = useCallback(async <T>(
    requestFn: () => Promise<Response>,
    options: {
      onSuccess?: (data: T) => void
      onError?: (error: Error) => void
      showToast?: boolean
      errorMessage?: string
    } = {}
  ): Promise<{ data?: T; error?: Error; success: boolean }> => {
    try {
      const response = await requestFn()
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        const errorMessage = errorData.error || errorData.message || `HTTP ${response.status}: ${response.statusText}`
        const error = new Error(errorMessage)
        
        if (options.onError) {
          options.onError(error)
        }
        
        return { error, success: false }
      }

      const data = await response.json()
      
      if (options.onSuccess) {
        options.onSuccess(data)
      }
      
      return { data, success: true }
    } catch (error: any) {
      if (options.onError) {
        options.onError(error)
      }
      
      return { error, success: false }
    }
  }, [])

  const get = useCallback(async <T>(
    url: string,
    options: {
      authenticated?: boolean
      onSuccess?: (data: T) => void
      onError?: (error: Error) => void
    } = {}
  ) => {
    const fetchFn = options.authenticated ? authenticatedFetch : loggedFetch
    return handleRequest<T>(
      () => fetchFn(url, { method: 'GET' }, 'get'),
      options
    )
  }, [loggedFetch, authenticatedFetch, handleRequest])

  const post = useCallback(async <T>(
    url: string,
    data?: any,
    options: {
      authenticated?: boolean
      onSuccess?: (data: T) => void
      onError?: (error: Error) => void
    } = {}
  ) => {
    const fetchFn = options.authenticated ? authenticatedFetch : loggedFetch
    return handleRequest<T>(
      () => fetchFn(url, {
        method: 'POST',
        body: data ? JSON.stringify(data) : undefined
      }, 'post'),
      options
    )
  }, [loggedFetch, authenticatedFetch, handleRequest])

  const put = useCallback(async <T>(
    url: string,
    data?: any,
    options: {
      authenticated?: boolean
      onSuccess?: (data: T) => void
      onError?: (error: Error) => void
    } = {}
  ) => {
    const fetchFn = options.authenticated ? authenticatedFetch : loggedFetch
    return handleRequest<T>(
      () => fetchFn(url, {
        method: 'PUT',
        body: data ? JSON.stringify(data) : undefined
      }, 'put'),
      options
    )
  }, [loggedFetch, authenticatedFetch, handleRequest])

  const del = useCallback(async <T>(
    url: string,
    options: {
      authenticated?: boolean
      onSuccess?: (data: T) => void
      onError?: (error: Error) => void
    } = {}
  ) => {
    const fetchFn = options.authenticated ? authenticatedFetch : loggedFetch
    return handleRequest<T>(
      () => fetchFn(url, { method: 'DELETE' }, 'delete'),
      options
    )
  }, [loggedFetch, authenticatedFetch, handleRequest])

  return {
    request: handleRequest,
    get,
    post,
    put,
    delete: del,
    fetch: loggedFetch,
    authenticatedFetch
  }
}
