{
  "compilerOptions": {
    "lib": ["dom", "dom.iterable", "es2022"],
    "allowJs": true,
    "target": "es2022",
    "skipLibCheck": true,
    "strict": false,
    "noEmit": true,
    "esModuleInterop": true,
    "module": "esnext",
    "moduleResolution": "bundler",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "jsx": "preserve",
    "incremental": true,
    "plugins": [
      {
        "name": "next"
      }
    ],
    "paths": {
      "@/*": ["./*"]
    },
    // Enhanced type checking (relaxed for build)
    "noImplicitAny": false,
    "noImplicitReturns": false,
    "noImplicitThis": false,
    "noUnusedLocals": false,
    "noUnusedParameters": false,
    "exactOptionalPropertyTypes": false,
    "noImplicitOverride": false,
    "noPropertyAccessFromIndexSignature": false,
    "noUncheckedIndexedAccess": false,
    "allowUnusedLabels": true,
    "allowUnreachableCode": true,
    "noFallthroughCasesInSwitch": false,
    "forceConsistentCasingInFileNames": true,
    // Additional strict checks (disabled for build)
    "strictNullChecks": false,
    "strictFunctionTypes": false,
    "strictBindCallApply": false,
    "strictPropertyInitialization": false,
    "alwaysStrict": false,
    // Module resolution
    "baseUrl": ".",
    "declaration": false,
    "declarationMap": false,
    "sourceMap": false,
    "removeComments": false,
    "importHelpers": true,
    "downlevelIteration": true,
    "useDefineForClassFields": true
  },
  "include": [
    "next-env.d.ts",
    "**/*.ts",
    "**/*.tsx",
    ".next/types/**/*.ts",
    "__tests__/**/*.ts",
    "__tests__/**/*.tsx",
    "jest.config.js",
    "jest.setup.js",
    "jest.env.js"
  ],
  "exclude": [
    "node_modules",
    ".next",
    "out",
    "build",
    "dist",
    "coverage",
    "__tests__",
    "tests",
    "**/*.test.ts",
    "**/*.test.tsx",
    "**/*.spec.ts",
    "**/*.spec.tsx"
  ],
  "ts-node": {
    "compilerOptions": {
      "module": "commonjs"
    }
  }
}
