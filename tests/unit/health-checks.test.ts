/**
 * Unit tests for health check and startup validation systems
 * Run with: node --test tests/unit/health-checks.test.ts
 */

import { test, describe, before } from 'node:test'
import assert from 'node:assert'
import { runStartupValidation, getSystemHealthSummary } from '../../lib/startup-checks'

describe('Health Check System Tests', () => {
  before(async () => {
    // Ensure environment is set up for testing
    if (!process.env.DATABASE_URL) {
      throw new Error('DATABASE_URL environment variable is required for health check tests')
    }
  })

  describe('Startup Validation', () => {
    test('Should run startup validation without errors', async () => {
      const result = await runStartupValidation()
      
      assert.ok(result, 'Startup validation should return a result')
      assert.ok(Array.isArray(result.checks), 'Result should include checks array')
      assert.ok(Array.isArray(result.errors), 'Result should include errors array')
      assert.ok(Array.isArray(result.warnings), 'Result should include warnings array')
      assert.ok(typeof result.success === 'boolean', 'Result should include success boolean')
    })

    test('Should validate environment variables', async () => {
      const result = await runStartupValidation()
      
      // Find environment variable checks
      const envChecks = result.checks.filter(check => 
        check.name.startsWith('Environment Variable:')
      )
      
      assert.ok(envChecks.length > 0, 'Should include environment variable checks')
      
      // Check for required variables
      const requiredVars = ['DATABASE_URL', 'APP_URL', 'EMAIL_FROM', 'SMTP_HOST', 'SMTP_PORT']
      for (const varName of requiredVars) {
        const check = envChecks.find(c => c.name.includes(varName))
        assert.ok(check, `Should check environment variable ${varName}`)
      }
    })

    test('Should validate database connectivity', async () => {
      const result = await runStartupValidation()
      
      const dbConnectivityCheck = result.checks.find(check => 
        check.name === 'Database Connectivity'
      )
      
      assert.ok(dbConnectivityCheck, 'Should include database connectivity check')
      assert.strictEqual(dbConnectivityCheck.status, 'pass', 'Database connectivity should pass')
      assert.ok(dbConnectivityCheck.details?.responseTime, 'Should include response time')
    })

    test('Should validate database schema', async () => {
      const result = await runStartupValidation()
      
      const schemaChecks = result.checks.filter(check => 
        check.name.startsWith('Database Table:')
      )
      
      assert.ok(schemaChecks.length > 0, 'Should include database schema checks')
      
      // Check for required tables
      const requiredTables = ['users', 'papers', 'notes', 'collections', 'reviews']
      for (const tableName of requiredTables) {
        const check = schemaChecks.find(c => c.name.includes(tableName))
        assert.ok(check, `Should check table ${tableName}`)
        assert.strictEqual(check.status, 'pass', `Table ${tableName} should exist`)
      }
    })

    test('Should validate email system', async () => {
      const result = await runStartupValidation()
      
      const smtpCheck = result.checks.find(check => 
        check.name === 'SMTP Connectivity'
      )
      
      assert.ok(smtpCheck, 'Should include SMTP connectivity check')
      // Note: SMTP might fail in test environment, so we just check it exists
      
      const emailConfigCheck = result.checks.find(check => 
        check.name === 'Email Configuration'
      )
      
      assert.ok(emailConfigCheck, 'Should include email configuration check')
      assert.strictEqual(emailConfigCheck.status, 'pass', 'Email configuration should be valid')
    })

    test('Should categorize check results correctly', async () => {
      const result = await runStartupValidation()
      
      const passedChecks = result.checks.filter(c => c.status === 'pass')
      const warningChecks = result.checks.filter(c => c.status === 'warning')
      const failedChecks = result.checks.filter(c => c.status === 'fail')
      
      assert.ok(passedChecks.length > 0, 'Should have some passing checks')
      
      // Verify error and warning arrays match the check statuses
      assert.strictEqual(result.errors.length, failedChecks.length, 'Errors array should match failed checks')
      assert.strictEqual(result.warnings.length, warningChecks.length, 'Warnings array should match warning checks')
    })
  })

  describe('System Health Summary', () => {
    test('Should generate health summary', async () => {
      const summary = await getSystemHealthSummary()
      
      assert.ok(summary, 'Should return a summary object')
      assert.ok(typeof summary.healthy === 'boolean', 'Should include healthy status')
      assert.ok(typeof summary.totalChecks === 'number', 'Should include total checks count')
      assert.ok(typeof summary.passed === 'number', 'Should include passed checks count')
      assert.ok(typeof summary.warnings === 'number', 'Should include warnings count')
      assert.ok(typeof summary.errors === 'number', 'Should include errors count')
      assert.ok(summary.lastChecked, 'Should include timestamp')
    })

    test('Health summary should be consistent with validation results', async () => {
      const validation = await runStartupValidation()
      const summary = await getSystemHealthSummary()
      
      assert.strictEqual(summary.healthy, validation.success, 'Health status should match validation success')
      assert.strictEqual(summary.totalChecks, validation.checks.length, 'Total checks should match')
      assert.strictEqual(summary.errors, validation.errors.length, 'Error count should match')
      assert.strictEqual(summary.warnings, validation.warnings.length, 'Warning count should match')
    })
  })

  describe('Check Result Structure', () => {
    test('Each check should have required properties', async () => {
      const result = await runStartupValidation()
      
      for (const check of result.checks) {
        assert.ok(check.name, 'Check should have a name')
        assert.ok(['pass', 'fail', 'warning'].includes(check.status), 'Check should have valid status')
        assert.ok(check.message, 'Check should have a message')
        
        // Details are optional but should be an object if present
        if (check.details !== undefined) {
          assert.ok(typeof check.details === 'object', 'Check details should be an object')
        }
      }
    })

    test('Failed checks should provide useful error information', async () => {
      const result = await runStartupValidation()
      
      const failedChecks = result.checks.filter(c => c.status === 'fail')
      
      for (const check of failedChecks) {
        assert.ok(check.message.length > 0, 'Failed check should have descriptive message')
        
        // If there are details, they should include error information
        if (check.details && check.details.error) {
          assert.ok(typeof check.details.error === 'string', 'Error details should be a string')
        }
      }
    })
  })

  describe('Performance Characteristics', () => {
    test('Startup validation should complete within reasonable time', async () => {
      const start = Date.now()
      await runStartupValidation()
      const duration = Date.now() - start
      
      // Should complete within 10 seconds in normal conditions
      assert.ok(duration < 10000, `Startup validation should complete quickly (took ${duration}ms)`)
    })

    test('Health summary should be faster than full validation', async () => {
      const validationStart = Date.now()
      await runStartupValidation()
      const validationDuration = Date.now() - validationStart
      
      const summaryStart = Date.now()
      await getSystemHealthSummary()
      const summaryDuration = Date.now() - summaryStart
      
      // Summary should be roughly the same time since it runs full validation
      // This test mainly ensures both functions work
      assert.ok(summaryDuration > 0, 'Health summary should take some time')
      assert.ok(validationDuration > 0, 'Validation should take some time')
    })
  })

  describe('Error Handling', () => {
    test('Should handle database connection failures gracefully', async () => {
      // This test would require mocking database failures
      // For now, we just ensure the validation doesn't throw
      try {
        const result = await runStartupValidation()
        assert.ok(result, 'Should return result even if some checks fail')
      } catch (error) {
        assert.fail('Startup validation should not throw errors')
      }
    })

    test('Should provide meaningful error messages', async () => {
      const result = await runStartupValidation()
      
      for (const error of result.errors) {
        assert.ok(typeof error === 'string', 'Error should be a string')
        assert.ok(error.length > 0, 'Error should not be empty')
        assert.ok(error.includes(':'), 'Error should include context (check name)')
      }
    })
  })
})
