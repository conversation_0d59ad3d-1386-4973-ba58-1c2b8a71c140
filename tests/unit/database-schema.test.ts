/**
 * Unit tests for database schema validation
 * Run with: node --test tests/unit/database-schema.test.ts
 */

import { test, describe, before } from 'node:test'
import assert from 'node:assert'
import { query } from '../../lib/db'

describe('Database Schema Tests', () => {
  before(async () => {
    // Ensure we can connect to the database
    try {
      await query('SELECT 1')
    } catch (error) {
      console.error('Database connection failed:', error)
      throw new Error('Cannot run schema tests without database connection')
    }
  })

  describe('Core Tables Existence', () => {
    const requiredTables = [
      'users',
      'user_sessions',
      'password_reset_tokens',
      'email_verification_tokens',
      'password_history',
      'audit_logs',
      'papers',
      'notes',
      'collections',
      'reviews'
    ]

    for (const tableName of requiredTables) {
      test(`Table ${tableName} should exist`, async () => {
        const result = await query(`
          SELECT EXISTS (
            SELECT FROM information_schema.tables 
            WHERE table_name = $1
          );
        `, [tableName])
        
        assert.strictEqual(result.rows[0].exists, true, `Table ${tableName} should exist`)
      })
    }
  })

  describe('Users Table Schema', () => {
    test('Users table should have correct columns', async () => {
      const result = await query(`
        SELECT column_name, data_type, is_nullable, column_default
        FROM information_schema.columns
        WHERE table_name = 'users'
        ORDER BY ordinal_position;
      `)

      const columns = result.rows.map(row => ({
        name: row.column_name,
        type: row.data_type,
        nullable: row.is_nullable === 'YES',
        default: row.column_default
      }))

      // Check required columns exist
      const requiredColumns = [
        'id', 'email', 'password_hash', 'display_name', 'role',
        'email_verified', 'created_at', 'updated_at', 'last_login',
        'is_active', 'privacy_settings', 'preferences'
      ]

      for (const colName of requiredColumns) {
        const column = columns.find(c => c.name === colName)
        assert.ok(column, `Column ${colName} should exist in users table`)
      }

      // Check specific column constraints
      const emailCol = columns.find(c => c.name === 'email')
      assert.strictEqual(emailCol?.nullable, false, 'Email column should be NOT NULL')

      const roleCol = columns.find(c => c.name === 'role')
      assert.strictEqual(roleCol?.default, "'user'::character varying", 'Role should default to user')
    })

    test('Users table should have unique constraint on email', async () => {
      const result = await query(`
        SELECT constraint_name, constraint_type
        FROM information_schema.table_constraints
        WHERE table_name = 'users' AND constraint_type = 'UNIQUE';
      `)

      const hasEmailUnique = result.rows.some(row => 
        row.constraint_name.includes('email')
      )
      assert.ok(hasEmailUnique, 'Users table should have unique constraint on email')
    })
  })

  describe('Papers Table Schema', () => {
    test('Papers table should have correct columns', async () => {
      const result = await query(`
        SELECT column_name, data_type, is_nullable
        FROM information_schema.columns
        WHERE table_name = 'papers'
        ORDER BY ordinal_position;
      `)

      const columns = result.rows.map(row => row.column_name)

      const requiredColumns = [
        'id', 'title', 'authors', 'venue', 'year', 'doi', 'url',
        'abstract', 'citation_count', 'reference_count', 'publication_date',
        'journal', 'volume', 'issue', 'pages', 'tags', 'starred',
        'user_id', 'created_at', 'updated_at'
      ]

      for (const colName of requiredColumns) {
        assert.ok(columns.includes(colName), `Column ${colName} should exist in papers table`)
      }
    })

    test('Papers table should have foreign key to users', async () => {
      const result = await query(`
        SELECT
          tc.constraint_name,
          kcu.column_name,
          ccu.table_name AS foreign_table_name,
          ccu.column_name AS foreign_column_name
        FROM information_schema.table_constraints AS tc
        JOIN information_schema.key_column_usage AS kcu
          ON tc.constraint_name = kcu.constraint_name
        JOIN information_schema.constraint_column_usage AS ccu
          ON ccu.constraint_name = tc.constraint_name
        WHERE tc.constraint_type = 'FOREIGN KEY'
          AND tc.table_name = 'papers'
          AND kcu.column_name = 'user_id';
      `)

      assert.strictEqual(result.rows.length, 1, 'Papers table should have foreign key on user_id')
      assert.strictEqual(result.rows[0].foreign_table_name, 'users', 'Foreign key should reference users table')
    })
  })

  describe('Authentication Tables Schema', () => {
    test('User sessions table should have correct structure', async () => {
      const result = await query(`
        SELECT column_name
        FROM information_schema.columns
        WHERE table_name = 'user_sessions'
        ORDER BY ordinal_position;
      `)

      const columns = result.rows.map(row => row.column_name)
      const requiredColumns = ['id', 'user_id', 'token_hash', 'expires_at', 'created_at', 'ip_address', 'user_agent']

      for (const colName of requiredColumns) {
        assert.ok(columns.includes(colName), `Column ${colName} should exist in user_sessions table`)
      }
    })

    test('Email verification tokens table should have correct structure', async () => {
      const result = await query(`
        SELECT column_name
        FROM information_schema.columns
        WHERE table_name = 'email_verification_tokens'
        ORDER BY ordinal_position;
      `)

      const columns = result.rows.map(row => row.column_name)
      const requiredColumns = ['id', 'user_id', 'token_hash', 'expires_at', 'used', 'created_at']

      for (const colName of requiredColumns) {
        assert.ok(columns.includes(colName), `Column ${colName} should exist in email_verification_tokens table`)
      }
    })
  })

  describe('Database Indexes', () => {
    test('Critical indexes should exist', async () => {
      const result = await query(`
        SELECT indexname, tablename
        FROM pg_indexes
        WHERE schemaname = 'public'
        ORDER BY tablename, indexname;
      `)

      const indexes = result.rows.map(row => `${row.tablename}.${row.indexname}`)

      // Check for critical indexes
      const criticalIndexes = [
        'users.users_email_key',  // Unique index on email
        'papers.idx_papers_user_id',
        'papers.idx_papers_starred',
        'papers.idx_papers_tags',
        'user_sessions.idx_user_sessions_user_id',
        'user_sessions.idx_user_sessions_token_hash'
      ]

      for (const indexName of criticalIndexes) {
        const exists = indexes.some(idx => idx.includes(indexName.split('.')[1]))
        assert.ok(exists, `Critical index ${indexName} should exist`)
      }
    })
  })

  describe('Database Functions and Triggers', () => {
    test('Update timestamp function should exist', async () => {
      const result = await query(`
        SELECT routine_name
        FROM information_schema.routines
        WHERE routine_name = 'update_updated_at_column'
          AND routine_type = 'FUNCTION';
      `)

      assert.strictEqual(result.rows.length, 1, 'update_updated_at_column function should exist')
    })

    test('Update triggers should exist on tables with updated_at', async () => {
      const result = await query(`
        SELECT trigger_name, event_object_table
        FROM information_schema.triggers
        WHERE trigger_name LIKE '%updated_at%'
        ORDER BY event_object_table;
      `)

      const tablesWithTriggers = result.rows.map(row => row.event_object_table)
      const expectedTables = ['users', 'papers', 'notes', 'collections']

      for (const tableName of expectedTables) {
        assert.ok(
          tablesWithTriggers.includes(tableName),
          `Table ${tableName} should have updated_at trigger`
        )
      }
    })
  })

  describe('Data Integrity', () => {
    test('Default admin user should exist', async () => {
      const result = await query(`
        SELECT id, email, role, email_verified, is_active
        FROM users
        WHERE email = '<EMAIL>';
      `)

      assert.strictEqual(result.rows.length, 1, 'Default admin user should exist')
      
      const admin = result.rows[0]
      assert.strictEqual(admin.role, 'admin', 'Default user should have admin role')
      assert.strictEqual(admin.email_verified, true, 'Default admin should be email verified')
      assert.strictEqual(admin.is_active, true, 'Default admin should be active')
    })

    test('System user should exist for data migration', async () => {
      const result = await query(`
        SELECT id, email, role
        FROM users
        WHERE id = '00000000-0000-0000-0000-000000000000';
      `)

      assert.strictEqual(result.rows.length, 1, 'System user should exist')
      assert.strictEqual(result.rows[0].role, 'admin', 'System user should have admin role')
    })
  })
})
