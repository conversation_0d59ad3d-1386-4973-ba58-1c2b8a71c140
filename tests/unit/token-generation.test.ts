/**
 * Unit tests for token generation and validation
 * Run with: node --test tests/unit/token-generation.test.ts
 */

import { test, describe } from 'node:test'
import assert from 'node:assert'
import { generateSecureToken, generateTokenExpiry, isTokenExpired } from '../../lib/auth'

describe('Token Generation', () => {
  test('generateSecureToken should create tokens of correct length', () => {
    const token = generateSecureToken()
    
    // Should be 64 characters (32 bytes * 2 for hex encoding)
    assert.strictEqual(token.length, 64)
    
    // Should only contain hex characters
    assert.match(token, /^[a-f0-9]+$/)
  })

  test('generateSecureToken should create unique tokens', () => {
    const token1 = generateSecureToken()
    const token2 = generateSecureToken()
    
    assert.notStrictEqual(token1, token2)
  })

  test('generateSecureToken should be URL-safe', () => {
    const token = generateSecureToken()
    
    // Should not contain characters that need URL encoding
    assert.doesNotMatch(token, /[+/=]/)
  })
})

describe('Token Expiry', () => {
  test('generateTokenExpiry should create future dates', () => {
    const expiry = generateTokenExpiry(1) // 1 hour
    const now = new Date()
    
    assert.ok(expiry > now)
  })

  test('generateTokenExpiry should respect hour parameter', () => {
    const expiry1 = generateTokenExpiry(1)
    const expiry24 = generateTokenExpiry(24)
    
    // 24 hour expiry should be later than 1 hour expiry
    assert.ok(expiry24 > expiry1)
    
    // Should be approximately 23 hours difference (allowing for execution time)
    const diffHours = (expiry24.getTime() - expiry1.getTime()) / (1000 * 60 * 60)
    assert.ok(diffHours >= 22.9 && diffHours <= 23.1)
  })

  test('isTokenExpired should correctly identify expired tokens', () => {
    // Create an expired token (1 second ago)
    const expiredDate = new Date(Date.now() - 1000)
    assert.strictEqual(isTokenExpired(expiredDate.toISOString()), true)
    
    // Create a future token (1 hour from now)
    const futureDate = new Date(Date.now() + 3600000)
    assert.strictEqual(isTokenExpired(futureDate.toISOString()), false)
  })

  test('isTokenExpired should handle edge cases', () => {
    // Test with current time (should be expired due to millisecond precision)
    const now = new Date().toISOString()
    assert.strictEqual(isTokenExpired(now), true)
    
    // Test with invalid date string
    assert.throws(() => {
      isTokenExpired('invalid-date')
    })
  })
})

describe('Token Security', () => {
  test('tokens should have sufficient entropy', () => {
    const tokens = new Set()
    const numTokens = 1000
    
    // Generate many tokens and ensure they're all unique
    for (let i = 0; i < numTokens; i++) {
      tokens.add(generateSecureToken())
    }
    
    assert.strictEqual(tokens.size, numTokens, 'All tokens should be unique')
  })

  test('tokens should not be predictable', () => {
    const token1 = generateSecureToken()
    const token2 = generateSecureToken()
    
    // Tokens should not have common prefixes or patterns
    let commonChars = 0
    for (let i = 0; i < Math.min(token1.length, token2.length); i++) {
      if (token1[i] === token2[i]) {
        commonChars++
      } else {
        break
      }
    }
    
    // Should have very few common characters at the start
    assert.ok(commonChars < 4, 'Tokens should not have predictable patterns')
  })
})
