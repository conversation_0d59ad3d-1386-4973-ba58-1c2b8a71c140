import { describe, it, expect, beforeAll, afterAll, beforeEach } from '@jest/globals'

describe('Review Session API', () => {
  let authToken: string
  let testUserId: string
  let testPaperIds: string[] = []

  beforeAll(async () => {
    // Create test user and get auth token
    const registerResponse = await fetch('http://localhost:3000/api/auth/register', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        email: `test-review-session-${Date.now()}@example.com`,
        password: 'TestPassword123!',
        displayName: 'Review Session Test User'
      })
    })

    if (!registerResponse.ok) {
      throw new Error(`Failed to register test user: ${registerResponse.status}`)
    }

    const registerData = await registerResponse.json()
    authToken = registerData.token
    testUserId = registerData.user.id

    // Create test papers
    const paperPromises = Array.from({ length: 3 }, async (_, i) => {
      const paperResponse = await fetch('http://localhost:3000/api/papers', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${authToken}`
        },
        body: JSON.stringify({
          title: `Test Paper ${i + 1} for Review Session`,
          authors: [`Author ${i + 1}`],
          venue: 'Test Conference',
          year: 2024,
          tags: ['test', 'review-session']
        })
      })

      if (!paperResponse.ok) {
        throw new Error(`Failed to create test paper ${i + 1}`)
      }

      const paper = await paperResponse.json()
      return paper.id
    })

    testPaperIds = await Promise.all(paperPromises)

    // Create notes for the papers
    await Promise.all(testPaperIds.map(async (paperId, i) => {
      await fetch(`http://localhost:3000/api/notes/${paperId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${authToken}`
        },
        body: JSON.stringify({
          quickSummary: `Quick summary for test paper ${i + 1}`,
          keyIdeas: [
            `Key idea 1 for paper ${i + 1}`,
            `Key idea 2 for paper ${i + 1}`,
            `Key idea 3 for paper ${i + 1}`
          ]
        })
      })
    }))

    // Add papers to review queue
    await Promise.all(testPaperIds.map(async (paperId) => {
      await fetch(`http://localhost:3000/api/papers/${paperId}/review`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${authToken}`
        }
      })
    }))
  })

  afterAll(async () => {
    // Clean up test data
    if (testPaperIds.length > 0) {
      await Promise.all(testPaperIds.map(async (paperId) => {
        await fetch(`http://localhost:3000/api/papers/${paperId}`, {
          method: 'DELETE',
          headers: { 'Authorization': `Bearer ${authToken}` }
        })
      }))
    }
  })

  describe('GET /api/review/session', () => {
    it('should start a review session with due papers', async () => {
      const response = await fetch('http://localhost:3000/api/review/session', {
        headers: { 'Authorization': `Bearer ${authToken}` }
      })

      expect(response.ok).toBe(true)
      const data = await response.json()

      expect(data).toHaveProperty('papers')
      expect(data).toHaveProperty('sessionId')
      expect(data).toHaveProperty('startedAt')
      expect(data).toHaveProperty('totalCount')

      expect(Array.isArray(data.papers)).toBe(true)
      expect(data.papers.length).toBeGreaterThan(0)
      expect(typeof data.sessionId).toBe('string')
      expect(data.totalCount).toBe(data.papers.length)

      // Check paper structure
      const paper = data.papers[0]
      expect(paper).toHaveProperty('id')
      expect(paper).toHaveProperty('title')
      expect(paper).toHaveProperty('authors')
      expect(paper).toHaveProperty('note')
      expect(paper).toHaveProperty('review')
    })

    it('should require authentication', async () => {
      const response = await fetch('http://localhost:3000/api/review/session')
      expect(response.status).toBe(401)
    })

    it('should only return papers belonging to the authenticated user', async () => {
      const response = await fetch('http://localhost:3000/api/review/session', {
        headers: { 'Authorization': `Bearer ${authToken}` }
      })

      expect(response.ok).toBe(true)
      const data = await response.json()

      // All papers should belong to the test user
      data.papers.forEach((paper: any) => {
        expect(paper.userId).toBe(testUserId)
      })
    })
  })

  describe('POST /api/review/session', () => {
    let sessionId: string
    let viewedPaperIds: string[]

    beforeEach(async () => {
      // Start a session to get session ID and paper IDs
      const sessionResponse = await fetch('http://localhost:3000/api/review/session', {
        headers: { 'Authorization': `Bearer ${authToken}` }
      })
      const sessionData = await sessionResponse.json()
      sessionId = sessionData.sessionId
      viewedPaperIds = sessionData.papers.slice(0, 2).map((p: any) => p.id) // View first 2 papers
    })

    it('should complete a review session and mark papers as reviewed', async () => {
      const response = await fetch('http://localhost:3000/api/review/session', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${authToken}`
        },
        body: JSON.stringify({
          sessionId,
          viewedPaperIds
        })
      })

      expect(response.ok).toBe(true)
      const data = await response.json()

      expect(data).toHaveProperty('sessionId', sessionId)
      expect(data).toHaveProperty('completedAt')
      expect(data).toHaveProperty('results')
      expect(data).toHaveProperty('summary')

      expect(Array.isArray(data.results)).toBe(true)
      expect(data.results.length).toBe(viewedPaperIds.length)
      expect(data.summary.total).toBe(viewedPaperIds.length)
      expect(data.summary.successful).toBeGreaterThan(0)

      // Check that papers were marked as reviewed (next due date should be in the future)
      for (const result of data.results) {
        if (result.success) {
          expect(new Date(result.nextDue).getTime()).toBeGreaterThan(Date.now())
          expect(typeof result.interval).toBe('number')
          expect(result.interval).toBeGreaterThan(0)
        }
      }
    })

    it('should require valid session data', async () => {
      const response = await fetch('http://localhost:3000/api/review/session', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${authToken}`
        },
        body: JSON.stringify({
          sessionId: 'invalid',
          viewedPaperIds: 'not-an-array'
        })
      })

      expect(response.status).toBe(400)
      const data = await response.json()
      expect(data).toHaveProperty('error')
    })

    it('should require authentication', async () => {
      const response = await fetch('http://localhost:3000/api/review/session', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          sessionId,
          viewedPaperIds
        })
      })

      expect(response.status).toBe(401)
    })

    it('should only allow updating papers belonging to the authenticated user', async () => {
      // Try to update a paper that doesn't belong to the user
      const response = await fetch('http://localhost:3000/api/review/session', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${authToken}`
        },
        body: JSON.stringify({
          sessionId,
          viewedPaperIds: ['non-existent-paper-id']
        })
      })

      expect(response.ok).toBe(true)
      const data = await response.json()

      // Should have results but with failures for unauthorized papers
      const unauthorizedResults = data.results.filter((r: any) => !r.success && r.reason === 'unauthorized')
      expect(unauthorizedResults.length).toBeGreaterThan(0)
    })
  })
})
