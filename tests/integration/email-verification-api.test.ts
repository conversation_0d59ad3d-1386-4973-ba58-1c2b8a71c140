/**
 * Integration tests for email verification API endpoints
 * Run with: node --test tests/integration/email-verification-api.test.ts
 */

import { test, describe, before, after } from 'node:test'
import assert from 'node:assert'
import { users, emailVerificationTokens } from '../../lib/database'
import { hashPassword, generateSecureToken, hashToken, generateTokenExpiry } from '../../lib/auth'

// Mock user data for testing
const testUser = {
  email: '<EMAIL>',
  password: 'TestPassword123!',
  displayName: 'Test User',
}

let createdUserId: string
let verificationToken: string

describe('Email Verification API Integration Tests', () => {
  before(async () => {
    // Clean up any existing test user
    try {
      const existingUser = await users.getByEmail(testUser.email)
      if (existingUser) {
        await users.delete(existingUser.id)
      }
    } catch (error) {
      // User doesn't exist, which is fine
    }
  })

  after(async () => {
    // Clean up test user
    if (createdUserId) {
      try {
        await users.delete(createdUserId)
      } catch (error) {
        console.warn('Failed to clean up test user:', error)
      }
    }
  })

  test('Registration should create user and verification token', async () => {
    const response = await fetch('http://localhost:3000/api/auth/register', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: testUser.email,
        password: testUser.password,
        displayName: testUser.displayName,
      }),
    })

    assert.strictEqual(response.status, 201)
    
    const data = await response.json()
    assert.ok(data.user)
    assert.strictEqual(data.user.email, testUser.email)
    assert.strictEqual(data.user.emailVerified, false)
    assert.strictEqual(data.verificationRequired, true)
    
    createdUserId = data.user.id

    // Verify that a verification token was created
    const user = await users.getById(createdUserId)
    assert.ok(user)
    assert.strictEqual(user.emailVerified, false)
  })

  test('Login should be blocked for unverified user', async () => {
    const response = await fetch('http://localhost:3000/api/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: testUser.email,
        password: testUser.password,
      }),
    })

    assert.strictEqual(response.status, 403)
    
    const data = await response.json()
    assert.ok(data.error.includes('verify your email'))
    assert.strictEqual(data.code, 'EMAIL_NOT_VERIFIED')
  })

  test('Resend verification should work for unverified user', async () => {
    const response = await fetch('http://localhost:3000/api/auth/resend-verification', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: testUser.email,
      }),
    })

    assert.strictEqual(response.status, 200)
    
    const data = await response.json()
    assert.ok(data.message.includes('verification link'))
  })

  test('Resend verification should be rate limited', async () => {
    // First request should succeed
    const response1 = await fetch('http://localhost:3000/api/auth/resend-verification', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: testUser.email,
      }),
    })
    assert.strictEqual(response1.status, 200)

    // Immediate second request should be rate limited
    const response2 = await fetch('http://localhost:3000/api/auth/resend-verification', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: testUser.email,
      }),
    })
    assert.strictEqual(response2.status, 429)
    
    const data = await response2.json()
    assert.ok(data.error.includes('Too many requests'))
  })

  test('Email verification with valid token should work', async () => {
    // Create a verification token manually for testing
    verificationToken = generateSecureToken()
    const tokenHash = hashToken(verificationToken)
    const expiresAt = generateTokenExpiry(24)

    await emailVerificationTokens.create({
      userId: createdUserId,
      tokenHash,
      expiresAt: expiresAt.toISOString(),
      used: false,
    })

    // Test the verification endpoint
    const response = await fetch(`http://localhost:3000/api/auth/verify-email?token=${verificationToken}`)
    
    // Should redirect to login with success
    assert.strictEqual(response.status, 302)
    assert.ok(response.headers.get('location')?.includes('verify=ok'))

    // Verify that user is now marked as verified
    const user = await users.getById(createdUserId)
    assert.ok(user)
    assert.strictEqual(user.emailVerified, true)

    // Verify that token is marked as used
    const token = await emailVerificationTokens.getByToken(verificationToken)
    assert.ok(token?.usedAt)
  })

  test('Email verification with invalid token should fail', async () => {
    const invalidToken = 'invalid-token-123'
    
    const response = await fetch(`http://localhost:3000/api/auth/verify-email?token=${invalidToken}`)
    
    // Should redirect to login with error
    assert.strictEqual(response.status, 302)
    assert.ok(response.headers.get('location')?.includes('verify=invalid'))
  })

  test('Email verification with expired token should fail', async () => {
    // Create an expired token
    const expiredToken = generateSecureToken()
    const expiredTokenHash = hashToken(expiredToken)
    const expiredDate = new Date(Date.now() - 1000) // 1 second ago

    await emailVerificationTokens.create({
      userId: createdUserId,
      tokenHash: expiredTokenHash,
      expiresAt: expiredDate.toISOString(),
      used: false,
    })

    const response = await fetch(`http://localhost:3000/api/auth/verify-email?token=${expiredToken}`)
    
    // Should redirect to login with expired error
    assert.strictEqual(response.status, 302)
    assert.ok(response.headers.get('location')?.includes('verify=expired'))
  })

  test('Login should work after email verification', async () => {
    const response = await fetch('http://localhost:3000/api/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: testUser.email,
        password: testUser.password,
      }),
    })

    assert.strictEqual(response.status, 200)
    
    const data = await response.json()
    assert.ok(data.user)
    assert.ok(data.token)
    assert.strictEqual(data.user.emailVerified, true)
  })
})
