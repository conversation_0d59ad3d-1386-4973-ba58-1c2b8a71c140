/**
 * Full System Validation Test
 * 
 * This test validates that all major system components work together correctly.
 * It tests the complete user journey from registration to paper management.
 * 
 * Run with: node --test tests/integration/full-system-validation.test.ts
 */

import { test, describe, before, after } from 'node:test'
import assert from 'node:assert'
import { users, query } from '../../lib/database'
import { runStartupValidation } from '../../lib/startup-checks'

// Test configuration
const BASE_URL = process.env.TEST_BASE_URL || 'http://localhost:3000'
const TEST_EMAIL = '<EMAIL>'
const TEST_PASSWORD = 'FullSystemTest123!'
const TEST_DISPLAY_NAME = 'Full System Test User'

let testUserId: string
let authToken: string
let testPaperId: string
let testCollectionId: string

describe('Full System Validation', () => {
  before(async () => {
    console.log('🔍 Running system validation checks...')
    
    // Validate system health before running tests
    const validation = await runStartupValidation()
    if (!validation.success) {
      console.warn('⚠️  System validation has issues:')
      validation.errors.forEach(error => console.warn(`   - ${error}`))
      console.warn('Continuing with tests anyway...')
    }

    // Clean up any existing test user
    try {
      const existingUser = await users.getByEmail(TEST_EMAIL)
      if (existingUser) {
        await users.delete(existingUser.id)
      }
    } catch (error) {
      // User doesn't exist, which is fine
    }
  })

  after(async () => {
    // Clean up test data
    if (testUserId) {
      try {
        await users.delete(testUserId)
      } catch (error) {
        console.warn('Failed to clean up test user:', error)
      }
    }
  })

  describe('System Health and Configuration', () => {
    test('Health endpoint should return healthy status', async () => {
      const response = await fetch(`${BASE_URL}/api/health`)
      assert.ok(response.status === 200 || response.status === 503, 'Health endpoint should respond')
      
      const data = await response.json()
      assert.ok(data.status, 'Health response should include status')
      assert.ok(data.checks, 'Health response should include checks')
    })

    test('Detailed health check should provide comprehensive information', async () => {
      const response = await fetch(`${BASE_URL}/api/health?detailed=true`)
      const data = await response.json()
      
      assert.ok(data.summary, 'Detailed health should include summary')
      assert.ok(data.checks, 'Detailed health should include checks')
      assert.ok(Array.isArray(data.checks), 'Checks should be an array')
      assert.ok(data.checks.length > 5, 'Should have multiple health checks')
    })

    test('Database should have correct schema', async () => {
      // Check critical tables exist
      const tables = ['users', 'papers', 'notes', 'collections', 'reviews']
      
      for (const tableName of tables) {
        const result = await query(`
          SELECT EXISTS (
            SELECT FROM information_schema.tables 
            WHERE table_name = $1
          );
        `, [tableName])
        
        assert.strictEqual(result.rows[0].exists, true, `Table ${tableName} should exist`)
      }
    })

    test('Admin user should exist and be functional', async () => {
      const adminResponse = await fetch(`${BASE_URL}/api/auth/login`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          email: '<EMAIL>',
          password: 'admin123'
        })
      })

      assert.strictEqual(adminResponse.status, 200, 'Admin login should succeed')
      
      const adminData = await adminResponse.json()
      assert.ok(adminData.token, 'Admin login should return token')
      assert.strictEqual(adminData.user.role, 'admin', 'Admin should have admin role')
    })
  })

  describe('Complete User Registration and Authentication Flow', () => {
    test('User registration should work correctly', async () => {
      const response = await fetch(`${BASE_URL}/api/auth/register`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          email: TEST_EMAIL,
          password: TEST_PASSWORD,
          displayName: TEST_DISPLAY_NAME
        })
      })

      assert.strictEqual(response.status, 201, 'Registration should succeed')
      
      const data = await response.json()
      assert.ok(data.user, 'Registration should return user data')
      assert.strictEqual(data.user.email, TEST_EMAIL, 'User email should match')
      assert.strictEqual(data.verificationRequired, true, 'Should require verification')
      
      testUserId = data.user.id
    })

    test('Email verification token should be created', async () => {
      const result = await query(
        'SELECT * FROM email_verification_tokens WHERE user_id = $1 AND used_at IS NULL',
        [testUserId]
      )
      
      assert.ok(result.rows.length > 0, 'Verification token should be created')
      assert.ok(result.rows[0].token, 'Token should have a value')
    })

    test('Login should be blocked before email verification', async () => {
      const response = await fetch(`${BASE_URL}/api/auth/login`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          email: TEST_EMAIL,
          password: TEST_PASSWORD
        })
      })

      assert.strictEqual(response.status, 403, 'Login should be blocked')
      
      const data = await response.json()
      assert.strictEqual(data.code, 'EMAIL_NOT_VERIFIED', 'Should return verification error')
    })

    test('Email verification should work', async () => {
      // Get verification token from database
      const tokenResult = await query(
        'SELECT token FROM email_verification_tokens WHERE user_id = $1 AND used_at IS NULL',
        [testUserId]
      )
      
      const token = tokenResult.rows[0].token
      
      const response = await fetch(`${BASE_URL}/api/auth/verify-email?token=${token}`, {
        redirect: 'manual'
      })

      assert.ok(response.status === 302 || response.status === 307, 'Should redirect after verification')
    })

    test('Login should work after email verification', async () => {
      const response = await fetch(`${BASE_URL}/api/auth/login`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          email: TEST_EMAIL,
          password: TEST_PASSWORD
        })
      })

      assert.strictEqual(response.status, 200, 'Login should succeed after verification')
      
      const data = await response.json()
      assert.ok(data.token, 'Login should return token')
      assert.strictEqual(data.user.emailVerified, true, 'User should be verified')
      
      authToken = data.token
    })
  })

  describe('Paper Management Functionality', () => {
    test('Should create a new paper', async () => {
      const paperData = {
        title: 'Test Paper for Full System Validation',
        authors: ['Test Author 1', 'Test Author 2'],
        venue: 'Test Conference 2024',
        year: 2024,
        abstract: 'This is a test paper created during full system validation.',
        tags: ['test', 'validation', 'system'],
        starred: true
      }

      const response = await fetch(`${BASE_URL}/api/papers`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(paperData)
      })

      assert.strictEqual(response.status, 201, 'Paper creation should succeed')
      
      const data = await response.json()
      assert.ok(data.id, 'Paper should have an ID')
      assert.strictEqual(data.title, paperData.title, 'Paper title should match')
      assert.strictEqual(data.userId, testUserId, 'Paper should belong to user')
      
      testPaperId = data.id
    })

    test('Should retrieve user papers', async () => {
      const response = await fetch(`${BASE_URL}/api/papers`, {
        headers: { 'Authorization': `Bearer ${authToken}` }
      })

      assert.strictEqual(response.status, 200, 'Papers retrieval should succeed')
      
      const data = await response.json()
      assert.ok(Array.isArray(data), 'Should return array of papers')
      assert.ok(data.length > 0, 'Should have at least one paper')
      
      const testPaper = data.find((p: any) => p.id === testPaperId)
      assert.ok(testPaper, 'Should find the test paper')
    })

    test('Should update paper', async () => {
      const updateData = {
        title: 'Updated Test Paper Title',
        starred: false
      }

      const response = await fetch(`${BASE_URL}/api/papers/${testPaperId}`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(updateData)
      })

      assert.strictEqual(response.status, 200, 'Paper update should succeed')
      
      const data = await response.json()
      assert.strictEqual(data.title, updateData.title, 'Title should be updated')
      assert.strictEqual(data.starred, updateData.starred, 'Starred status should be updated')
    })
  })

  describe('Collections Management', () => {
    test('Should create a collection', async () => {
      const collectionData = {
        name: 'Test Collection',
        paperIds: [testPaperId]
      }

      const response = await fetch(`${BASE_URL}/api/collections`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(collectionData)
      })

      assert.strictEqual(response.status, 201, 'Collection creation should succeed')
      
      const data = await response.json()
      assert.ok(data.id, 'Collection should have an ID')
      assert.strictEqual(data.name, collectionData.name, 'Collection name should match')
      
      testCollectionId = data.id
    })

    test('Should retrieve user collections', async () => {
      const response = await fetch(`${BASE_URL}/api/collections`, {
        headers: { 'Authorization': `Bearer ${authToken}` }
      })

      assert.strictEqual(response.status, 200, 'Collections retrieval should succeed')
      
      const data = await response.json()
      assert.ok(Array.isArray(data), 'Should return array of collections')
      
      const testCollection = data.find((c: any) => c.id === testCollectionId)
      assert.ok(testCollection, 'Should find the test collection')
    })
  })

  describe('Security and Access Control', () => {
    test('Should reject requests without authentication', async () => {
      const response = await fetch(`${BASE_URL}/api/papers`)
      assert.strictEqual(response.status, 401, 'Should require authentication')
    })

    test('Should reject requests with invalid token', async () => {
      const response = await fetch(`${BASE_URL}/api/papers`, {
        headers: { 'Authorization': 'Bearer invalid-token' }
      })
      assert.strictEqual(response.status, 401, 'Should reject invalid token')
    })

    test('Should not allow access to other users data', async () => {
      // This would require creating another user and testing cross-user access
      // For now, we verify that papers are filtered by user_id
      const response = await fetch(`${BASE_URL}/api/papers`, {
        headers: { 'Authorization': `Bearer ${authToken}` }
      })

      const data = await response.json()
      for (const paper of data) {
        assert.strictEqual(paper.userId, testUserId, 'Papers should belong to authenticated user')
      }
    })
  })

  describe('Data Integrity and Persistence', () => {
    test('Data should persist across requests', async () => {
      // Verify paper still exists
      const paperResponse = await fetch(`${BASE_URL}/api/papers/${testPaperId}`, {
        headers: { 'Authorization': `Bearer ${authToken}` }
      })
      assert.strictEqual(paperResponse.status, 200, 'Paper should still exist')

      // Verify collection still exists
      const collectionResponse = await fetch(`${BASE_URL}/api/collections`, {
        headers: { 'Authorization': `Bearer ${authToken}` }
      })
      const collections = await collectionResponse.json()
      const testCollection = collections.find((c: any) => c.id === testCollectionId)
      assert.ok(testCollection, 'Collection should still exist')
    })

    test('Database constraints should be enforced', async () => {
      // Try to create paper with invalid user_id (should fail)
      const invalidPaperData = {
        title: 'Invalid Paper',
        authors: ['Test Author'],
        venue: 'Test Venue',
        year: 2024
      }

      // This should fail because the API should set user_id from the token
      // and we can't override it to an invalid value
      const response = await fetch(`${BASE_URL}/api/papers`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(invalidPaperData)
      })

      // Should succeed because API sets valid user_id
      assert.strictEqual(response.status, 201, 'API should set valid user_id')
    })
  })

  describe('System Performance and Reliability', () => {
    test('Health checks should respond quickly', async () => {
      const start = Date.now()
      const response = await fetch(`${BASE_URL}/api/health`)
      const duration = Date.now() - start

      assert.ok(duration < 5000, 'Health check should respond within 5 seconds')
      assert.ok(response.status === 200 || response.status === 503, 'Health check should respond')
    })

    test('API endpoints should handle concurrent requests', async () => {
      // Make multiple concurrent requests
      const promises = Array.from({ length: 5 }, () =>
        fetch(`${BASE_URL}/api/papers`, {
          headers: { 'Authorization': `Bearer ${authToken}` }
        })
      )

      const responses = await Promise.all(promises)
      
      for (const response of responses) {
        assert.strictEqual(response.status, 200, 'All concurrent requests should succeed')
      }
    })
  })
})
