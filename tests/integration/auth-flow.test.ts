/**
 * Integration tests for complete authentication flows
 * Run with: node --test tests/integration/auth-flow.test.ts
 */

import { test, describe, before, after } from 'node:test'
import assert from 'node:assert'
import { users, emailVerificationTokens, userSessions } from '../../lib/database'
import { hashPassword } from '../../lib/auth'

// Test configuration
const BASE_URL = process.env.TEST_BASE_URL || 'http://localhost:3000'
const TEST_EMAIL = '<EMAIL>'
const TEST_PASSWORD = 'TestPassword123!'
const TEST_DISPLAY_NAME = 'Auth Test User'

let testUserId: string
let authToken: string

describe('Authentication Flow Integration Tests', () => {
  before(async () => {
    // Clean up any existing test user
    try {
      const existingUser = await users.getByEmail(TEST_EMAIL)
      if (existingUser) {
        await users.delete(existingUser.id)
      }
    } catch (error) {
      // User doesn't exist, which is fine
    }
  })

  after(async () => {
    // Clean up test user and related data
    if (testUserId) {
      try {
        await users.delete(testUserId)
      } catch (error) {
        console.warn('Failed to clean up test user:', error)
      }
    }
  })

  describe('User Registration Flow', () => {
    test('Should register new user successfully', async () => {
      const response = await fetch(`${BASE_URL}/api/auth/register`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: TEST_EMAIL,
          password: TEST_PASSWORD,
          displayName: TEST_DISPLAY_NAME,
        }),
      })

      assert.strictEqual(response.status, 201, 'Registration should return 201')

      const data = await response.json()
      assert.ok(data.user, 'Response should include user data')
      assert.strictEqual(data.user.email, TEST_EMAIL, 'User email should match')
      assert.strictEqual(data.user.emailVerified, false, 'New user should not be verified')
      assert.strictEqual(data.verificationRequired, true, 'Should indicate verification required')

      testUserId = data.user.id
    })

    test('Should prevent duplicate email registration', async () => {
      const response = await fetch(`${BASE_URL}/api/auth/register`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: TEST_EMAIL,
          password: TEST_PASSWORD,
          displayName: 'Another User',
        }),
      })

      assert.strictEqual(response.status, 400, 'Duplicate registration should return 400')

      const data = await response.json()
      assert.ok(data.error, 'Response should include error message')
      assert.match(data.error, /already exists/i, 'Error should mention email already exists')
    })

    test('Should validate password requirements', async () => {
      const response = await fetch(`${BASE_URL}/api/auth/register`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: '<EMAIL>',
          password: '123',
          displayName: 'Weak Password User',
        }),
      })

      assert.strictEqual(response.status, 400, 'Weak password should return 400')

      const data = await response.json()
      assert.ok(data.error, 'Response should include error message')
      assert.match(data.error, /password/i, 'Error should mention password requirements')
    })
  })

  describe('Email Verification Flow', () => {
    let verificationToken: string

    test('Should create verification token during registration', async () => {
      // Get the verification token from database
      const tokens = await emailVerificationTokens.getByUserId(testUserId)
      assert.ok(tokens.length > 0, 'Verification token should be created')

      verificationToken = tokens[0].token
      assert.ok(verificationToken, 'Token should have a value')
    })

    test('Should verify email with valid token', async () => {
      const response = await fetch(`${BASE_URL}/api/auth/verify-email?token=${verificationToken}`)

      // Should redirect to login page with success message
      assert.ok(response.redirected || response.status === 302, 'Should redirect after verification')
    })

    test('Should mark user as verified in database', async () => {
      const user = await users.getById(testUserId)
      assert.strictEqual(user?.emailVerified, true, 'User should be marked as verified')
    })

    test('Should reject invalid verification token', async () => {
      const response = await fetch(`${BASE_URL}/api/auth/verify-email?token=invalid-token`)

      // Should redirect to login with error
      assert.ok(response.redirected || response.status === 302, 'Should redirect on invalid token')
    })
  })

  describe('Login Flow', () => {
    test('Should reject login before email verification', async () => {
      // Create another unverified user for this test
      const unverifiedEmail = '<EMAIL>'
      const registerResponse = await fetch(`${BASE_URL}/api/auth/register`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: unverifiedEmail,
          password: TEST_PASSWORD,
          displayName: 'Unverified User',
        }),
      })

      assert.strictEqual(registerResponse.status, 201, 'Registration should succeed')

      // Try to login with unverified account
      const loginResponse = await fetch(`${BASE_URL}/api/auth/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: unverifiedEmail,
          password: TEST_PASSWORD,
        }),
      })

      assert.strictEqual(loginResponse.status, 403, 'Unverified login should return 403')

      const data = await loginResponse.json()
      assert.strictEqual(data.code, 'EMAIL_NOT_VERIFIED', 'Should return specific error code')

      // Clean up unverified user
      const unverifiedUser = await users.getByEmail(unverifiedEmail)
      if (unverifiedUser) {
        await users.delete(unverifiedUser.id)
      }
    })

    test('Should login successfully with verified account', async () => {
      const response = await fetch(`${BASE_URL}/api/auth/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: TEST_EMAIL,
          password: TEST_PASSWORD,
        }),
      })

      assert.strictEqual(response.status, 200, 'Login should return 200')

      const data = await response.json()
      assert.ok(data.user, 'Response should include user data')
      assert.ok(data.token, 'Response should include auth token')
      assert.ok(data.expiresAt, 'Response should include token expiry')

      authToken = data.token
    })

    test('Should reject invalid credentials', async () => {
      const response = await fetch(`${BASE_URL}/api/auth/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: TEST_EMAIL,
          password: 'wrong-password',
        }),
      })

      assert.strictEqual(response.status, 401, 'Invalid login should return 401')

      const data = await response.json()
      assert.ok(data.error, 'Response should include error message')
    })

    test('Should create session in database', async () => {
      const sessions = await userSessions.getByUserId(testUserId)
      assert.ok(sessions.length > 0, 'Session should be created in database')

      const session = sessions[0]
      assert.strictEqual(session.userId, testUserId, 'Session should belong to correct user')
      assert.ok(session.tokenHash, 'Session should have token hash')
      assert.ok(new Date(session.expiresAt) > new Date(), 'Session should not be expired')
    })
  })

  describe('Protected Route Access', () => {
    test('Should access protected route with valid token', async () => {
      const response = await fetch(`${BASE_URL}/api/auth/me`, {
        headers: {
          'Authorization': `Bearer ${authToken}`,
        },
      })

      assert.strictEqual(response.status, 200, 'Protected route should return 200 with valid token')

      const data = await response.json()
      assert.ok(data.user, 'Response should include user data')
      assert.strictEqual(data.user.id, testUserId, 'Should return correct user')
    })

    test('Should reject access without token', async () => {
      const response = await fetch(`${BASE_URL}/api/auth/me`)

      assert.strictEqual(response.status, 401, 'Protected route should return 401 without token')

      const data = await response.json()
      assert.ok(data.error, 'Response should include error message')
    })

    test('Should reject access with invalid token', async () => {
      const response = await fetch(`${BASE_URL}/api/auth/me`, {
        headers: {
          'Authorization': 'Bearer invalid-token',
        },
      })

      assert.strictEqual(response.status, 401, 'Protected route should return 401 with invalid token')
    })
  })

  describe('Password Reset Flow', () => {
    test('Should initiate password reset', async () => {
      const response = await fetch(`${BASE_URL}/api/auth/forgot-password`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: TEST_EMAIL,
        }),
      })

      assert.strictEqual(response.status, 200, 'Password reset should return 200')

      const data = await response.json()
      assert.ok(data.message, 'Response should include success message')
    })

    test('Should not reveal if email exists', async () => {
      const response = await fetch(`${BASE_URL}/api/auth/forgot-password`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: '<EMAIL>',
        }),
      })

      // Should return same response to prevent email enumeration
      assert.strictEqual(response.status, 200, 'Should return 200 even for non-existent email')
    })
  })

  describe('Logout Flow', () => {
    test('Should logout successfully', async () => {
      const response = await fetch(`${BASE_URL}/api/auth/logout`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${authToken}`,
        },
      })

      assert.strictEqual(response.status, 200, 'Logout should return 200')
    })

    test('Should invalidate session after logout', async () => {
      const response = await fetch(`${BASE_URL}/api/auth/me`, {
        headers: {
          'Authorization': `Bearer ${authToken}`,
        },
      })

      assert.strictEqual(response.status, 401, 'Token should be invalid after logout')
    })
  })
})
