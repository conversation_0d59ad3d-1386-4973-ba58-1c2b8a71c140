'use client'

import React, { useEffect, useState, Suspense } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { useAuth } from '@/lib/auth-context'
import { EmailVerificationStatus } from '@/components/auth/EmailVerificationStatus'
import { AuthModal } from '@/components/auth/AuthModal'

function LoginPageContent() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const { isAuthenticated, isLoading } = useAuth()
  const [showAuthModal, setShowAuthModal] = useState(false)

  // Get verification status from URL parameters
  const verifyStatus = searchParams.get('verify') as 'ok' | 'expired' | 'invalid' | 'already' | 'error' | null
  const email = searchParams.get('email')

  useEffect(() => {
    // If user is already authenticated, redirect to dashboard
    if (isAuthenticated) {
      router.push('/dashboard')
      return
    }

    // If no verification status, show auth modal
    if (!verifyStatus) {
      setShowAuthModal(true)
    }
  }, [isAuthenticated, verifyStatus, router])

  const handleBackToLogin = () => {
    // Clear URL parameters and show auth modal
    router.replace('/login')
    setShowAuthModal(true)
  }

  const handleAuthSuccess = () => {
    setShowAuthModal(false)
    router.push('/dashboard')
  }

  const handleCloseAuthModal = () => {
    setShowAuthModal(false)
    router.push('/dashboard')
  }

  // Show loading state
  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    )
  }

  // If user is authenticated, don't show anything (will redirect)
  if (isAuthenticated) {
    return null
  }

  // Show email verification status if present
  if (verifyStatus) {
    return (
      <EmailVerificationStatus
        status={verifyStatus}
        email={email || undefined}
        onBackToLogin={handleBackToLogin}
      />
    )
  }

  // Show auth modal for regular login
  return (
    <>
      <div className="flex items-center justify-center min-h-screen p-4">
        <div className="text-center space-y-4">
          <h1 className="text-2xl font-bold">Welcome to PaperNugget</h1>
          <p className="text-muted-foreground">Sign in to access your research papers</p>
        </div>
      </div>
      
      <AuthModal
        isOpen={showAuthModal}
        onClose={handleCloseAuthModal}
        defaultMode="login"
      />
    </>
  )
}

export default function LoginPage() {
  return (
    <Suspense fallback={
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    }>
      <LoginPageContent />
    </Suspense>
  )
}
