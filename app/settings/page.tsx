'use client'

import { useState } from 'react'
import { SidebarTrigger } from '@/components/ui/sidebar'
import { ProtectedRoute } from '@/components/auth/ProtectedRoute'
import { AuthModal } from '@/components/auth/AuthModal'
import { UserSettings } from '@/components/user/UserSettings'

function SettingsPageContent() {
  return (
    <div className="flex flex-col min-h-screen">
      <header className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="container flex h-14 items-center">
          <SidebarTrigger />
          <div className="ml-4">
            <h1 className="text-lg font-semibold">Settings</h1>
          </div>
        </div>
      </header>

      <main className="flex-1">
        <UserSettings />
      </main>
    </div>
  )
}

export default function SettingsPage() {
  const [authModalOpen, setAuthModalOpen] = useState(false)

  return (
    <ProtectedRoute
      onAuthRequired={() => setAuthModalOpen(true)}
      requireEmailVerification={false}
      fallback={
        <AuthModal
          isOpen={true}
          onClose={() => {
            // Redirect to login page instead of just closing
            window.location.href = '/login'
          }}
        />
      }
    >
      <SettingsPageContent />
    </ProtectedRoute>
  )
}