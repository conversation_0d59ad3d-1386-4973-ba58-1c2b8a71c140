"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { <PERSON>ader2, <PERSON><PERSON><PERSON>, ArrowLef<PERSON> } from "lucide-react"
import { ReviewSessionSlideshow } from "@/components/review-session-slideshow"
import { ReviewSessionErrorBoundary } from "@/components/review-session-error-boundary"
import { ReviewSessionSkeleton } from "@/components/review-session-skeleton"
import { useToast } from "@/hooks/use-toast"
import { ProtectedRoute } from "@/components/auth/ProtectedRoute"
import { AuthModal } from "@/components/auth/AuthModal"
import type { Paper, Note } from "@/lib/types"

interface ReviewSessionPaper extends Paper {
  note?: Note | null
}

interface ReviewSession {
  papers: ReviewSessionPaper[]
  sessionId: string
  startedAt: string
  totalCount: number
}

function ReviewSessionPageContent() {
  const router = useRouter()
  const { toast } = useToast()
  const [session, setSession] = useState<ReviewSession | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    startReviewSession()
  }, [])

  const startReviewSession = async () => {
    try {
      setLoading(true)
      setError(null)

      // Check if there are parameters in the URL
      const urlParams = new URLSearchParams(window.location.search)
      const collectionId = urlParams.get('collection')
      const includeAll = urlParams.get('includeAll')

      let url = "/api/review/session"
      const params = new URLSearchParams()
      if (collectionId) params.append('collection', collectionId)
      if (includeAll) params.append('includeAll', includeAll)
      if (params.toString()) url += `?${params.toString()}`

      const response = await fetch(url)
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const sessionData = await response.json()
      
      if (sessionData.papers.length === 0) {
        setError("No papers are due for review at this time.")
        return
      }

      setSession(sessionData)
    } catch (error) {
      console.error("Error starting review session:", error)
      setError("Failed to start review session. Please try again.")
      toast({
        title: "Error starting review session",
        description: "Please try again later",
        variant: "destructive"
      })
    } finally {
      setLoading(false)
    }
  }

  const handleSessionComplete = () => {
    toast({
      title: "Review session completed! 🎉",
      description: "Great job! Your progress has been saved."
    })
    router.push("/review")
  }

  const handleSessionCancel = () => {
    router.push("/review")
  }

  if (loading) {
    return <ReviewSessionSkeleton />
  }

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen p-4">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <CardTitle className="flex items-center justify-center gap-2">
              <BookOpen className="h-6 w-6" />
              Review Session
            </CardTitle>
          </CardHeader>
          <CardContent className="text-center space-y-4">
            <p className="text-muted-foreground">{error}</p>
            <div className="flex gap-2 justify-center">
              <Button variant="outline" onClick={() => router.push("/review")}>
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Review
              </Button>
              <Button onClick={startReviewSession}>
                Try Again
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  if (!session) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen">
        <p className="text-lg">No review session available.</p>
        <Button 
          className="mt-4" 
          onClick={() => router.push("/review")}
        >
          Back to Review
        </Button>
      </div>
    )
  }

  return (
    <ReviewSessionErrorBoundary
      onReset={startReviewSession}
      onGoBack={() => router.push("/review")}
    >
      <ReviewSessionSlideshow
        papers={session.papers}
        sessionId={session.sessionId}
        onComplete={handleSessionComplete}
        onCancel={handleSessionCancel}
      />
    </ReviewSessionErrorBoundary>
  )
}

export default function ReviewSessionPage() {
  const [authModalOpen, setAuthModalOpen] = useState(false)

  return (
    <ProtectedRoute
      onAuthRequired={() => setAuthModalOpen(true)}
      requireEmailVerification={false}
      fallback={
        <AuthModal
          isOpen={true}
          onClose={() => {
            // Redirect to login page instead of just closing
            window.location.href = '/login'
          }}
        />
      }
    >
      <ReviewSessionPageContent />
    </ProtectedRoute>
  )
}
