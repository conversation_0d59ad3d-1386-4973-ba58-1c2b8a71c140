'use client'

import { SidebarTrigger } from '@/components/ui/sidebar'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { 
  BookOpen, 
  Zap, 
  Users, 
  Settings, 
  ExternalLink, 
  CheckCircle, 
  ArrowRight,
  Key,
  TestTube,
  Library,
  Lightbulb,
  Target,
  Workflow
} from 'lucide-react'
import Link from 'next/link'

export default function GettingStartedPage() {
  return (
    <div className="flex flex-col min-h-screen">
      <header className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="container flex h-14 items-center">
          <SidebarTrigger />
          <div className="ml-4">
            <h1 className="text-lg font-semibold">Getting Started</h1>
          </div>
        </div>
      </header>

      <main className="flex-1 container py-6 space-y-6 max-w-4xl mx-auto">
        {/* Welcome Section */}
        <div className="text-center space-y-4 px-4">
          <h1 className="text-2xl sm:text-3xl font-bold tracking-tight">Welcome to PaperNugget</h1>
          <p className="text-lg sm:text-xl text-muted-foreground max-w-2xl mx-auto leading-relaxed">
            Your research companion for quick recall of academic papers through structured summaries and key insights.
          </p>
          <p className="text-sm text-muted-foreground">
            Developed by Veronica Valeros from{' '}
            <a
              href="https://lostandloud.art"
              target="_blank"
              rel="noopener noreferrer"
              className="text-primary hover:underline"
            >
              Lost and Loud Creative Studio
            </a>
          </p>
        </div>

        {/* Purpose & Value Proposition */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Target className="h-5 w-5 text-primary" />
              Purpose & Value Proposition
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <p className="text-muted-foreground">
              PaperNugget is designed for busy researchers who need to quickly recall and review research papers. 
              Instead of re-reading entire papers, you can capture the essence through:
            </p>
            <div className="grid sm:grid-cols-2 lg:grid-cols-3 gap-4">
              <div className="flex items-start gap-3 p-3 rounded-lg bg-yellow-50 dark:bg-yellow-950/20">
                <Lightbulb className="h-5 w-5 text-yellow-600 dark:text-yellow-400 mt-0.5 flex-shrink-0" />
                <div>
                  <h4 className="font-medium">Quick Summaries</h4>
                  <p className="text-sm text-muted-foreground">Concise overviews of paper content</p>
                </div>
              </div>
              <div className="flex items-start gap-3 p-3 rounded-lg bg-green-50 dark:bg-green-950/20">
                <CheckCircle className="h-5 w-5 text-green-600 dark:text-green-400 mt-0.5 flex-shrink-0" />
                <div>
                  <h4 className="font-medium">3 Key Ideas</h4>
                  <p className="text-sm text-muted-foreground">Essential takeaways from each paper</p>
                </div>
              </div>
              <div className="flex items-start gap-3 p-3 rounded-lg bg-blue-50 dark:bg-blue-950/20 sm:col-span-2 lg:col-span-1">
                <BookOpen className="h-5 w-5 text-blue-600 dark:text-blue-400 mt-0.5 flex-shrink-0" />
                <div>
                  <h4 className="font-medium">Organized Collections</h4>
                  <p className="text-sm text-muted-foreground">Group papers by topic or project</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Current Integrations */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Workflow className="h-5 w-5 text-primary" />
              Current Integrations
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="border rounded-lg p-4">
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 bg-red-100 rounded-lg flex items-center justify-center">
                    <span className="text-red-600 font-bold text-sm">Z</span>
                  </div>
                  <div>
                    <h4 className="font-medium">Zotero Sync</h4>
                    <p className="text-sm text-muted-foreground">Bidirectional synchronization with your Zotero library</p>
                  </div>
                </div>
                <Badge variant="secondary">Active</Badge>
              </div>
              <ul className="text-sm text-muted-foreground space-y-1 ml-11">
                <li>• Sync paper metadata and notes to Zotero</li>
                <li>• Support for both personal libraries and group libraries</li>
                <li>• Collection-specific sync destinations</li>
                <li>• Automatic duplicate detection and prevention</li>
              </ul>
            </div>
          </CardContent>
        </Card>

        {/* Collections Feature */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BookOpen className="h-5 w-5 text-primary" />
              Collections Feature
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <p className="text-muted-foreground">
              Organize your papers into collections for better management and targeted sync destinations.
            </p>
            
            <div className="space-y-4">
              <div className="border-l-4 border-blue-500 pl-4 py-2">
                <h4 className="font-medium mb-2 text-blue-700 dark:text-blue-300">Basic Collections</h4>
                <p className="text-sm text-muted-foreground mb-3 leading-relaxed">
                  Group related papers by topic, project, or any criteria that makes sense for your research.
                </p>
                <Link href="/collections">
                  <Button variant="outline" size="sm" className="text-blue-600 border-blue-200 hover:bg-blue-50">
                    <BookOpen className="h-4 w-4 mr-2" />
                    Manage Collections
                  </Button>
                </Link>
              </div>

              <div className="border-l-4 border-green-500 pl-4 py-2">
                <h4 className="font-medium mb-2 text-green-700 dark:text-green-300">Collection-Specific Zotero Sync</h4>
                <p className="text-sm text-muted-foreground mb-3 leading-relaxed">
                  Map different collections to different Zotero destinations:
                </p>
                <div className="grid sm:grid-cols-2 gap-3 text-sm">
                  <div className="flex items-center gap-2 p-2 rounded bg-blue-50 dark:bg-blue-950/20">
                    <Library className="h-4 w-4 text-blue-500 flex-shrink-0" />
                    <span>Sync to "My Library"</span>
                  </div>
                  <div className="flex items-center gap-2 p-2 rounded bg-green-50 dark:bg-green-950/20">
                    <Users className="h-4 w-4 text-green-500 flex-shrink-0" />
                    <span>Sync to specific Zotero groups</span>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Zotero Setup Guide */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Settings className="h-5 w-5 text-primary" />
              Zotero Setup Guide
            </CardTitle>
            <CardDescription>
              Follow these steps to connect your Zotero account with PaperNugget
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Step 1 */}
            <div className="flex gap-4">
              <div className="flex-shrink-0 w-8 h-8 bg-primary text-primary-foreground rounded-full flex items-center justify-center text-sm font-medium">
                1
              </div>
              <div className="space-y-3 flex-1">
                <h4 className="font-medium">Generate Zotero API Key</h4>
                <p className="text-sm text-muted-foreground leading-relaxed">
                  Visit your Zotero account settings to create a new API key with the required permissions.
                </p>
                <Button variant="outline" size="sm" asChild className="w-full sm:w-auto">
                  <a href="https://www.zotero.org/settings/keys" target="_blank" rel="noopener noreferrer">
                    <ExternalLink className="h-4 w-4 mr-2" />
                    Open Zotero API Keys
                  </a>
                </Button>
                <Alert className="border-amber-200 bg-amber-50 dark:bg-amber-950/20">
                  <Key className="h-4 w-4 text-amber-600" />
                  <AlertDescription className="text-amber-800 dark:text-amber-200">
                    <strong>Required permissions:</strong> "Allow library access" and "Allow write access" for both personal library and any groups you want to sync with.
                  </AlertDescription>
                </Alert>
              </div>
            </div>

            {/* Step 2 */}
            <div className="flex gap-4">
              <div className="flex-shrink-0 w-8 h-8 bg-primary text-primary-foreground rounded-full flex items-center justify-center text-sm font-medium">
                2
              </div>
              <div className="space-y-3 flex-1">
                <h4 className="font-medium">Configure API Key in PaperNugget</h4>
                <p className="text-sm text-muted-foreground leading-relaxed">
                  Add your Zotero API key to PaperNugget settings to enable synchronization.
                </p>
                <Link href="/settings">
                  <Button variant="outline" size="sm" className="w-full sm:w-auto">
                    <Settings className="h-4 w-4 mr-2" />
                    Open Settings
                  </Button>
                </Link>
              </div>
            </div>

            {/* Step 3 */}
            <div className="flex gap-4">
              <div className="flex-shrink-0 w-8 h-8 bg-primary text-primary-foreground rounded-full flex items-center justify-center text-sm font-medium">
                3
              </div>
              <div className="space-y-3 flex-1">
                <h4 className="font-medium">Test Connection</h4>
                <p className="text-sm text-muted-foreground leading-relaxed">
                  Use the "Test Connection" button in settings to verify your API key is working correctly.
                </p>
                <div className="flex items-center gap-2 text-sm text-green-600 dark:text-green-400 p-2 rounded bg-green-50 dark:bg-green-950/20">
                  <TestTube className="h-4 w-4 flex-shrink-0" />
                  <span>Connection test will verify access to your library and groups</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Quick Actions */}
        <Card>
          <CardHeader>
            <CardTitle>Ready to Get Started?</CardTitle>
            <CardDescription>
              Jump into your research workflow with these quick actions
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid sm:grid-cols-2 lg:grid-cols-3 gap-3">
              <Link href="/papers/new" className="block">
                <Button className="w-full justify-start">
                  <Zap className="h-4 w-4 mr-2" />
                  Add Your First Paper
                </Button>
              </Link>
              <Link href="/collections" className="block">
                <Button variant="outline" className="w-full justify-start">
                  <BookOpen className="h-4 w-4 mr-2" />
                  Create a Collection
                </Button>
              </Link>
              <Link href="/settings" className="block sm:col-span-2 lg:col-span-1">
                <Button variant="outline" className="w-full justify-start">
                  <Settings className="h-4 w-4 mr-2" />
                  Configure Zotero
                </Button>
              </Link>
            </div>
          </CardContent>
        </Card>
      </main>
    </div>
  )
}
