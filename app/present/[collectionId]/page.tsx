"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation"
import { <PERSON>Left, ArrowRight, X, Star, Edit2 } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Textarea } from "@/components/ui/textarea"
import { authenticatedFetch } from "@/lib/utils"
import type { Paper, Note } from "@/lib/types"

export default function PresentPage() {
  const params = useParams()
  const router = useRouter()
  const [papers, setPapers] = useState<Paper[]>([])
  const [notes, setNotes] = useState<Record<string, Note>>({})
  const [currentIndex, setCurrentIndex] = useState(0)
  const [filterTag, setFilterTag] = useState("")
  const [isEditing, setIsEditing] = useState(false)
  const [editingNote, setEditingNote] = useState<Note | null>(null)

  const collectionId = params.collectionId as string

  useEffect(() => {
    fetchData()
  }, [collectionId])

  const fetchData = async () => {
    try {
      const [papersRes, notesRes] = await Promise.all([
        authenticatedFetch(`/api/collections/${collectionId}/papers`),
        authenticatedFetch(`/api/collections/${collectionId}/notes`),
      ])

      if (papersRes.ok) {
        const papersData = await papersRes.json()
        setPapers(papersData)
      }

      if (notesRes.ok) {
        const notesData = await notesRes.json()
        const notesMap = notesData.reduce((acc: Record<string, Note>, note: Note) => {
          acc[note.paperId] = note
          return acc
        }, {})
        setNotes(notesMap)
      }
    } catch (error) {
      console.error("Failed to fetch data:", error)
    }
  }

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (isEditing) return

      switch (e.key) {
        case "ArrowLeft":
          e.preventDefault()
          setCurrentIndex((prev) => Math.max(0, prev - 1))
          break
        case "ArrowRight":
          e.preventDefault()
          setCurrentIndex((prev) => Math.min(papers.length - 1, prev + 1))
          break
        case "f":
          e.preventDefault()
          const tag = prompt("Filter by tag:")
          if (tag !== null) setFilterTag(tag)
          break
        case "n":
          e.preventDefault()
          const jumpTo = prompt("Jump to paper number:")
          if (jumpTo) {
            const index = Number.parseInt(jumpTo) - 1
            if (index >= 0 && index < papers.length) {
              setCurrentIndex(index)
            }
          }
          break
        case "e":
          e.preventDefault()
          startEditing()
          break
        case "s":
          e.preventDefault()
          toggleStar()
          break
        case "Escape":
          router.push(`/collections/${collectionId}`)
          break
      }
    }

    window.addEventListener("keydown", handleKeyDown)
    return () => window.removeEventListener("keydown", handleKeyDown)
  }, [papers.length, isEditing])

  const filteredPapers = filterTag ? papers.filter((paper) => (paper.tags || []).includes(filterTag)) : papers

  const currentPaper = filteredPapers[currentIndex]
  const currentNote = currentPaper ? notes[currentPaper.id] : null

  const startEditing = () => {
    if (currentNote) {
      setEditingNote({ ...currentNote })
      setIsEditing(true)
    }
  }

  const saveEdit = async () => {
    if (!editingNote || !currentPaper) return

    try {
      await authenticatedFetch(`/api/notes/${currentPaper.id}`, {
        method: "PUT",
        body: JSON.stringify(editingNote),
      })

      setNotes((prev) => ({ ...prev, [currentPaper.id]: editingNote }))
      setIsEditing(false)
      setEditingNote(null)
    } catch (error) {
      console.error("Failed to save note:", error)
    }
  }

  const toggleStar = async () => {
    if (!currentPaper) return

    try {
      await authenticatedFetch(`/api/papers/${currentPaper.id}/star`, { method: "POST" })
      setPapers((prev) => prev.map((p) => (p.id === currentPaper.id ? { ...p, starred: !p.starred } : p)))
    } catch (error) {
      console.error("Failed to toggle star:", error)
    }
  }

  if (!currentPaper) {
    return (
      <div className="h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4">No papers to present</h1>
          <Button onClick={() => router.push(`/collections/${collectionId}`)}>
            <X className="h-4 w-4 mr-2" />
            Close
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="h-screen bg-background flex flex-col">
      {/* Header */}
      <div className="border-b p-4 flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="ghost" size="sm" onClick={() => router.push(`/collections/${collectionId}`)}>
            <X className="h-4 w-4" />
          </Button>
          <span className="text-sm text-muted-foreground">
            {currentIndex + 1} of {filteredPapers.length}
          </span>
          {filterTag && (
            <Badge variant="secondary">
              Filtered by: {filterTag}
              <button onClick={() => setFilterTag("")} className="ml-2 hover:bg-muted rounded-full p-1">
                <X className="h-3 w-3" />
              </button>
            </Badge>
          )}
        </div>
        <div className="flex items-center gap-2">
          <Button variant="ghost" size="sm" onClick={startEditing}>
            <Edit2 className="h-4 w-4" />
          </Button>
          <Button variant="ghost" size="sm" onClick={toggleStar}>
            <Star className={`h-4 w-4 ${currentPaper.starred ? "fill-yellow-400 text-yellow-400" : ""}`} />
          </Button>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 p-8 overflow-auto">
        <div className="max-w-4xl mx-auto">
          {/* Paper Info */}
          <div className="mb-8">
            <h1 className="text-4xl font-bold mb-4">{currentPaper.title}</h1>
            <p className="text-xl text-muted-foreground mb-2">{(currentPaper.authors || []).join(", ")}</p>
            {currentPaper.venue && (
              <p className="text-lg text-muted-foreground">
                {currentPaper.venue} {currentPaper.year && `(${currentPaper.year})`}
              </p>
            )}
            <div className="flex gap-2 mt-4">
              {(currentPaper.tags || []).map((tag) => (
                <Badge key={tag} variant="outline">
                  {tag}
                </Badge>
              ))}
            </div>
          </div>

          {/* Notes */}
          {currentNote && (
            <div className="space-y-6">
              {isEditing && editingNote ? (
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <h2 className="text-2xl font-semibold">Edit Notes</h2>
                    <div className="flex gap-2">
                      <Button onClick={saveEdit}>Save</Button>
                      <Button variant="outline" onClick={() => setIsEditing(false)}>
                        Cancel
                      </Button>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <label className="text-sm font-medium">Quick Summary</label>
                    <Textarea
                      value={editingNote.quickSummary || ""}
                      onChange={(e) => setEditingNote({ ...editingNote, quickSummary: e.target.value })}
                      rows={2}
                    />
                  </div>

                  {editingNote.keyIdeas.map((idea, index) => (
                    <div key={index} className="space-y-2">
                      <label className="text-sm font-medium">Key Idea {index + 1}</label>
                      <Textarea
                        value={idea}
                        onChange={(e) => {
                          const keyIdeas = [...editingNote.keyIdeas]
                          keyIdeas[index] = e.target.value
                          setEditingNote({ ...editingNote, keyIdeas })
                        }}
                        rows={2}
                      />
                    </div>
                  ))}
                </div>
              ) : (
                <div>
                  {currentNote.quickSummary && (
                    <div className="mb-8">
                      <h2 className="text-2xl font-semibold mb-4">Quick Summary</h2>
                      <p className="text-xl leading-relaxed bg-blue-50 dark:bg-blue-950/30 p-4 rounded-lg border-l-4 border-blue-500">
                        {currentNote.quickSummary}
                      </p>
                    </div>
                  )}

                  <h2 className="text-2xl font-semibold mb-6">Key Ideas</h2>
                  <div className="space-y-6">
                    {currentNote.keyIdeas.filter(Boolean).map((idea, index) => (
                      <div key={index} className="flex items-start gap-4">
                        <span className="bg-red-500 text-white rounded-full w-10 h-10 flex items-center justify-center text-xl font-bold shrink-0 mt-1">
                          {index + 1}
                        </span>
                        <p className="text-lg leading-relaxed">{idea}</p>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Navigation */}
      <div className="border-t p-4 flex items-center justify-between">
        <Button
          variant="outline"
          onClick={() => setCurrentIndex((prev) => Math.max(0, prev - 1))}
          disabled={currentIndex === 0}
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Previous
        </Button>

        <div className="text-sm text-muted-foreground">
          Use ← → to navigate, F to filter, N to jump, E to edit, S to star, ESC to close
        </div>

        <Button
          variant="outline"
          onClick={() => setCurrentIndex((prev) => Math.min(filteredPapers.length - 1, prev + 1))}
          disabled={currentIndex === filteredPapers.length - 1}
        >
          Next
          <ArrowRight className="h-4 w-4 ml-2" />
        </Button>
      </div>
    </div>
  )
}
