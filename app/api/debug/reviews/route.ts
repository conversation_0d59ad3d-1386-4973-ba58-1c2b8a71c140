import { NextResponse } from "next/server"
import { reviews, papers } from "@/lib/database"
import { withAuth } from "@/lib/auth-middleware"

export const GET = withAuth(async (request, { user, userId }) => {
  try {
    console.log(`[Debug] Getting reviews for user: ${userId}`)
    
    // Get all reviews for this user
    const userReviews = await reviews.getByUserId(userId)
    console.log(`[Debug] Found ${userReviews.length} reviews for user`)
    
    // Get all papers for this user
    const userPapers = await papers.getByUserId(userId)
    console.log(`[Debug] Found ${userPapers.length} papers for user`)
    
    // Get detailed info for each review
    const reviewDetails = await Promise.all(
      userReviews.map(async (review) => {
        const paper = await papers.getById(review.paperId)
        return {
          review,
          paper: paper ? { id: paper.id, title: paper.title, userId: paper.userId } : null
        }
      })
    )
    
    const now = new Date().toISOString()
    const dueReviews = userReviews.filter(review => review.nextDue <= now)
    
    return NextResponse.json({
      userId,
      userEmail: user.email,
      totalReviews: userReviews.length,
      totalPapers: userPapers.length,
      dueReviews: dueReviews.length,
      currentTime: now,
      reviews: reviewDetails,
      dueReviewDetails: dueReviews.map(review => ({
        paperId: review.paperId,
        nextDue: review.nextDue,
        isDue: review.nextDue <= now,
        timeDiff: new Date(review.nextDue).getTime() - new Date(now).getTime()
      }))
    })
  } catch (error) {
    console.error("Debug error:", error)
    return NextResponse.json(
      { error: "Debug failed", details: error.message },
      { status: 500 }
    )
  }
}, { allowUnverified: true })
