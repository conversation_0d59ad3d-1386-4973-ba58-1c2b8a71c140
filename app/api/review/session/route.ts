import { NextResponse } from "next/server"
import { reviews, papers, notes } from "@/lib/database"
import { withAuth } from "@/lib/auth-middleware"

// Simple in-memory rate limiting (in production, use Redis or similar)
const sessionAttempts = new Map<string, { count: number; resetTime: number }>()

function checkRateLimit(userId: string): boolean {
  const now = Date.now()
  const userAttempts = sessionAttempts.get(userId)

  if (!userAttempts || now > userAttempts.resetTime) {
    sessionAttempts.set(userId, { count: 1, resetTime: now + 60000 }) // 1 minute window
    return true
  }

  if (userAttempts.count >= 10) { // Max 10 sessions per minute
    return false
  }

  userAttempts.count++
  return true
}

export const GET = withAuth(async (request, { user, userId }) => {
  try {
    // Check rate limit
    if (!checkRateLimit(userId)) {
      return NextResponse.json(
        { error: "Too many session requests. Please wait a moment." },
        { status: 429 }
      )
    }

    const sessionStartTime = Date.now()
    const url = new URL(request.url)
    const collectionId = url.searchParams.get('collection')
    const includeAll = url.searchParams.get('includeAll') === 'true'

    console.log(`[ReviewSession] Starting session for user: ${userId}${collectionId ? ` (collection: ${collectionId})` : ''}${includeAll ? ' (including all papers)' : ''}`)
    const now = new Date().toISOString()

    // Get reviews for the specific user
    let userReviews = await reviews.getByUserId(userId)
    console.log(`[ReviewSession] Found ${userReviews.length} total reviews for user: ${userId}`)

    // If collection ID is provided, filter reviews to only include papers in that collection
    if (collectionId) {
      const { collections } = await import("@/lib/database")
      const collection = await collections.getById(collectionId)

      if (!collection || collection.userId !== userId) {
        return NextResponse.json(
          { error: "Collection not found or access denied" },
          { status: 404 }
        )
      }

      userReviews = userReviews.filter(review => collection.paperIds.includes(review.paperId))
      console.log(`[ReviewSession] Filtered to ${userReviews.length} reviews for collection: ${collection.name}`)
    }

    console.log(`[ReviewSession] User reviews:`, userReviews.map(r => ({ paperId: r.paperId, nextDue: r.nextDue, type: typeof r.nextDue })))

    // Filter for due reviews (or include all if requested)
    const reviewsToProcess = includeAll ? userReviews : userReviews.filter((review) => {
      try {
        // Handle both string and Date objects
        const reviewDueTime = typeof review.nextDue === 'string'
          ? new Date(review.nextDue).getTime()
          : new Date(review.nextDue).getTime()
        const currentTime = new Date(now).getTime()
        const isDue = reviewDueTime <= currentTime

        console.log(`[ReviewSession] Paper ${review.paperId}:`)
        console.log(`  - nextDue: ${review.nextDue} (type: ${typeof review.nextDue}, time: ${reviewDueTime})`)
        console.log(`  - now: ${now} (time: ${currentTime})`)
        console.log(`  - isDue: ${isDue} (diff: ${reviewDueTime - currentTime}ms)`)

        return isDue
      } catch (error) {
        console.error(`[ReviewSession] Error processing review ${review.paperId}:`, error)
        return false
      }
    })
    console.log(`[ReviewSession] Found ${reviewsToProcess.length} ${includeAll ? 'total' : 'due'} reviews for user: ${userId}`)

    // Get papers and notes for reviews to process (all reviews already belong to the user)
    const duePapers = await Promise.all(
      reviewsToProcess.map(async (review) => {
        try {
          const paper = await papers.getById(review.paperId)
          if (!paper) {
            console.warn(`Paper ${review.paperId} not found`)
            return null
          }

          const note = await notes.getByPaperId(review.paperId)
          return { ...paper, review, note }
        } catch (error) {
          console.error(`Error fetching paper ${review.paperId}:`, error)
          return null
        }
      })
    )

    const result = duePapers.filter(Boolean)
    const sessionDuration = Date.now() - sessionStartTime

    console.log(`[ReviewSession] Session created successfully for user: ${userId}, papers: ${result.length}, duration: ${sessionDuration}ms`)

    return NextResponse.json({
      papers: result,
      sessionId: crypto.randomUUID(),
      startedAt: now,
      totalCount: result.length
    })
  } catch (error) {
    console.error("Error starting review session:", error)
    return NextResponse.json(
      { error: "Failed to start review session" },
      { status: 500 }
    )
  }
}, { allowUnverified: true })

export const POST = withAuth(async (request, { user, userId }) => {
  try {
    const body = await request.json()
    const { sessionId, viewedPaperIds } = body

    // Validate input
    if (!sessionId || typeof sessionId !== 'string' || sessionId.trim().length === 0) {
      return NextResponse.json(
        { error: "Invalid session ID" },
        { status: 400 }
      )
    }

    if (!Array.isArray(viewedPaperIds)) {
      return NextResponse.json(
        { error: "viewedPaperIds must be an array" },
        { status: 400 }
      )
    }

    if (viewedPaperIds.length === 0) {
      return NextResponse.json(
        { error: "No papers to mark as reviewed" },
        { status: 400 }
      )
    }

    // Validate paper IDs
    const invalidPaperIds = viewedPaperIds.filter(id =>
      typeof id !== 'string' || id.trim().length === 0
    )

    if (invalidPaperIds.length > 0) {
      return NextResponse.json(
        { error: "Invalid paper IDs provided" },
        { status: 400 }
      )
    }

    // Limit the number of papers that can be processed at once
    if (viewedPaperIds.length > 100) {
      return NextResponse.json(
        { error: "Too many papers in one session (max 100)" },
        { status: 400 }
      )
    }

    console.log("Completing review session:", sessionId, "Papers viewed:", viewedPaperIds.length)

    // Mark all viewed papers as reviewed using the spaced repetition algorithm
    const results = await Promise.all(
      viewedPaperIds.map(async (paperId) => {
        try {
          // Verify the paper belongs to the user
          const paper = await papers.getById(paperId)
          if (!paper || paper.userId !== userId) {
            console.warn(`Paper ${paperId} does not belong to user ${userId}`)
            return { paperId, success: false, reason: "unauthorized" }
          }

          const existingReview = await reviews.getByPaperId(paperId)
          if (!existingReview) {
            console.warn(`No review found for paper ${paperId}`)
            return { paperId, success: false, reason: "no_review" }
          }

          // Calculate next review date using spaced repetition
          const currentInterval = existingReview.lastInterval || 1
          const currentEase = existingReview.ease || 2.5

          // For a successful review, increase the interval and ease
          let newInterval: number
          let newEase: number

          if (currentInterval === 1) {
            // First review - schedule for 6 days
            newInterval = 6
            newEase = Math.min(3.0, currentEase + 0.1)
          } else {
            // Subsequent reviews - use spaced repetition
            newInterval = Math.max(1, Math.round(currentInterval * currentEase))
            newEase = Math.min(3.0, currentEase + 0.1)
          }

          const nextDue = new Date()
          nextDue.setDate(nextDue.getDate() + newInterval)

          console.log(`[ReviewSession] Updating paper ${paperId}: interval ${currentInterval} -> ${newInterval}, ease ${currentEase} -> ${newEase}, next due: ${nextDue.toISOString()}`)

          // Update the review
          const updatedReview = await reviews.update(paperId, {
            nextDue: nextDue.toISOString(),
            ease: newEase,
            lastInterval: newInterval
          })

          return {
            paperId,
            success: true,
            nextDue: nextDue.toISOString(),
            interval: newInterval,
            ease: newEase
          }
        } catch (error) {
          console.error(`Error updating review for paper ${paperId}:`, error)
          return { paperId, success: false, reason: "error" }
        }
      })
    )

    const successful = results.filter(r => r.success).length
    const failed = results.filter(r => !r.success).length

    console.log(`Review session completed. Successful: ${successful}, Failed: ${failed}`)

    return NextResponse.json({
      sessionId,
      completedAt: new Date().toISOString(),
      results,
      summary: {
        total: viewedPaperIds.length,
        successful,
        failed
      }
    })
  } catch (error) {
    console.error("Error completing review session:", error)
    return NextResponse.json(
      { error: "Failed to complete review session" },
      { status: 500 }
    )
  }
}, { allowUnverified: true })
