import { NextResponse } from "next/server"
import { reviews, papers } from "@/lib/database"
import { withAuth } from "@/lib/auth-middleware"

export const GET = withAuth(async (request, { user, userId }) => {
  try {
    console.log("Fetching review stats...")
    const userReviews = await reviews.getByUserId(userId)
    const userPapers = await papers.getByUserId(userId)
    console.log("Reviews:", userReviews.length, "Papers:", userPapers.length)
    
    const now = new Date()
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
    const tomorrow = new Date(today.getTime() + 24 * 60 * 60 * 1000)
    const nextWeek = new Date(today.getTime() + 7 * 24 * 60 * 60 * 1000)
    
    // Calculate statistics
    const totalPapers = userPapers.length
    const totalReviews = userReviews.length

    const dueToday = userReviews.filter(review => {
      const dueDate = new Date(review.nextDue)
      return dueDate <= tomorrow
    }).length

    const dueThisWeek = userReviews.filter(review => {
      const dueDate = new Date(review.nextDue)
      return dueDate <= nextWeek
    }).length

    const overdue = userReviews.filter(review => {
      const dueDate = new Date(review.nextDue)
      return dueDate < today
    }).length

    // Calculate average ease factor
    const averageEase = userReviews.length > 0
      ? userReviews.reduce((sum, review) => sum + review.ease, 0) / userReviews.length
      : 0

    // Calculate retention rate (papers with ease > 2.0)
    const wellRetained = userReviews.filter(review => review.ease > 2.0).length
    const retentionRate = userReviews.length > 0 ? (wellRetained / userReviews.length) * 100 : 0
    
    // Calculate next review intervals distribution
    const intervalDistribution = {
      today: dueToday,
      tomorrow: userReviews.filter(review => {
        const dueDate = new Date(review.nextDue)
        return dueDate > tomorrow && dueDate <= new Date(tomorrow.getTime() + 24 * 60 * 60 * 1000)
      }).length,
      thisWeek: dueThisWeek - dueToday,
      later: userReviews.length - dueThisWeek
    }
    
    const stats = {
      totalPapers,
      totalReviews,
      dueToday,
      dueThisWeek,
      overdue,
      averageEase: Math.round(averageEase * 100) / 100,
      retentionRate: Math.round(retentionRate * 10) / 10,
      intervalDistribution
    }
    
    return NextResponse.json(stats)
  } catch (error) {
    console.error("Error fetching review stats:", error)
    // Return default stats instead of error to prevent client-side crashes
    return NextResponse.json({
      totalPapers: 0,
      totalReviews: 0,
      dueToday: 0,
      dueThisWeek: 0,
      overdue: 0,
      averageEase: 0,
      retentionRate: 0,
      intervalDistribution: {
        today: 0,
        tomorrow: 0,
        thisWeek: 0,
        later: 0
      }
    })
  }
}, { allowUnverified: true })
