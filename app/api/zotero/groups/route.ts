import { NextRequest, NextResponse } from 'next/server'
import { withAuthSecurity } from '@/lib/security-middleware'
import { createSuccessResponse, createErrorResponse } from '@/lib/validation'
import { getZoteroSettingsFromPreferences, createZoteroSyncService } from '@/lib/zotero-sync-service'

/**
 * Get Zotero groups for the authenticated user
 */
export const GET = withAuthSecurity(async (request: NextRequest, { correlationId, user, userId, logger }) => {
  try {
    logger.info('Fetching Zotero groups', { userId })
    
    // Get Zotero settings from user preferences
    const zoteroSettings = getZoteroSettingsFromPreferences(user.preferences || {})
    
    if (!zoteroSettings.enabled) {
      return createErrorResponse(
        'Zotero sync is disabled',
        400,
        correlationId,
        ['Zotero sync is not enabled in your settings'],
        'Please enable Zotero sync in your settings'
      )
    }
    
    if (!zoteroSettings.apiKey) {
      return createErrorResponse(
        'Zotero API key not configured',
        400,
        correlationId,
        ['Zotero API key is not configured'],
        'Please configure your Zotero API key in settings'
      )
    }

    // Create Zotero client and fetch groups
    try {
      const syncService = await createZoteroSyncService(zoteroSettings.apiKey, zoteroSettings)
      const groups = await syncService.getZoteroGroups()

      return createSuccessResponse(
        groups,
        'Zotero groups retrieved successfully',
        correlationId
      )
    } catch (zoteroError) {
      logger.error('Zotero API error when fetching groups', {
        error: zoteroError.message,
        userId,
        hasApiKey: !!zoteroSettings.apiKey
      })

      // Return empty groups list instead of error to allow UI to function
      return createSuccessResponse(
        [],
        'No Zotero groups available',
        correlationId
      )
    }
  } catch (error) {
    logger.error('Error fetching Zotero groups', { error: error.message, userId })
    return createErrorResponse(
      'Failed to fetch Zotero groups',
      500,
      correlationId,
      ['An error occurred while fetching your Zotero groups'],
      'Internal server error'
    )
  }
}, {}, {
  allowUnverified: true,
  auditLog: {
    action: 'zotero_groups_list',
    resourceType: 'zotero'
  }
})
