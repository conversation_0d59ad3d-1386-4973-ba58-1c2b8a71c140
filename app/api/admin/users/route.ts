import { NextRequest, NextResponse } from 'next/server'
import { users, papers, collections } from '@/lib/database'
import { withAdminSecurity } from '@/lib/security-middleware'
import { validationSchemas } from '@/lib/validation-schemas'
import { sanitizeUser } from '@/lib/auth'
import { createSuccessResponse, createListResponse } from '@/lib/validation'
import { z } from 'zod'

export const GET = withAdminSecurity(async (request: NextRequest, { correlationId, validatedData, user, userId, logger }) => {
  try {
    logger.info('Admin listing users', { userId })

    // Get all users
    const allUsers = await users.getAll()

    // Get user statistics (paper and collection counts)
    const usersWithStats = await Promise.all(
      allUsers.map(async (user) => {
        const [userPapers, userCollections] = await Promise.all([
          papers.getByUserId(user.id),
          collections.getByUserId(user.id)
        ])

        return {
          ...sanitizeUser(user),
          paperCount: userPapers.length,
          collectionCount: userCollections.length
        }
      })
    )

    // Apply pagination and sorting from validated query
    const { page = 1, limit = 20, sortBy = 'createdAt', sortOrder = 'desc', search } = validatedData.query || {}

    // Filter by search if provided
    let filteredUsers = usersWithStats
    if (search) {
      const searchLower = search.toLowerCase()
      filteredUsers = usersWithStats.filter(user =>
        user.email.toLowerCase().includes(searchLower) ||
        (user.displayName && user.displayName.toLowerCase().includes(searchLower))
      )
    }

    // Sort users
    filteredUsers.sort((a, b) => {
      let aVal = a[sortBy as keyof typeof a]
      let bVal = b[sortBy as keyof typeof b]

      if (typeof aVal === 'string') aVal = aVal.toLowerCase()
      if (typeof bVal === 'string') bVal = bVal.toLowerCase()

      if (sortOrder === 'desc') {
        return aVal > bVal ? -1 : aVal < bVal ? 1 : 0
      } else {
        return aVal < bVal ? -1 : aVal > bVal ? 1 : 0
      }
    })

    // Paginate
    const startIndex = (page - 1) * limit
    const endIndex = startIndex + limit
    const paginatedUsers = filteredUsers.slice(startIndex, endIndex)

    const pagination = {
      page,
      limit,
      total: filteredUsers.length,
      totalPages: Math.ceil(filteredUsers.length / limit)
    }

    const sorting = { sortBy, sortOrder }

    return createSuccessResponse({
      data: paginatedUsers,
      pagination,
      sorting
    }, correlationId)
  } catch (error: any) {
    logger.error('Error fetching users', { error: error.message, userId })
    return NextResponse.json({ error: 'Failed to fetch users', correlationId }, { status: 500 })
  }
}, {
  query: validationSchemas.user.adminUserList
}, {
  auditLog: {
    action: 'admin_list_users',
    resourceType: 'user'
  }
})

export const POST = withAdminSecurity(async (request: NextRequest, { correlationId, validatedData, user, userId, logger }) => {
  try {
    logger.info('Admin creating user', { userId })

    const data = validatedData.body

    // Admin can create users with any role
    const newUser = await users.create({
      email: data.email.toLowerCase(),
      passwordHash: data.passwordHash, // Should be pre-hashed
      displayName: data.displayName,
      role: data.role || 'user',
      emailVerified: data.emailVerified || false,
      isActive: data.isActive !== undefined ? data.isActive : true,
      privacySettings: data.privacySettings || {},
      preferences: data.preferences || {},
    })

    logger.info('User created by admin', {
      userId,
      createdUserId: newUser.id,
      createdUserEmail: newUser.email
    })

    return createSuccessResponse({
      message: 'User created successfully',
      user: sanitizeUser(newUser)
    }, correlationId)
  } catch (error: any) {
    logger.error('Error creating user', { error: error.message, userId })
    return NextResponse.json({ error: 'Failed to create user', correlationId }, { status: 500 })
  }
}, {
  body: z.object({
    email: z.string().email('Invalid email format'),
    passwordHash: z.string().min(1, 'Password hash is required'),
    displayName: z.string().min(1).max(100).optional(),
    role: z.enum(['admin', 'user', 'readonly']).optional(),
    emailVerified: z.boolean().optional(),
    isActive: z.boolean().optional(),
    privacySettings: z.record(z.any()).optional(),
    preferences: z.record(z.any()).optional()
  })
}, {
  auditLog: {
    action: 'admin_create_user',
    resourceType: 'user'
  }
})