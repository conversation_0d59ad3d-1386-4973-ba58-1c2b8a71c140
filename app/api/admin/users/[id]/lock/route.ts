import { NextRequest, NextResponse } from 'next/server'
import { users, userSessions } from '@/lib/database'
import { withAdminSecurity } from '@/lib/security-middleware'
import { validationSchemas } from '@/lib/validation-schemas'
import { createSuccessResponse, createErrorResponse } from '@/lib/validation'
import { z } from 'zod'

export const POST = withAdminSecurity(async (request: NextRequest, { correlationId, validatedData, user, userId, logger }, { params }: { params: Promise<{ id: string }> }) => {
  try {
    const { id } = await params
    logger.info('Admin locking user account', { userId, targetUserId: id })

    // Prevent admin from locking themselves
    if (id === userId) {
      return createErrorResponse('Cannot lock your own account', 400, correlationId)
    }

    const targetUser = await users.getById(id)
    if (!targetUser) {
      return createErrorResponse('User not found', 404, correlationId)
    }

    const { locked, reason } = validatedData.body

    // Update user active status
    const updatedUser = await users.update(id, {
      isActive: !locked,
      updatedAt: new Date().toISOString()
    })

    if (!updatedUser) {
      return createErrorResponse('Failed to update user status', 500, correlationId)
    }

    // If locking the account, invalidate all user sessions
    if (locked) {
      await userSessions.deleteByUserId(id)
      logger.info('User sessions invalidated due to account lock', { targetUserId: id })
    }

    logger.info(`User account ${locked ? 'locked' : 'unlocked'} by admin`, { 
      userId, 
      targetUserId: id, 
      targetUserEmail: targetUser.email,
      reason 
    })

    return createSuccessResponse({
      message: `User account ${locked ? 'locked' : 'unlocked'} successfully`,
      userId: id,
      locked,
      reason
    }, correlationId)
  } catch (error: any) {
    logger.error('Error updating user lock status', { error: error.message, userId })
    return createErrorResponse('Failed to update user status', 500, correlationId)
  }
}, {
  params: validationSchemas.user.adminUserParams,
  body: z.object({
    locked: z.boolean(),
    reason: z.string().min(1, 'Reason is required').max(500, 'Reason too long').optional()
  })
}, {
  auditLog: {
    action: 'admin_lock_user',
    resourceType: 'user'
  }
})
