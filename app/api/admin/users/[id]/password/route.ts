import { NextRequest, NextResponse } from 'next/server'
import { users } from '@/lib/database'
import { withAdminSecurity } from '@/lib/security-middleware'
import { validationSchemas } from '@/lib/validation-schemas'
import { hashPassword } from '@/lib/auth'
import { createSuccessResponse, createErrorResponse } from '@/lib/validation'
import { z } from 'zod'

export const POST = withAdminSecurity(async (request: NextRequest, { correlationId, validatedData, user, userId, logger }, { params }: { params: Promise<{ id: string }> }) => {
  try {
    const { id } = await params
    logger.info('Admin resetting user password', { userId, targetUserId: id })

    const targetUser = await users.getById(id)
    if (!targetUser) {
      return createErrorResponse('User not found', 404, correlationId)
    }

    const { newPassword } = validatedData.body

    // Hash the new password
    const passwordHash = await hashPassword(newPassword)

    // Update user password
    const updatedUser = await users.update(id, {
      passwordHash,
      updatedAt: new Date().toISOString()
    })

    if (!updatedUser) {
      return createErrorResponse('Failed to update password', 500, correlationId)
    }

    logger.info('User password reset by admin', { 
      userId, 
      targetUserId: id, 
      targetUserEmail: targetUser.email 
    })

    return createSuccessResponse({
      message: 'Password reset successfully',
      userId: id
    }, correlationId)
  } catch (error: any) {
    logger.error('Error resetting user password', { error: error.message, userId })
    return createErrorResponse('Failed to reset password', 500, correlationId)
  }
}, {
  params: validationSchemas.user.adminUserParams,
  body: z.object({
    newPassword: z.string().min(8, 'Password must be at least 8 characters')
      .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, 'Password must contain at least one lowercase letter, one uppercase letter, and one number')
  })
}, {
  auditLog: {
    action: 'admin_reset_password',
    resourceType: 'user'
  }
})
