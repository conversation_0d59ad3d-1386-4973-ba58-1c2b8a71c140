import { NextRequest, NextResponse } from 'next/server'
import { users, papers, collections, userSessions } from '@/lib/database'
import { withAdminSecurity } from '@/lib/security-middleware'
import { validationSchemas } from '@/lib/validation-schemas'
import { sanitizeUser } from '@/lib/auth'
import { createSuccessResponse, createErrorResponse } from '@/lib/validation'
import { z } from 'zod'

export const GET = withAdminSecurity(async (request: NextRequest, { correlationId, validatedData, user, userId, logger }, { params }: { params: Promise<{ id: string }> }) => {
  try {
    const { id } = await params
    logger.info('Admin viewing user', { userId, targetUserId: id })

    const targetUser = await users.getById(id)
    if (!targetUser) {
      return createErrorResponse('User not found', 404, correlationId)
    }

    // Get additional user statistics
    const [targetUserPapers, targetUserCollections, targetUserSessions] = await Promise.all([
      papers.getByUserId(id),
      collections.getByUserId(id),
      userSessions.getByUserId(id)
    ])

    const userWithStats = {
      ...sanitizeUser(targetUser),
      paperCount: targetUserPapers.length,
      collectionCount: targetUserCollections.length,
      sessionCount: targetUserSessions.length,
      lastSession: targetUserSessions.length > 0 ? targetUserSessions[0].createdAt : null
    }

    return createSuccessResponse(userWithStats, correlationId)
  } catch (error: any) {
    logger.error('Error fetching user', { error: error.message, userId })
    return createErrorResponse('Failed to fetch user', 500, correlationId)
  }
}, {
  params: validationSchemas.user.adminUserParams
}, {
  auditLog: {
    action: 'admin_view_user',
    resourceType: 'user'
  }
})

export const PUT = withAdminSecurity(async (request: NextRequest, { correlationId, validatedData, user, userId, logger }, { params }: { params: Promise<{ id: string }> }) => {
  try {
    const { id } = await params
    logger.info('Admin updating user', { userId, targetUserId: id })

    const targetUser = await users.getById(id)
    if (!targetUser) {
      return createErrorResponse('User not found', 404, correlationId)
    }

    const data = validatedData.body

    // Prevent admin from demoting themselves
    if (id === userId && data.role && data.role !== 'admin') {
      return createErrorResponse('Cannot change your own admin role', 400, correlationId)
    }

    // Prevent admin from deactivating themselves
    if (id === userId && data.isActive === false) {
      return createErrorResponse('Cannot deactivate your own account', 400, correlationId)
    }

    const updatedUser = await users.update(id, {
      ...data,
      updatedAt: new Date().toISOString()
    })

    if (!updatedUser) {
      return createErrorResponse('Failed to update user', 500, correlationId)
    }

    logger.info('User updated by admin', {
      userId,
      targetUserId: id,
      changes: Object.keys(data)
    })

    return createSuccessResponse({
      message: 'User updated successfully',
      user: sanitizeUser(updatedUser)
    }, correlationId)
  } catch (error: any) {
    logger.error('Error updating user', { error: error.message, userId })
    return createErrorResponse('Failed to update user', 500, correlationId)
  }
}, {
  params: validationSchemas.user.adminUserParams,
  body: validationSchemas.admin.userUpdate
}, {
  auditLog: {
    action: 'admin_update_user',
    resourceType: 'user'
  }
})

export const DELETE = withAdminSecurity(async (request: NextRequest, { correlationId, validatedData, user, userId, logger }, { params }: { params: Promise<{ id: string }> }) => {
  try {
    const { id } = await params
    logger.info('Admin deleting user', { userId, targetUserId: id })

    // Prevent admin from deleting themselves
    if (id === userId) {
      return createErrorResponse('Cannot delete your own account', 400, correlationId)
    }

    const targetUser = await users.getById(id)
    if (!targetUser) {
      return createErrorResponse('User not found', 404, correlationId)
    }

    // Delete user (this will cascade delete papers, collections, etc. due to foreign key constraints)
    const deleted = await users.delete(id)
    if (!deleted) {
      return createErrorResponse('Failed to delete user', 500, correlationId)
    }

    logger.info('User deleted by admin', {
      userId,
      deletedUserId: id,
      deletedUserEmail: targetUser.email
    })

    return createSuccessResponse({
      message: 'User deleted successfully',
      deletedUser: {
        id: targetUser.id,
        email: targetUser.email,
        displayName: targetUser.displayName
      }
    }, correlationId)
  } catch (error: any) {
    logger.error('Error deleting user', { error: error.message, userId })
    return createErrorResponse('Failed to delete user', 500, correlationId)
  }
}, {
  params: validationSchemas.user.adminUserParams
}, {
  auditLog: {
    action: 'admin_delete_user',
    resourceType: 'user'
  }
})