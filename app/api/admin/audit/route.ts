import { NextRequest, NextResponse } from 'next/server'
import { auditLogs } from '@/lib/database'
import { withAdminSecurity } from '@/lib/security-middleware'
import { createSuccessResponse, createListResponse } from '@/lib/validation'
import { z } from 'zod'

export const GET = withAdminSecurity(async (request: NextRequest, { correlationId, validatedData, user, userId, logger }) => {
  try {
    logger.info('Admin requesting audit logs', { userId })

    const { page = 1, limit = 50, search, action, resourceType, userId: filterUserId } = validatedData.query || {}

    // Get all audit logs
    const allLogs = await auditLogs.getAll(1000) // Get more logs for filtering

    // Apply filters
    let filteredLogs = allLogs

    if (search) {
      const searchLower = search.toLowerCase()
      filteredLogs = filteredLogs.filter(log => 
        log.action.toLowerCase().includes(searchLower) ||
        (log.resourceType && log.resourceType.toLowerCase().includes(searchLower)) ||
        (log.resourceId && log.resourceId.toLowerCase().includes(searchLower)) ||
        (log.userId && log.userId.toLowerCase().includes(searchLower))
      )
    }

    if (action) {
      filteredLogs = filteredLogs.filter(log => log.action.includes(action))
    }

    if (resourceType) {
      filteredLogs = filteredLogs.filter(log => log.resourceType === resourceType)
    }

    if (filterUserId) {
      filteredLogs = filteredLogs.filter(log => log.userId === filterUserId)
    }

    // Sort by creation date (newest first)
    filteredLogs.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())

    // Paginate
    const startIndex = (page - 1) * limit
    const endIndex = startIndex + limit
    const paginatedLogs = filteredLogs.slice(startIndex, endIndex)

    const pagination = {
      page,
      limit,
      total: filteredLogs.length,
      totalPages: Math.ceil(filteredLogs.length / limit)
    }

    return createSuccessResponse({
      data: paginatedLogs,
      pagination
    }, correlationId)
  } catch (error: any) {
    logger.error('Error fetching audit logs', { error: error.message, userId })
    return NextResponse.json({ error: 'Failed to fetch audit logs', correlationId }, { status: 500 })
  }
}, {
  query: z.object({
    page: z.string().transform(val => parseInt(val) || 1).optional(),
    limit: z.string().transform(val => Math.min(parseInt(val) || 50, 100)).optional(),
    search: z.string().optional(),
    action: z.string().optional(),
    resourceType: z.string().optional(),
    userId: z.string().optional()
  })
}, {
  auditLog: {
    action: 'admin_view_audit_logs',
    resourceType: 'audit'
  }
})
