import { NextRequest, NextResponse } from 'next/server'
import { users, papers, collections, reviews, auditLogs } from '@/lib/database'
import { withAdminSecurity } from '@/lib/security-middleware'
import { createSuccessResponse, createErrorResponse } from '@/lib/validation'

export const GET = withAdminSecurity(async (request: NextRequest, { correlationId, user, userId, logger }) => {
  try {
    logger.info('Admin requesting dashboard stats', { userId })

    // Get all data for statistics
    const [allUsers, allPapers, allCollections, allReviews, recentAuditLogs] = await Promise.all([
      users.getAll(),
      papers.getAll(),
      collections.getAll(),
      reviews.getAll(),
      auditLogs.getRecent(50) // Get last 50 audit log entries
    ])

    // Calculate time-based statistics
    const now = new Date()
    const oneWeekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
    const oneMonthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)

    // User statistics
    const activeUsers = allUsers.filter(u => u.isActive).length
    const verifiedUsers = allUsers.filter(u => u.emailVerified).length
    const adminUsers = allUsers.filter(u => u.role === 'admin').length
    const newUsersThisWeek = allUsers.filter(u => new Date(u.createdAt) >= oneWeekAgo).length
    const newUsersThisMonth = allUsers.filter(u => new Date(u.createdAt) >= oneMonthAgo).length

    // Content statistics
    const papersThisWeek = allPapers.filter(p => new Date(p.createdAt) >= oneWeekAgo).length
    const papersThisMonth = allPapers.filter(p => new Date(p.createdAt) >= oneMonthAgo).length
    const collectionsThisWeek = allCollections.filter(c => new Date(c.createdAt) >= oneWeekAgo).length
    const collectionsThisMonth = allCollections.filter(c => new Date(c.createdAt) >= oneMonthAgo).length

    // Review statistics
    const reviewsThisWeek = allReviews.filter(r => new Date(r.createdAt || '') >= oneWeekAgo).length
    const reviewsThisMonth = allReviews.filter(r => new Date(r.createdAt || '') >= oneMonthAgo).length

    // Top users by content
    const userContentCounts = await Promise.all(
      allUsers.slice(0, 10).map(async (user) => {
        const [userPapers, userCollections] = await Promise.all([
          papers.getByUserId(user.id),
          collections.getByUserId(user.id)
        ])
        return {
          id: user.id,
          email: user.email,
          displayName: user.displayName,
          paperCount: userPapers.length,
          collectionCount: userCollections.length,
          totalContent: userPapers.length + userCollections.length
        }
      })
    )

    const topUsers = userContentCounts
      .sort((a, b) => b.totalContent - a.totalContent)
      .slice(0, 5)

    // Recent activity from audit logs
    const recentActivity = recentAuditLogs.map(log => ({
      id: log.id,
      action: log.action,
      resourceType: log.resourceType,
      userId: log.userId,
      createdAt: log.createdAt,
      details: log.details
    }))

    const dashboardStats = {
      users: {
        total: allUsers.length,
        active: activeUsers,
        verified: verifiedUsers,
        admins: adminUsers,
        newThisWeek: newUsersThisWeek,
        newThisMonth: newUsersThisMonth
      },
      content: {
        totalPapers: allPapers.length,
        totalCollections: allCollections.length,
        totalReviews: allReviews.length,
        papersThisWeek,
        papersThisMonth,
        collectionsThisWeek,
        collectionsThisMonth,
        reviewsThisWeek,
        reviewsThisMonth
      },
      topUsers,
      recentActivity: recentActivity.slice(0, 10)
    }

    return createSuccessResponse(dashboardStats, correlationId)
  } catch (error: any) {
    logger.error('Error fetching admin dashboard stats', { error: error.message, userId })
    return createErrorResponse('Failed to fetch dashboard statistics', 500, correlationId)
  }
}, {}, {
  auditLog: {
    action: 'admin_view_dashboard',
    resourceType: 'admin'
  }
})
