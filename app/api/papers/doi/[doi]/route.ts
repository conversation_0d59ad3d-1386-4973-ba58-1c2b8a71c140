import { type NextRequest, NextResponse } from "next/server"

export async function GET(request: NextRequest, { params }: { params: { doi: string } }) {
  try {
    const doi = decodeURIComponent(params.doi)

    // Validate DOI format
    if (!doi || !doi.includes('/')) {
      return NextResponse.json({ error: "Invalid DOI format" }, { status: 400 })
    }

    // Fetch from CrossRef API
    const crossrefUrl = `https://api.crossref.org/works/${doi}`
    const response = await fetch(crossrefUrl, {
      headers: {
        'Accept': 'application/json',
        'User-Agent': 'PaperNugget/1.0 (mailto:<EMAIL>)'
      }
    })

    if (!response.ok) {
      if (response.status === 404) {
        return NextResponse.json({ error: "DOI not found" }, { status: 404 })
      }
      throw new Error(`CrossRef API error: ${response.status}`)
    }

    const data = await response.json()
    const work = data.message

    // Extract paper information
    const paperData = {
      title: work.title?.[0] || '',
      authors: work.author?.map((author: any) =>
        `${author.given || ''} ${author.family || ''}`.trim()
      ).filter(Boolean) || [],
      venue: work['container-title']?.[0] || work.publisher || '',
      year: work.published?.['date-parts']?.[0]?.[0] ||
            work['published-print']?.['date-parts']?.[0]?.[0] ||
            work['published-online']?.['date-parts']?.[0]?.[0],
      doi: work.DOI,
      url: work.URL
    }

    return NextResponse.json(paperData)
  } catch (error) {
    console.error("Error fetching DOI:", error)
    return NextResponse.json(
      { error: "Failed to fetch paper information from DOI" },
      { status: 500 }
    )
  }
}
