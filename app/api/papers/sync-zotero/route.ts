import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import { papers } from '@/lib/database'
import { withAuthSecurity } from '@/lib/security-middleware'
import { createSuccessResponse, createErrorResponse } from '@/lib/validation'
import { canAccessResource } from '@/lib/auth-middleware'
import { createZoteroSyncService, getZoteroSettingsFromPreferences, validateZoteroSettings } from '@/lib/zotero-sync-service'

export const POST = withAuthSecurity(async (request: NextRequest, { correlationId, validatedData, user, userId, logger }) => {
  try {
    const data = validatedData.body
    logger.info('Bulk syncing papers to Zotero', { userId, mode: data.mode, paperCount: data.paperIds?.length })

    // Get Zotero settings from user preferences
    logger.info('Getting Zotero settings for sync all papers', {
      userId,
      hasPreferences: !!user.preferences,
      preferences: user.preferences
    })

    const zoteroSettings = getZoteroSettingsFromPreferences(user.preferences || {})

    logger.info('Zotero settings retrieved for sync all', {
      userId,
      enabled: zoteroSettings.enabled,
      hasApiKey: !!zoteroSettings.apiKey,
      hasUserId: !!zoteroSettings.userId,
      hasLibraryId: !!zoteroSettings.libraryId
    })
    
    if (!zoteroSettings.enabled) {
      logger.warn('Zotero sync not enabled for sync all papers', {
        userId,
        zoteroSettings
      })
      const errorResponse = createErrorResponse(
        'Zotero sync is disabled',
        400,
        correlationId,
        [
          'Zotero sync is currently disabled in your settings.',
          'To enable sync: Go to Settings → Zotero Integration → Enable Zotero Sync',
          'Make sure you have configured your API key and library settings.'
        ],
        'Please enable Zotero sync in your settings to use this feature'
      )
      logger.error('Returning 400 error response - Zotero not enabled for sync all', { response: errorResponse })
      return errorResponse
    }

    // Validate Zotero settings
    logger.info('Validating Zotero settings for bulk sync', {
      userId,
      mode: data.mode,
      enabled: zoteroSettings.enabled,
      hasApiKey: !!zoteroSettings.apiKey,
      apiKeyLength: zoteroSettings.apiKey?.length,
      libraryType: zoteroSettings.libraryType,
      libraryId: zoteroSettings.libraryId
    })

    const validation = validateZoteroSettings(zoteroSettings)
    if (!validation.valid) {
      logger.error('Zotero settings validation failed for bulk sync', {
        userId,
        mode: data.mode,
        errors: validation.errors,
        settings: {
          enabled: zoteroSettings.enabled,
          hasApiKey: !!zoteroSettings.apiKey,
          apiKeyLength: zoteroSettings.apiKey?.length,
          libraryType: zoteroSettings.libraryType,
          libraryId: zoteroSettings.libraryId
        }
      })
      return createErrorResponse(
        'Invalid Zotero configuration',
        400,
        correlationId,
        validation.errors,
        'Configuration validation failed'
      )
    }

    let paperIds: string[] = []

    if (data.mode === 'selected' && data.paperIds) {
      // Sync specific papers
      paperIds = data.paperIds
      
      // Verify user has access to all papers
      for (const paperId of paperIds) {
        const paper = await papers.getById(paperId)
        if (!paper) {
          return createErrorResponse(
            'Paper not found',
            404,
            correlationId,
            [`Paper ${paperId} not found`],
            'Invalid paper ID'
          )
        }
        
        if (!canAccessResource(user, paper.userId)) {
          return createErrorResponse(
            'Access denied',
            403,
            correlationId,
            [`Access denied to paper ${paperId}`],
            'Permission denied'
          )
        }
      }
    } else if (data.mode === 'changed') {
      // Sync only changed papers
      try {
        const syncService = await createZoteroSyncService(zoteroSettings.apiKey!, zoteroSettings)
        const changedPapers = await syncService.getChangedPapers(userId)
        paperIds = changedPapers.map(p => p.id)
      } catch (error) {
        logger.error('Error getting changed papers', { error: error.message, userId })
        return createErrorResponse(
          'Failed to get changed papers',
          500,
          correlationId,
          ['Unable to determine which papers need syncing'],
          'Internal error'
        )
      }
    } else if (data.mode === 'all') {
      // Sync all user papers
      const userPapers = await papers.getByUserId(userId)
      paperIds = userPapers.map(p => p.id)
    } else {
      return createErrorResponse(
        'Invalid sync mode',
        400,
        correlationId,
        ['Mode must be "selected", "changed", or "all"'],
        'Invalid mode'
      )
    }

    if (paperIds.length === 0) {
      return createSuccessResponse({
        message: 'No papers to sync',
        successful: [],
        failed: [],
        total: 0
      }, correlationId)
    }

    try {
      // Create sync service and sync papers
      const syncService = await createZoteroSyncService(zoteroSettings.apiKey!, zoteroSettings)
      const result = await syncService.syncMultiplePapers(paperIds)

      logger.info('Bulk sync completed', { 
        userId, 
        total: result.total,
        successful: result.successful.length,
        failed: result.failed.length
      })

      return createSuccessResponse({
        message: `Sync completed: ${result.successful.length} successful, ${result.failed.length} failed`,
        successful: result.successful,
        failed: result.failed,
        total: result.total
      }, correlationId)
    } catch (error) {
      logger.error('Error during bulk Zotero sync', { error: error.message, userId })
      return createErrorResponse(
        'Bulk sync failed',
        500,
        correlationId,
        ['An unexpected error occurred during bulk sync'],
        'Internal sync error'
      )
    }
  } catch (error: any) {
    logger.error('Error in bulk Zotero sync endpoint', { error: error.message, userId })
    return createErrorResponse(
      'Internal server error',
      500,
      correlationId,
      undefined,
      'Failed to sync papers to Zotero'
    )
  }
}, {
  body: z.object({
    mode: z.enum(['selected', 'changed', 'all']),
    paperIds: z.array(z.string()).min(1).optional()
  }).refine((data) => {
    // If mode is 'selected', paperIds is required
    if (data.mode === 'selected') {
      return data.paperIds && data.paperIds.length > 0
    }
    return true
  }, {
    message: "paperIds is required when mode is 'selected'"
  })
}, {
  allowUnverified: true,
  auditLog: {
    action: 'papers_bulk_zotero_sync',
    resourceType: 'paper'
  }
})
