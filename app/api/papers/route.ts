import { NextRequest, NextResponse } from "next/server"
import { papers, reviews } from "@/lib/database"
import { withAuthSecurity, withPublicSecurity } from "@/lib/security-middleware"
import { createSuccessResponse, createErrorResponse } from "@/lib/validation"
import { validationSchemas } from "@/lib/validation-schemas"
import { withIdempotency } from "@/lib/idempotency"
import { parsePaginationParams, parseSortingParams, applySorting, applyPagination, createListResponse, SORT_FIELD_MAPPINGS } from "@/lib/pagination"
import { createPaperWithReview } from "@/lib/transaction-wrapper"
import type { Paper } from "@/lib/types"

export const GET = withAuthSecurity(async (request: NextRequest, { correlationId, validatedData, user, userId, logger }) => {
  try {
    logger.info('Fetching user papers', { userId, query: validatedData.query })

    // Parse pagination and sorting parameters
    const pagination = parsePaginationParams(request, 20)
    const sorting = parseSortingParams(request, SORT_FIELD_MAPPINGS.papers.allowed, SORT_FIELD_MAPPINGS.papers.default)

    // Get filter parameters
    const { search, starred, tag, year } = validatedData.query || {}

    // Get papers for the authenticated user
    const userPapers = await papers.getByUserId(userId)

    // Apply filters
    let filteredPapers = userPapers

    if (search) {
      const searchLower = search.toLowerCase()
      filteredPapers = filteredPapers.filter(paper =>
        paper.title.toLowerCase().includes(searchLower) ||
        paper.authors.some(author => author.toLowerCase().includes(searchLower)) ||
        paper.abstract?.toLowerCase().includes(searchLower)
      )
    }

    if (starred !== undefined) {
      filteredPapers = filteredPapers.filter(paper => paper.starred === starred)
    }

    if (tag) {
      filteredPapers = filteredPapers.filter(paper =>
        paper.tags.some(paperTag => paperTag.toLowerCase().includes(tag.toLowerCase()))
      )
    }

    if (year) {
      filteredPapers = filteredPapers.filter(paper => paper.year === year)
    }

    // Apply sorting and pagination
    const sortedPapers = applySorting(filteredPapers, sorting, (a, b, field, order) => {
      const aVal = (a as any)[field]
      const bVal = (b as any)[field]

      // Handle null/undefined
      if (aVal == null && bVal == null) return 0
      if (aVal == null) return 1
      if (bVal == null) return -1

      // Special handling for arrays (like authors)
      if (Array.isArray(aVal) && Array.isArray(bVal)) {
        const aStr = aVal.join(', ')
        const bStr = bVal.join(', ')
        const comparison = aStr.localeCompare(bStr)
        return order === 'desc' ? -comparison : comparison
      }

      // Default comparison
      let comparison = 0
      if (typeof aVal === 'string' && typeof bVal === 'string') {
        comparison = aVal.localeCompare(bVal)
      } else if (typeof aVal === 'number' && typeof bVal === 'number') {
        comparison = aVal - bVal
      } else {
        comparison = String(aVal).localeCompare(String(bVal))
      }

      return order === 'desc' ? -comparison : comparison
    })
    const paginatedPapers = applyPagination(sortedPapers, pagination)

    const response = createListResponse(paginatedPapers, pagination, filteredPapers.length, sorting)

    return createSuccessResponse(response.data, correlationId, {
      pagination: response.pagination,
      sorting: response.sorting
    })
  } catch (error: any) {
    logger.error('Error fetching papers', { error: error.message, userId })
    return NextResponse.json({ error: "Failed to fetch papers", correlationId }, { status: 500 })
  }
}, {
  query: validationSchemas.paper.list
}, {
  allowUnverified: true,
  auditLog: {
    action: 'papers_list',
    resourceType: 'paper'
  }
})

export const POST = withIdempotency(withAuthSecurity(async (request: NextRequest, { correlationId, validatedData, user, userId, logger }) => {
  try {
    const data = validatedData.body

    logger.info('Creating new paper', { userId, title: data.title })

    const paper: Paper = {
      id: crypto.randomUUID(),
      title: data.title,
      authors: data.authors || [],
      venue: data.venue,
      year: data.year,
      doi: data.doi,
      url: data.url,
      abstract: data.abstract,
      citationCount: data.citationCount,
      referenceCount: data.referenceCount,
      publicationDate: data.publicationDate,
      journal: data.journal,
      volume: data.volume,
      issue: data.issue,
      pages: data.pages,
      tags: data.tags || [],
      starred: false,
      userId: userId, // Associate paper with authenticated user
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      paperType: data.paperType || 'journalArticle',
    }

    // Use transaction to ensure both paper and review are created atomically
    const initialReview = {
      ease: 2.5,
      nextDue: new Date().toISOString(), // Due immediately for first review
      lastInterval: 1
    }

    const createdPaper = await createPaperWithReview(paper, initialReview)
    logger.info('Paper and initial review created successfully', { paperId: createdPaper.id, userId })

    logger.info('Paper created successfully', { paperId: createdPaper.id, userId })

    return createSuccessResponse(createdPaper, correlationId)
  } catch (error) {
    logger.error('Error creating paper', { error: error.message, userId })
    return createErrorResponse(
      'Internal server error',
      500,
      correlationId,
      undefined,
      'Failed to create paper'
    )
  }
}, {
  body: validationSchemas.paper.create,
  maxBodySize: 50 * 1024 // 50KB limit for paper creation
}, {
  allowUnverified: true,
  auditLog: {
    action: 'paper_create',
    resourceType: 'paper',
    getResourceId: (request, routeParams) => 'new'
  }
}), {
  // Idempotency options
  keyGenerator: (request) => {
    const idempotencyKey = request.headers.get('idempotency-key')
    const userId = request.headers.get('x-user-id') || 'anonymous'

    if (idempotencyKey) {
      return `paper-create:${userId}:${idempotencyKey}`
    }

    // If no explicit idempotency key, generate a unique key based on timestamp
    // This allows multiple papers to be created without interference
    return `paper-create:${userId}:auto:${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
  }
})
