import { NextRequest, NextResponse } from "next/server"
import { findBestMatch, getPaperByDOI, convertSemanticScholarPaper } from "@/lib/semantic-scholar"
import { withAuthSecurity } from "@/lib/security-middleware"
import { validationSchemas } from "@/lib/validation-schemas"
import { createZoteroSyncService, getZoteroSettingsFromPreferences } from "@/lib/zotero-sync-service"
import { collections } from "@/lib/database"
import { ZoteroSettings } from "@/lib/types"

/**
 * Get Zotero settings for enrichment, considering collection-specific configurations
 */
async function getZoteroSettingsForEnrichment(
  userPreferences: Record<string, any>,
  collectionId: string | undefined,
  userId: string,
  logger: any
): Promise<ZoteroSettings> {
  logger.info('Starting Zotero settings retrieval', {
    userId,
    collectionId,
    hasUserPreferences: !!userPreferences,
    userPreferencesStructure: userPreferences ? {
      hasZotero: !!userPreferences.zotero,
      zoteroKeys: userPreferences.zotero ? Object.keys(userPreferences.zotero) : []
    } : null
  })

  // Start with global user settings
  const globalSettings = getZoteroSettingsFromPreferences(userPreferences)

  logger.info('Global Zotero settings parsed', {
    userId,
    enabled: globalSettings.enabled,
    hasApiKey: !!globalSettings.apiKey,
    apiKeyLength: globalSettings.apiKey?.length,
    libraryType: globalSettings.libraryType,
    libraryId: globalSettings.libraryId
  })

  // If no collection specified, use global settings
  if (!collectionId) {
    logger.info('No collection context, using global Zotero settings', {
      userId,
      enabled: globalSettings.enabled,
      hasApiKey: !!globalSettings.apiKey,
      libraryType: globalSettings.libraryType
    })
    return globalSettings
  }

  try {
    logger.info('Fetching collection for Zotero settings', { userId, collectionId })

    // Get the collection to check for specific Zotero configuration
    const collection = await collections.getById(collectionId)

    if (!collection) {
      logger.warn('Collection not found, using global settings', {
        userId,
        collectionId,
        globalSettingsEnabled: globalSettings.enabled
      })
      return globalSettings
    }

    logger.info('Collection retrieved', {
      userId,
      collectionId,
      collectionName: collection.name,
      collectionUserId: collection.userId,
      hasZoteroLibraryType: !!collection.zoteroLibraryType,
      zoteroLibraryType: collection.zoteroLibraryType,
      hasZoteroLibraryId: !!collection.zoteroLibraryId,
      zoteroLibraryId: collection.zoteroLibraryId
    })

    // Check if collection has specific Zotero destination configured
    if (collection.zoteroLibraryType &&
        (collection.zoteroLibraryType === 'user' || collection.zoteroLibraryId)) {

      const collectionSettings: ZoteroSettings = {
        ...globalSettings,
        libraryType: collection.zoteroLibraryType,
        libraryId: collection.zoteroLibraryType === 'user' ? 'user' : collection.zoteroLibraryId!
      }

      logger.info('Using collection-specific Zotero settings', {
        userId,
        collectionId,
        collectionName: collection.name,
        libraryType: collectionSettings.libraryType,
        libraryId: collectionSettings.libraryId,
        enabled: collectionSettings.enabled,
        hasApiKey: !!collectionSettings.apiKey,
        apiKeyLength: collectionSettings.apiKey?.length
      })

      return collectionSettings
    }

    logger.info('Collection has no specific Zotero config, using global settings', {
      userId,
      collectionId,
      collectionName: collection.name,
      globalEnabled: globalSettings.enabled
    })
    return globalSettings

  } catch (error: any) {
    logger.error('Error getting collection for Zotero settings, falling back to global', {
      userId,
      collectionId,
      error: error.message,
      errorStack: error.stack,
      errorName: error.name
    })
    return globalSettings
  }
}

export const POST = withAuthSecurity(async (request: NextRequest, { correlationId, validatedData, user, userId, logger }) => {
  try {
    // Log the complete request payload for debugging
    logger.info('Paper enrichment request received', {
      userId,
      correlationId,
      requestBody: validatedData.body,
      userPreferences: user.preferences,
      timestamp: new Date().toISOString()
    })

    const { title, authors, doi, collectionId } = validatedData.body

    if (!title && !doi) {
      logger.error('Validation failed: Missing required fields', {
        userId,
        correlationId,
        title: !!title,
        doi: !!doi,
        requestBody: validatedData.body
      })
      return NextResponse.json({ error: "Title or DOI is required", correlationId }, { status: 400 })
    }

    logger.info('Enriching paper with priority system', {
      userId,
      title,
      authors,
      doi,
      collectionId,
      hasCollectionContext: !!collectionId,
      correlationId
    })

    let enrichedMetadata = null
    let source = null
    let zoteroLibraryInfo = null

    // STEP 1: Try to find in Zotero first (if configured)
    try {
      logger.info('Getting Zotero settings for enrichment', {
        userId,
        correlationId,
        collectionId,
        hasUserPreferences: !!user.preferences,
        userPreferencesKeys: user.preferences ? Object.keys(user.preferences) : []
      })

      const zoteroSettings = await getZoteroSettingsForEnrichment(
        user.preferences || {},
        collectionId,
        userId,
        logger
      )

      logger.info('Zotero settings retrieved', {
        userId,
        correlationId,
        enabled: zoteroSettings.enabled,
        hasApiKey: !!zoteroSettings.apiKey,
        apiKeyLength: zoteroSettings.apiKey?.length,
        libraryType: zoteroSettings.libraryType,
        libraryId: zoteroSettings.libraryId,
        collectionId
      })

      if (zoteroSettings.enabled && zoteroSettings.apiKey) {
        logger.info('Attempting Zotero enrichment', {
          userId,
          correlationId,
          hasApiKey: !!zoteroSettings.apiKey,
          libraryType: zoteroSettings.libraryType,
          libraryId: zoteroSettings.libraryId
        })

        const syncService = await createZoteroSyncService(zoteroSettings.apiKey, zoteroSettings)

        logger.info('Searching paper in Zotero', {
          userId,
          correlationId,
          title,
          hasDoi: !!doi,
          authorsCount: authors?.length || 0
        })

        const zoteroResult = await syncService.searchPaperInZotero(title, doi, authors)

        if (zoteroResult) {
          enrichedMetadata = zoteroResult.metadata
          source = zoteroResult.source
          zoteroLibraryInfo = {
            libraryType: zoteroResult.libraryType,
            libraryId: zoteroResult.libraryId,
            libraryName: zoteroResult.libraryName
          }

          logger.info('Paper found in Zotero', {
            userId,
            correlationId,
            source: zoteroResult.source,
            libraryType: zoteroResult.libraryType,
            libraryName: zoteroResult.libraryName,
            metadataFields: Object.keys(zoteroResult.metadata || {})
          })
        } else {
          logger.info('Paper not found in Zotero, will try Semantic Scholar', {
            userId,
            correlationId,
            title,
            doi
          })
        }
      } else {
        logger.info('Zotero not configured, skipping to Semantic Scholar', {
          userId,
          correlationId,
          enabled: zoteroSettings.enabled,
          hasApiKey: !!zoteroSettings.apiKey,
          reason: !zoteroSettings.enabled ? 'disabled' : 'no_api_key'
        })
      }
    } catch (zoteroError: any) {
      logger.error('Zotero search failed, falling back to Semantic Scholar', {
        userId,
        correlationId,
        collectionId,
        error: zoteroError.message,
        errorStack: zoteroError.stack,
        errorName: zoteroError.name,
        zoteroEnabled: zoteroSettings?.enabled,
        hasApiKey: !!zoteroSettings?.apiKey,
        libraryType: zoteroSettings?.libraryType,
        libraryId: zoteroSettings?.libraryId,
        title,
        doi
      })
    }

    // STEP 2: Fall back to Semantic Scholar if not found in Zotero
    if (!enrichedMetadata) {
      logger.info('Attempting Semantic Scholar enrichment', { userId })

      let semanticScholarPaper = null

      // Try to find by DOI first if available
      if (doi) {
        semanticScholarPaper = await getPaperByDOI(doi)
      }

      // If not found by DOI, try searching by title
      if (!semanticScholarPaper && title) {
        semanticScholarPaper = await findBestMatch(title, authors)
      }

      if (semanticScholarPaper) {
        enrichedMetadata = convertSemanticScholarPaper(semanticScholarPaper)
        source = "Semantic Scholar"

        logger.info('Paper found in Semantic Scholar', { userId })
      }
    }

    // STEP 3: Return results
    if (!enrichedMetadata) {
      logger.info('Paper not found in any source', { userId })
      return NextResponse.json({
        message: "Paper not found in Zotero or Semantic Scholar",
        enriched: false,
        metadata: {},
        source: null
      })
    }

    const response = {
      message: "Paper enriched successfully",
      enriched: true,
      metadata: enrichedMetadata,
      source
    }

    // Add Zotero library info if found in Zotero
    if (zoteroLibraryInfo) {
      response.zoteroLibrary = zoteroLibraryInfo
    }

    logger.info('Successfully enriched paper with metadata', {
      userId,
      source,
      citationCount: enrichedMetadata.citationCount,
      referenceCount: enrichedMetadata.referenceCount,
      doi: enrichedMetadata.doi,
      zoteroLibrary: zoteroLibraryInfo?.libraryName
    })

    return NextResponse.json(response)

  } catch (error: any) {
    logger.error("Critical error in paper enrichment", {
      error: error.message,
      errorName: error.name,
      errorStack: error.stack,
      userId,
      correlationId,
      title,
      doi,
      collectionId,
      requestBody: validatedData.body,
      userPreferences: user.preferences,
      timestamp: new Date().toISOString()
    })

    // Provide more specific error messages based on the error type
    let errorMessage = "Failed to enrich paper metadata"
    let statusCode = 500

    if (error.message?.includes('Zotero API error')) {
      errorMessage = "Failed to enrich paper: Zotero API error"
      statusCode = 400
      logger.error("Zotero API specific error", {
        userId,
        correlationId,
        zoteroError: error.message,
        title,
        doi,
        collectionId
      })
    } else if (error.message?.includes('validation')) {
      errorMessage = "Failed to enrich paper: Invalid request data"
      statusCode = 400
      logger.error("Validation specific error", {
        userId,
        correlationId,
        validationError: error.message,
        requestBody: validatedData.body
      })
    } else if (error.message?.includes('Collection not found')) {
      errorMessage = "Failed to enrich paper: Collection not found"
      statusCode = 404
      logger.error("Collection not found error", {
        userId,
        correlationId,
        collectionId,
        error: error.message
      })
    }

    return NextResponse.json({
      error: errorMessage,
      enriched: false,
      metadata: {},
      source: null,
      correlationId,
      details: error.message,
      debugInfo: {
        errorType: error.name,
        timestamp: new Date().toISOString(),
        hasCollectionContext: !!collectionId
      }
    }, { status: statusCode })
  }
}, {
  body: validationSchemas.paper.enrich
}, {
  allowUnverified: true,
  auditLog: {
    action: 'paper_enrich',
    resourceType: 'paper'
  }
})
