import { type NextRequest, NextResponse } from "next/server"
import { reviews, papers } from "@/lib/database"
import { withAuth, canAccessResource } from "@/lib/auth-middleware"

export const POST = withAuth(async (request: NextRequest, { user, userId }, { params }: { params: { id: string } }) => {
  try {
    const paperId = params.id

    // Verify the paper belongs to the user
    const paper = await papers.getById(paperId)
    if (!paper) {
      return NextResponse.json({ error: "Paper not found" }, { status: 404 })
    }

    if (!canAccessResource(user, paper.userId)) {
      return NextResponse.json({ error: "Access denied" }, { status: 403 })
    }

    // Check if review already exists
    const existingReview = await reviews.getByPaperId(paperId)
    
    if (existingReview) {
      // Update existing review to be due immediately
      const updatedReview = await reviews.update(paperId, {
        nextDue: new Date().toISOString(),
        ease: existingReview.ease, // Keep existing ease factor
      })
      
      return NextResponse.json({ 
        message: "Paper added to review queue", 
        review: updatedReview,
        wasExisting: true
      })
    } else {
      // Create new review entry (without lastInterval for now)
      const newReview = {
        paperId,
        ease: 2.5, // Default ease factor
        nextDue: new Date().toISOString(), // Due immediately
      }
      
      const createdReview = await reviews.create(newReview)

      return NextResponse.json({
        message: "Paper added to review queue",
        review: createdReview,
        wasExisting: false
      })
    }
  } catch (error) {
    console.error("Error adding paper to review:", error)

    // Check if it's a missing column error
    if (error.message && error.message.includes('column "last_interval"')) {
      return NextResponse.json({
        error: "Database schema needs to be updated. Please run the migration script.",
        details: "Missing last_interval column in reviews table"
      }, { status: 500 })
    }

    return NextResponse.json({ error: "Failed to add paper to review" }, { status: 500 })
  }
}, { allowUnverified: true })

export const DELETE = withAuth(async (request: NextRequest, { user, userId }, { params }: { params: { id: string } }) => {
  try {
    const paperId = params.id

    // Verify the paper belongs to the user
    const paper = await papers.getById(paperId)
    if (!paper) {
      return NextResponse.json({ error: "Paper not found" }, { status: 404 })
    }

    if (!canAccessResource(user, paper.userId)) {
      return NextResponse.json({ error: "Access denied" }, { status: 403 })
    }

    // Remove paper from review queue
    const deleted = await reviews.delete(paperId)
    
    if (deleted) {
      return NextResponse.json({ message: "Paper removed from review queue" })
    } else {
      return NextResponse.json({ error: "Paper not found in review queue" }, { status: 404 })
    }
  } catch (error) {
    console.error("Error removing paper from review:", error)
    return NextResponse.json({ error: "Failed to remove paper from review" }, { status: 500 })
  }
}, { allowUnverified: true })

export const GET = withAuth(async (request: NextRequest, { user, userId }, { params }: { params: { id: string } }) => {
  try {
    const paperId = params.id

    // Verify the paper belongs to the user
    const paper = await papers.getById(paperId)
    if (!paper) {
      return NextResponse.json({ error: "Paper not found" }, { status: 404 })
    }

    if (!canAccessResource(user, paper.userId)) {
      return NextResponse.json({ error: "Access denied" }, { status: 403 })
    }

    // Get review status for paper
    const review = await reviews.getByPaperId(paperId)
    
    if (review) {
      const now = new Date()
      const dueDate = new Date(review.nextDue)
      const isDue = dueDate <= now
      
      return NextResponse.json({ 
        inReview: true,
        review,
        isDue,
        daysUntilDue: Math.ceil((dueDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24))
      })
    } else {
      return NextResponse.json({ 
        inReview: false,
        review: null,
        isDue: false,
        daysUntilDue: null
      })
    }
  } catch (error) {
    console.error("Error getting paper review status:", error)
    return NextResponse.json({ error: "Failed to get review status" }, { status: 500 })
  }
}, { allowUnverified: true })
