import { NextRequest, NextResponse } from "next/server"
import { papers } from "@/lib/database"
import { withAuthSecurity } from "@/lib/security-middleware"
import { validationSchemas } from "@/lib/validation-schemas"

export const GET = withAuthSecurity(async (request: NextRequest, { correlationId, validatedData, user, userId, logger }, { params }: { params: Promise<{ id: string }> }) => {
  try {
    const { id } = await params
    logger.info('Fetching paper', { userId, paperId: id })

    const paper = await papers.getById(id)
    if (!paper) {
      return NextResponse.json({ error: "Paper not found", correlationId }, { status: 404 })
    }

    // Check if user can access this paper
    if (paper.userId !== userId) {
      logger.warn('Access denied to paper', { userId, paperId: id, paperUserId: paper.userId })
      return NextResponse.json({ error: "Access denied", correlationId }, { status: 403 })
    }

    return NextResponse.json(paper)
  } catch (error: any) {
    logger.error('Error fetching paper', { error: error.message, userId })
    return NextResponse.json({ error: "Failed to fetch paper", correlationId }, { status: 500 })
  }
}, {
  params: validationSchemas.paper.params
}, {
  allowUnverified: true,
  auditLog: {
    action: 'paper_get',
    resourceType: 'paper'
  }
})

export const PUT = withAuthSecurity(async (request: NextRequest, { correlationId, validatedData, user, userId, logger }, { params }: { params: Promise<{ id: string }> }) => {
  try {
    const { id } = await params
    logger.info('Updating paper', { userId, paperId: id })

    const paper = await papers.getById(id)
    if (!paper) {
      return NextResponse.json({ error: "Paper not found", correlationId }, { status: 404 })
    }

    // Check if user can modify this paper
    if (paper.userId !== userId) {
      logger.warn('Access denied to update paper', { userId, paperId: id, paperUserId: paper.userId })
      return NextResponse.json({ error: "Access denied", correlationId }, { status: 403 })
    }

    const data = validatedData.body
    const updated = await papers.update(id, {
      ...data,
      updatedAt: new Date().toISOString(),
    })

    return NextResponse.json({ data: updated })
  } catch (error: any) {
    logger.error('Error updating paper', { error: error.message, userId })
    return NextResponse.json({ error: "Failed to update paper", correlationId }, { status: 500 })
  }
}, {
  params: validationSchemas.paper.params,
  body: validationSchemas.paper.update
}, {
  allowUnverified: true,
  auditLog: {
    action: 'paper_update',
    resourceType: 'paper'
  }
})

export const DELETE = withAuthSecurity(async (request: NextRequest, { correlationId, validatedData, user, userId, logger }, { params }: { params: Promise<{ id: string }> }) => {
  try {
    const { id } = await params
    logger.info('Deleting paper', { userId, paperId: id })

    const paper = await papers.getById(id)
    if (!paper) {
      return NextResponse.json({ error: "Paper not found", correlationId }, { status: 404 })
    }

    // Check if user can delete this paper
    if (paper.userId !== userId) {
      logger.warn('Access denied to delete paper', { userId, paperId: id, paperUserId: paper.userId })
      return NextResponse.json({ error: "Access denied", correlationId }, { status: 403 })
    }

    await papers.delete(id)
    // Also delete associated note and reviews (handled by CASCADE in database)
    // The database foreign key constraints will automatically delete related records

    return NextResponse.json({ data: { success: true } })
  } catch (error: any) {
    logger.error('Error deleting paper', { error: error.message, userId })
    return NextResponse.json({ error: "Failed to delete paper", correlationId }, { status: 500 })
  }
}, {
  params: validationSchemas.paper.params
}, {
  allowUnverified: true,
  auditLog: {
    action: 'paper_delete',
    resourceType: 'paper'
  }
})
