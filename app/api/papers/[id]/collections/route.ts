import { type NextRequest, NextResponse } from "next/server"
import { collections } from "@/lib/database"
import { withAuthSecurity } from "@/lib/security-middleware"
import { validationSchemas } from "@/lib/validation-schemas"

export const GET = withAuthSecurity(async (request: NextRequest, { correlationId, validatedData, user, userId, logger }, { params }: { params: Promise<{ id: string }> }) => {
  try {
    const { id } = await params
    logger.info('Fetching paper collections', { userId, paperId: id })

    // Get collections for the authenticated user only
    const userCollections = await collections.getByUserId(userId)
    const paperCollections = userCollections.filter((c) => c.paperIds.includes(id))

    return NextResponse.json(paperCollections)
  } catch (error: any) {
    logger.error("Error fetching paper collections", { error: error.message, userId })
    return NextResponse.json({ error: "Failed to fetch paper collections", correlationId }, { status: 500 })
  }
}, {
  params: validationSchemas.paper.params
}, {
  allowUnverified: true,
  auditLog: {
    action: 'paper_collections_list',
    resourceType: 'paper'
  }
})
