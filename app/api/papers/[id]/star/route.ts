import { type NextRequest, NextResponse } from "next/server"
import { papers } from "@/lib/database"
import { withAuth, canAccessResource } from "@/lib/auth-middleware"

export const POST = withAuth(async (request: NextRequest, { user, userId }, { params }: { params: { id: string } }) => {
  try {
    const paper = await papers.getById(params.id)
    if (!paper) {
      return NextResponse.json({ error: "Paper not found" }, { status: 404 })
    }

    // Check if user can modify this paper
    if (!canAccessResource(user, paper.userId)) {
      return NextResponse.json({ error: "Access denied" }, { status: 403 })
    }

    const updated = await papers.update(params.id, {
      starred: !paper.starred,
      updatedAt: new Date().toISOString(),
    })

    return NextResponse.json(updated)
  } catch (error) {
    console.error("Error updating paper star status:", error)
    return NextResponse.json({ error: "Failed to update paper" }, { status: 500 })
  }
}, { allowUnverified: true })
