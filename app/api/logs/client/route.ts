import { NextRequest, NextResponse } from 'next/server'

/**
 * API endpoint to receive client-side logs and forward them to Docker container logs
 */
export async function POST(request: NextRequest) {
  try {
    const logEntry = await request.json()

    // Validate log entry structure
    if (!logEntry.type || !logEntry.level) {
      return NextResponse.json(
        { error: 'Invalid log entry format' },
        { status: 400 }
      )
    }

    // Add client-side indicator to the log entry
    const enhancedLogEntry = {
      level: logEntry.level,
      message: `Frontend ${logEntry.type}: ${logEntry.message || 'No message'}`,
      timestamp: new Date().toISOString(),
      source: 'frontend',
      userAgent: request.headers.get('user-agent'),
      clientIp: request.headers.get('x-forwarded-for') ||
                request.headers.get('x-real-ip') ||
                'unknown',
      ...logEntry
    }

    // Log to server console - this will appear in Docker logs
    console.log(JSON.stringify(enhancedLogEntry))

    return NextResponse.json({ success: true })
  } catch (error) {
    // Don't log this error to avoid infinite loops
    console.error('Client logging endpoint error:', error)
    return NextResponse.json(
      { error: 'Failed to process log entry' },
      { status: 500 }
    )
  }
}
