import { NextRequest, NextResponse } from 'next/server'
import { users, auditLogs } from '@/lib/database'
import { hashPassword, verifyPassword, isValidPassword } from '@/lib/auth'
import { withAuth, getClientIP, getUserAgent } from '@/lib/auth-middleware'
import type { ChangePasswordRequest } from '@/lib/types'

export const POST = withAuth(async (request: NextRequest, { user, userId }) => {
  try {
    const body: ChangePasswordRequest = await request.json()
    const { currentPassword, newPassword } = body

    // Validate input
    if (!currentPassword || !newPassword) {
      return NextResponse.json(
        { error: 'Current password and new password are required' },
        { status: 400 }
      )
    }

    // Validate new password strength
    const passwordValidation = isValidPassword(newPassword)
    if (!passwordValidation.valid) {
      return NextResponse.json(
        { error: 'New password does not meet requirements', details: passwordValidation.errors },
        { status: 400 }
      )
    }

    // Get user with password hash
    const userWithPassword = await users.getByEmail(user.email)
    if (!userWithPassword) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      )
    }

    // Verify current password
    const isCurrentPasswordValid = await verifyPassword(currentPassword, userWithPassword.passwordHash)
    if (!isCurrentPasswordValid) {
      // Log failed attempt
      await auditLogs.create({
        userId,
        action: 'password_change_failed',
        resourceType: 'user',
        resourceId: userId,
        ipAddress: getClientIP(request),
        userAgent: getUserAgent(request),
        details: { reason: 'invalid_current_password' },
      })

      return NextResponse.json(
        { error: 'Current password is incorrect' },
        { status: 400 }
      )
    }

    // Check if new password is different from current
    const isSamePassword = await verifyPassword(newPassword, userWithPassword.passwordHash)
    if (isSamePassword) {
      return NextResponse.json(
        { error: 'New password must be different from current password' },
        { status: 400 }
      )
    }

    // Hash the new password
    const newPasswordHash = await hashPassword(newPassword)

    // Update user's password
    await users.update(userId, {
      passwordHash: newPasswordHash,
    })

    // Log successful password change
    await auditLogs.create({
      userId,
      action: 'password_changed',
      resourceType: 'user',
      resourceId: userId,
      ipAddress: getClientIP(request),
      userAgent: getUserAgent(request),
      details: { email: user.email },
    })

    return NextResponse.json({
      message: 'Password changed successfully',
    })

  } catch (error) {
    console.error('Change password error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}, { allowUnverified: true })