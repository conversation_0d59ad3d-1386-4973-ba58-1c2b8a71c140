import { NextRequest, NextResponse } from 'next/server'
import { users, passwordResetTokens, auditLogs } from '@/lib/database'
import { hashPassword, isValidPassword, hashToken, isTokenExpired } from '@/lib/auth'
import { getClientIP, getUserAgent } from '@/lib/auth-middleware'
import type { PasswordResetConfirm } from '@/lib/types'

export async function POST(request: NextRequest) {
  try {
    const body: PasswordResetConfirm = await request.json()
    const { token, newPassword } = body

    // Validate input
    if (!token || !newPassword) {
      return NextResponse.json(
        { error: 'Token and new password are required' },
        { status: 400 }
      )
    }

    // Validate password strength
    const passwordValidation = isValidPassword(newPassword)
    if (!passwordValidation.valid) {
      return NextResponse.json(
        { error: 'Password does not meet requirements', details: passwordValidation.errors },
        { status: 400 }
      )
    }

    // Hash the token to find it in database
    const tokenHash = hashToken(token)

    // Find the reset token
    const resetToken = await passwordResetTokens.getByTokenHash(tokenHash)
    if (!resetToken) {
      return NextResponse.json(
        { error: 'Invalid or expired reset token' },
        { status: 400 }
      )
    }

    // Check if token has expired
    if (isTokenExpired(resetToken.expiresAt)) {
      // Clean up expired token
      await passwordResetTokens.markAsUsed(tokenHash)
      return NextResponse.json(
        { error: 'Reset token has expired' },
        { status: 400 }
      )
    }

    // Check if token has already been used
    if (resetToken.used) {
      return NextResponse.json(
        { error: 'Reset token has already been used' },
        { status: 400 }
      )
    }

    // Get the user
    const user = await users.getById(resetToken.userId)
    if (!user || !user.isActive) {
      return NextResponse.json(
        { error: 'User not found or inactive' },
        { status: 400 }
      )
    }

    // Hash the new password
    const passwordHash = await hashPassword(newPassword)

    // Update user's password
    await users.update(user.id, {
      passwordHash,
    })

    // Mark token as used
    await passwordResetTokens.markAsUsed(tokenHash)

    // Log the password reset
    const clientIp = getClientIP(request)
    const userAgent = getUserAgent(request)

    await auditLogs.create({
      userId: user.id,
      action: 'password_reset_completed',
      resourceType: 'user',
      resourceId: user.id,
      ipAddress: clientIp,
      userAgent,
      details: { email: user.email },
    })

    return NextResponse.json({
      message: 'Password has been reset successfully. You can now sign in with your new password.',
    })

  } catch (error) {
    console.error('Reset password error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}