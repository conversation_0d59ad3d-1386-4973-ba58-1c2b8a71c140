import { NextRequest, NextResponse } from 'next/server'
import { users, emailVerificationTokens, auditLogs } from '@/lib/database'
import { isTokenExpired, hashToken } from '@/lib/auth'
import { getClientIP, getUserAgent } from '@/lib/auth-middleware'
import { getBaseUrlFromRequest } from '@/lib/config'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const token = searchParams.get('token')

    // Get the base URL from request headers for proper redirects
    const baseUrl = getBaseUrlFromRequest(request)

    if (!token) {
      // Redirect to login with error
      return NextResponse.redirect(new URL('/login?verify=invalid', baseUrl))
    }

    // Hash the token to find it in database
    const tokenHash = hashToken(token)

    // Find the verification token
    const verificationToken = await emailVerificationTokens.getByTokenHash(tokenHash)
    if (!verificationToken) {
      // Redirect to login with error
      return NextResponse.redirect(new URL('/login?verify=invalid', baseUrl))
    }

    // Check if token has expired
    if (isTokenExpired(verificationToken.expiresAt)) {
      // Mark token as used to prevent reuse
      await emailVerificationTokens.markAsUsed(tokenHash)
      // Redirect to login with expired error
      return NextResponse.redirect(new URL('/login?verify=expired', baseUrl))
    }

    // Check if token has already been used
    if (verificationToken.used) {
      // Redirect to login with error
      return NextResponse.redirect(new URL('/login?verify=invalid', baseUrl))
    }

    // Get the user
    const user = await users.getById(verificationToken.userId)
    if (!user || !user.isActive) {
      // Redirect to login with error
      return NextResponse.redirect(new URL('/login?verify=invalid', baseUrl))
    }

    // Check if user is already verified
    if (user.emailVerified) {
      // Mark token as used and redirect to login
      await emailVerificationTokens.markAsUsed(tokenHash)
      return NextResponse.redirect(new URL('/login?verify=already', baseUrl))
    }

    // Mark user as verified
    await users.update(user.id, {
      emailVerified: true,
    })

    // Mark token as used
    await emailVerificationTokens.markAsUsed(tokenHash)

    // Log the email verification
    const clientIp = getClientIP(request)
    const userAgent = getUserAgent(request)
    
    await auditLogs.create({
      userId: user.id,
      action: 'email_verified',
      resourceType: 'user',
      resourceId: user.id,
      ipAddress: clientIp,
      userAgent,
      details: { email: user.email },
    })

    // Redirect to login with success
    return NextResponse.redirect(new URL('/login?verify=ok', baseUrl))

  } catch (error) {
    console.error('Email verification error:', error)
    // Get base URL for error redirect
    const baseUrl = getBaseUrlFromRequest(request)
    // Redirect to login with error
    return NextResponse.redirect(new URL('/login?verify=error', baseUrl))
  }
}
