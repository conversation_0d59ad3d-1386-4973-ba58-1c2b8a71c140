import { NextRequest, NextResponse } from 'next/server'
import { users, emailVerificationTokens, auditLogs } from '@/lib/database'
import { hashPassword, isValidEmail, isValidPassword, generateSecureToken, hashToken, generateTokenExpiry, sanitizeUser } from '@/lib/auth'
import { sendVerificationEmail } from '@/lib/mailer'
import { getClientIP, getUserAgent } from '@/lib/auth-middleware'
import { withPublicSecurity } from '@/lib/security-middleware'
import { createSuccessResponse, createErrorResponse } from '@/lib/validation'
import { validationSchemas } from '@/lib/validation-schemas'
import { registrationLimiter, generateRateLimitKey, logSecurityEvent } from '@/lib/rate-limiter'
import { getBaseUrlFromRequest } from '@/lib/config'
import type { RegisterRequest } from '@/lib/types'

export const POST = withPublicSecurity(async (request: NextRequest, { correlationId, validatedData, logger }) => {
  try {
    const { email, password, displayName } = validatedData.body

    logger.info('Registration attempt', { email })

    // Validate email format
    if (!isValidEmail(email)) {
      return createErrorResponse(
        'Invalid email format',
        400,
        correlationId
      )
    }

    // Validate password strength
    const passwordValidation = isValidPassword(password)
    if (!passwordValidation.valid) {
      return createErrorResponse(
        'Password does not meet requirements',
        400,
        correlationId,
        passwordValidation.errors
      )
    }

    // Check if user already exists
    const existingUser = await users.getByEmail(email.toLowerCase())
    if (existingUser) {
      logger.warn('Registration attempt with existing email', { email })
      return createErrorResponse(
        'User with this email already exists',
        409,
        correlationId
      )
    }

    // Hash password
    const passwordHash = await hashPassword(password)

    // Create user
    const newUser = await users.create({
      email: email.toLowerCase(),
      passwordHash,
      displayName: displayName || null,
      role: 'user',
      emailVerified: false,
      isActive: true,
      privacySettings: {},
      preferences: {},
    })

    // Generate email verification token
    const verificationToken = generateSecureToken()
    const tokenHash = hashToken(verificationToken)
    const expiresAt = generateTokenExpiry(24) // 24 hours

    await emailVerificationTokens.create({
      userId: newUser.id,
      tokenHash,
      expiresAt: expiresAt.toISOString(),
      used: false,
    })

    // Send verification email with dynamic base URL
    try {
      const baseUrl = getBaseUrlFromRequest(request)
      await sendVerificationEmail(newUser.email, verificationToken, baseUrl)
    } catch (emailError) {
      logger.error('Failed to send verification email', { error: emailError.message, email: newUser.email })
      // Continue with registration even if email fails
      // User can request resend later
    }

    logger.info('User registered successfully', { userId: newUser.id, email: newUser.email })

    // Return success (don't include sensitive data)
    return createSuccessResponse({
      message: 'User registered successfully. Please check your email for verification.',
      user: sanitizeUser(newUser),
      verificationRequired: true,
    }, correlationId, 201)

  } catch (error: any) {
    logger.error('Registration error', { error: error.message })
    return createErrorResponse(
      'Internal server error',
      500,
      correlationId,
      undefined,
      'Failed to register user'
    )
  }
}, {
  body: validationSchemas.auth.register,
  maxBodySize: 10 * 1024 // 10KB limit for registration
}, {
  rateLimitConfig: {
    maxRequests: 3,
    windowMs: 60 * 60 * 1000, // 1 hour
    message: 'Too many registration attempts. Please try again later.'
  },
  auditLog: {
    action: 'user_register',
    resourceType: 'user',
    getResourceId: (request, routeParams) => 'new'
  }
})