import { NextRequest, NextResponse } from 'next/server'
import { userSessions, auditLogs } from '@/lib/database'
import { generateSessionTokenHash, getUserIdFromToken } from '@/lib/auth'

export async function POST(request: NextRequest) {
  try {
    // Get token from Authorization header or cookie
    const authHeader = request.headers.get('authorization')
    const cookieToken = request.cookies.get('auth-token')?.value

    const token = authHeader?.replace('Bearer ', '') || cookieToken

    if (!token) {
      return NextResponse.json(
        { error: 'No token provided' },
        { status: 401 }
      )
    }

    // Get user ID from token
    const userId = getUserIdFromToken(token)
    if (!userId) {
      return NextResponse.json(
        { error: 'Invalid token' },
        { status: 401 }
      )
    }

    // Generate token hash for database lookup
    const tokenHash = generateSessionTokenHash(token)

    // Delete session from database
    await userSessions.deleteByTokenHash(tokenHash)

    // Log the logout
    const clientIp = request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown'
    const userAgent = request.headers.get('user-agent') || 'unknown'

    await auditLogs.create({
      userId,
      action: 'logout',
      resourceType: 'user',
      resourceId: userId,
      ipAddress: clientIp,
      userAgent,
      details: {},
    })

    // Create response
    const response = NextResponse.json({
      message: 'Logout successful',
    })

    // Clear the auth cookie
    response.cookies.set('auth-token', '', {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: 0, // Expire immediately
      path: '/',
    })

    return response

  } catch (error) {
    console.error('Logout error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}