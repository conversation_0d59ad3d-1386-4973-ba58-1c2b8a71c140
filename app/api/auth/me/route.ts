import { NextRequest, NextResponse } from 'next/server'
import { users, userSessions } from '@/lib/database'
import { generateSessionTokenHash, getUserIdFromToken, isTokenExpired, sanitizeUser } from '@/lib/auth'

export async function GET(request: NextRequest) {
  try {
    // Get token from Authorization header or cookie
    const authHeader = request.headers.get('authorization')
    const cookieToken = request.cookies.get('auth-token')?.value

    const token = authHeader?.replace('Bearer ', '') || cookieToken

    if (!token) {
      return NextResponse.json(
        { error: 'No token provided' },
        { status: 401 }
      )
    }

    // Get user ID from token
    const userId = getUserIdFromToken(token)
    if (!userId) {
      return NextResponse.json(
        { error: 'Invalid token' },
        { status: 401 }
      )
    }

    // Verify session exists in database
    const tokenHash = generateSessionTokenHash(token)
    const session = await userSessions.getByTokenHash(tokenHash)

    if (!session) {
      return NextResponse.json(
        { error: 'Session not found' },
        { status: 401 }
      )
    }

    // Check if session has expired
    if (isTokenExpired(session.expiresAt)) {
      // Clean up expired session
      await userSessions.deleteByTokenHash(tokenHash)
      return NextResponse.json(
        { error: 'Session expired' },
        { status: 401 }
      )
    }

    // Get user data
    const user = await users.getById(userId)
    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      )
    }

    // Check if user is still active
    if (!user.isActive) {
      return NextResponse.json(
        { error: 'Account is deactivated' },
        { status: 401 }
      )
    }

    return NextResponse.json({
      user: sanitizeUser(user),
      session: {
        expiresAt: session.expiresAt,
        createdAt: session.createdAt,
      },
    })

  } catch (error) {
    console.error('Get current user error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}