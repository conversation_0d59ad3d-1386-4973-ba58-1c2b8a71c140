import { NextRequest, NextResponse } from 'next/server'
import { users, passwordResetTokens, auditLogs } from '@/lib/database'
import { isValidEmail, generateSecureToken, hashToken, generateTokenExpiry } from '@/lib/auth'
import { sendPasswordResetEmail } from '@/lib/mailer'
import { getClientIP, getUserAgent } from '@/lib/auth-middleware'
import { getBaseUrlFromRequest } from '@/lib/config'
import type { PasswordResetRequest } from '@/lib/types'

export async function POST(request: NextRequest) {
  try {
    const body: PasswordResetRequest = await request.json()
    const { email } = body

    // Validate input
    if (!email) {
      return NextResponse.json(
        { error: 'Email is required' },
        { status: 400 }
      )
    }

    // Validate email format
    if (!isValidEmail(email)) {
      return NextResponse.json(
        { error: 'Invalid email format' },
        { status: 400 }
      )
    }

    // Find user by email
    const user = await users.getByEmail(email.toLowerCase())

    // Always return success to prevent email enumeration attacks
    // But only send email if user exists
    if (user && user.isActive) {
      // Generate password reset token
      const resetToken = generateSecureToken()
      const tokenHash = hashToken(resetToken)
      const expiresAt = generateTokenExpiry(1) // 1 hour expiry

      // Store token in database
      await passwordResetTokens.create({
        userId: user.id,
        tokenHash,
        expiresAt: expiresAt.toISOString(),
        used: false,
      })

      // Log the password reset request
      const clientIp = getClientIP(request)
      const userAgent = getUserAgent(request)

      await auditLogs.create({
        userId: user.id,
        action: 'password_reset_requested',
        resourceType: 'user',
        resourceId: user.id,
        ipAddress: clientIp,
        userAgent,
        details: { email: user.email },
      })

      // Send password reset email with dynamic base URL
      try {
        const baseUrl = getBaseUrlFromRequest(request)
        await sendPasswordResetEmail(user.email, resetToken, baseUrl)
      } catch (emailError) {
        console.error('Failed to send password reset email:', emailError)
        // Continue with the flow even if email fails
        // User will see success message but won't receive email
      }
    }

    // Always return success message
    return NextResponse.json({
      message: 'If an account with that email exists, we have sent a password reset link.',
    })

  } catch (error) {
    console.error('Forgot password error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}