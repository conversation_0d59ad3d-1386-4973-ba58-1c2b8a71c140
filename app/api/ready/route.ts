import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/db'
import { withPublicSecurity } from '@/lib/security-middleware'
import { createSuccessResponse, createErrorResponse } from '@/lib/validation'

/**
 * Readiness endpoint - checks if the application is ready to serve traffic
 * This is different from health - readiness means all dependencies are available
 */

interface ReadinessCheck {
  service: string
  status: 'ready' | 'not_ready'
  message?: string
  responseTime?: number
}

async function checkDatabaseReadiness(): Promise<ReadinessCheck> {
  try {
    const start = Date.now()
    
    // Check if we can connect and query the database
    await query('SELECT 1')
    
    // Check if essential tables exist
    const tablesResult = await query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      AND table_name IN ('users', 'papers', 'collections')
    `)
    
    const responseTime = Date.now() - start
    const expectedTables = ['users', 'papers', 'collections']
    const existingTables = tablesResult.rows.map(row => row.table_name)
    const missingTables = expectedTables.filter(table => !existingTables.includes(table))
    
    if (missingTables.length > 0) {
      return {
        service: 'database',
        status: 'not_ready',
        message: `Missing tables: ${missingTables.join(', ')}`,
        responseTime
      }
    }
    
    return {
      service: 'database',
      status: 'ready',
      message: 'Database connection and schema validated',
      responseTime
    }
  } catch (error) {
    return {
      service: 'database',
      status: 'not_ready',
      message: `Database error: ${error.message}`
    }
  }
}

async function checkEnvironmentReadiness(): Promise<ReadinessCheck> {
  const requiredEnvVars = [
    'DATABASE_URL',
    'JWT_SECRET',
    'NEXTAUTH_SECRET'
  ]
  
  const missingVars = requiredEnvVars.filter(varName => !process.env[varName])
  
  if (missingVars.length > 0) {
    return {
      service: 'environment',
      status: 'not_ready',
      message: `Missing environment variables: ${missingVars.join(', ')}`
    }
  }
  
  return {
    service: 'environment',
    status: 'ready',
    message: 'All required environment variables are set'
  }
}

async function checkMemoryReadiness(): Promise<ReadinessCheck> {
  const memUsage = process.memoryUsage()
  const usedMemory = memUsage.heapUsed
  const totalMemory = memUsage.heapTotal
  const usagePercent = (usedMemory / totalMemory) * 100
  
  // Consider not ready if memory usage is critically high
  if (usagePercent > 95) {
    return {
      service: 'memory',
      status: 'not_ready',
      message: `Critical memory usage: ${Math.round(usagePercent)}%`
    }
  }
  
  return {
    service: 'memory',
    status: 'ready',
    message: `Memory usage: ${Math.round(usagePercent)}%`
  }
}

export const GET = withPublicSecurity(async (request: NextRequest, { correlationId, logger }) => {
  try {
    logger.info('Readiness check requested')
    
    // Run all readiness checks in parallel
    const [dbCheck, envCheck, memCheck] = await Promise.all([
      checkDatabaseReadiness(),
      checkEnvironmentReadiness(),
      checkMemoryReadiness()
    ])
    
    const checks = [dbCheck, envCheck, memCheck]
    const notReadyChecks = checks.filter(check => check.status === 'not_ready')
    const isReady = notReadyChecks.length === 0
    
    const readinessStatus = {
      status: isReady ? 'ready' : 'not_ready',
      timestamp: new Date().toISOString(),
      checks,
      summary: {
        total: checks.length,
        ready: checks.filter(c => c.status === 'ready').length,
        notReady: notReadyChecks.length
      }
    }
    
    const statusCode = isReady ? 200 : 503
    
    if (isReady) {
      logger.info('Application is ready', { checks: checks.length })
      return createSuccessResponse(readinessStatus, correlationId)
    } else {
      logger.warn('Application is not ready', { 
        notReadyServices: notReadyChecks.map(c => c.service),
        issues: notReadyChecks.map(c => c.message)
      })
      
      return NextResponse.json(
        {
          error: 'Application not ready',
          data: readinessStatus,
          correlationId,
          timestamp: new Date().toISOString()
        },
        { 
          status: statusCode,
          headers: {
            'Cache-Control': 'no-cache, no-store, must-revalidate',
            'Pragma': 'no-cache',
            'Expires': '0',
            'X-Correlation-ID': correlationId
          }
        }
      )
    }
    
  } catch (error) {
    logger.error('Readiness check failed', { error: error.message })
    return createErrorResponse(
      'Readiness check failed',
      503,
      correlationId,
      undefined,
      'Unable to determine application readiness'
    )
  }
}, {}, 'apiGeneral')
