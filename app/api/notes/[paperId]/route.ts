import { type NextRequest, NextResponse } from "next/server"
import { notes, papers } from "@/lib/database"
import { withAuth, canAccessResource } from "@/lib/auth-middleware"

export const GET = withAuth(async (request: NextRequest, { user, userId }, { params }: { params: Promise<{ paperId: string }> }) => {
  try {
    const { paperId } = await params
    // First check if user can access the paper
    const paper = await papers.getById(paperId)
    if (!paper) {
      return NextResponse.json({ error: "Paper not found" }, { status: 404 })
    }

    if (!canAccessResource(user, paper.userId)) {
      return NextResponse.json({ error: "Access denied" }, { status: 403 })
    }

    const note = await notes.getByPaperId(paperId)
    if (!note) {
      return NextResponse.json({ error: "Note not found" }, { status: 404 })
    }
    return NextResponse.json(note)
  } catch (error) {
    console.error("Error fetching note:", error)
    return NextResponse.json({ error: "Failed to fetch note" }, { status: 500 })
  }
}, { allowUnverified: true })

export const PUT = withAuth(async (request: NextRequest, { user, userId }, { params }: { params: Promise<{ paperId: string }> }) => {
  try {
    const { paperId } = await params
    // First check if user can access the paper
    const paper = await papers.getById(paperId)
    if (!paper) {
      return NextResponse.json({ error: "Paper not found" }, { status: 404 })
    }

    if (!canAccessResource(user, paper.userId)) {
      return NextResponse.json({ error: "Access denied" }, { status: 403 })
    }

    const data = await request.json()
    let note = await notes.getByPaperId(paperId)

    if (!note) {
      // Create new note
      note = {
        id: crypto.randomUUID(),
        paperId: paperId,
        quickSummary: data.quickSummary || "",
        keyIdeas: data.keyIdeas || ["", "", ""],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      }
      const createdNote = await notes.create(note)
      return NextResponse.json(createdNote)
    } else {
      // Update existing note
      const updatedNote = await notes.update(paperId, {
        ...data,
        updatedAt: new Date().toISOString(),
      })
      return NextResponse.json(updatedNote)
    }
  } catch (error) {
    console.error("Error updating note:", error)
    return NextResponse.json({ error: "Failed to update note" }, { status: 500 })
  }
}, { allowUnverified: true })
