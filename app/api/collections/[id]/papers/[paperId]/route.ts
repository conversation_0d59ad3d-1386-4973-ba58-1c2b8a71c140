import { type NextRequest, NextResponse } from "next/server"
import { collections } from "@/lib/database"

export async function POST(request: NextRequest, { params }: { params: Promise<{ id: string; paperId: string }> }) {
  try {
    const { id, paperId } = await params
    const collection = await collections.getById(id)
    if (!collection) {
      return NextResponse.json({ error: "Collection not found" }, { status: 404 })
    }

    if (!collection.paperIds.includes(paperId)) {
      const updated = await collections.update(id, {
        paperIds: [...collection.paperIds, paperId],
      })
      return NextResponse.json(updated)
    }

    return NextResponse.json(collection)
  } catch (error) {
    console.error("Error adding paper to collection:", error)
    return NextResponse.json({ error: "Failed to add paper to collection" }, { status: 500 })
  }
}

export async function DELETE(request: NextRequest, { params }: { params: Promise<{ id: string; paperId: string }> }) {
  try {
    const { id, paperId } = await params
    const collection = await collections.getById(id)
    if (!collection) {
      return NextResponse.json({ error: "Collection not found" }, { status: 404 })
    }

    const updated = await collections.update(id, {
      paperIds: collection.paperIds.filter((pid) => pid !== paperId),
    })

    return NextResponse.json(updated)
  } catch (error) {
    console.error("Error removing paper from collection:", error)
    return NextResponse.json({ error: "Failed to remove paper from collection" }, { status: 500 })
  }
}
