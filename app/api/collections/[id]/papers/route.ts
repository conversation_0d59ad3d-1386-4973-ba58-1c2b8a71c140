import { type NextRequest, NextResponse } from "next/server"
import { collections, papers } from "@/lib/database"
import { withAuthSecurity } from "@/lib/security-middleware"
import { validationSchemas } from "@/lib/validation-schemas"

export const GET = withAuthSecurity(async (request: NextRequest, { correlationId, validatedData, user, userId, logger }, { params }: { params: Promise<{ id: string }> }) => {
  try {
    const { id } = await params
    logger.info('Fetching collection papers', { userId, collectionId: id })

    const collection = await collections.getById(id)
    if (!collection) {
      return NextResponse.json({ error: "Collection not found", correlationId }, { status: 404 })
    }

    // Check if user can access this collection
    if (collection.userId !== userId) {
      logger.warn('Access denied to collection', { userId, collectionId: id, collectionUserId: collection.userId })
      return NextResponse.json({ error: "Access denied", correlationId }, { status: 403 })
    }

    const collectionPapers = await Promise.all(
      collection.paperIds.map(async (id) => await papers.getById(id))
    )

    return NextResponse.json(collectionPapers.filter(Boolean))
  } catch (error: any) {
    logger.error('Error fetching collection papers', { error: error.message, userId })
    return NextResponse.json({ error: "Failed to fetch collection papers", correlationId }, { status: 500 })
  }
}, {
  params: validationSchemas.collection.params
}, {
  allowUnverified: true,
  auditLog: {
    action: 'collection_papers_list',
    resourceType: 'collection'
  }
})

export const POST = withAuthSecurity(async (request: NextRequest, { correlationId, validatedData, user, userId, logger }, { params }: { params: Promise<{ id: string }> }) => {
  try {
    const { id } = await params
    const data = validatedData.body

    logger.info('Adding papers to collection', { userId, collectionId: id, paperIds: data.paperIds })

    const collection = await collections.getById(id)
    if (!collection) {
      return NextResponse.json({ error: "Collection not found", correlationId }, { status: 404 })
    }

    // Check if user can modify this collection
    if (collection.userId !== userId) {
      logger.warn('Access denied to modify collection', { userId, collectionId: id, collectionUserId: collection.userId })
      return NextResponse.json({ error: "Access denied", correlationId }, { status: 403 })
    }

    // Verify all papers exist and belong to the user
    const paperChecks = await Promise.all(
      data.paperIds.map(async (paperId: string) => {
        const paper = await papers.getById(paperId)
        return {
          paperId,
          exists: !!paper,
          belongsToUser: paper ? paper.userId === userId : false
        }
      })
    )

    const invalidPapers = paperChecks.filter(check => !check.exists)
    if (invalidPapers.length > 0) {
      return NextResponse.json({
        error: "Some papers not found",
        invalidPapers: invalidPapers.map(p => p.paperId),
        correlationId
      }, { status: 400 })
    }

    const unauthorizedPapers = paperChecks.filter(check => check.exists && !check.belongsToUser)
    if (unauthorizedPapers.length > 0) {
      return NextResponse.json({
        error: "Cannot add papers that don't belong to you",
        unauthorizedPapers: unauthorizedPapers.map(p => p.paperId),
        correlationId
      }, { status: 403 })
    }

    // Add papers to collection (avoid duplicates)
    const existingPaperIds = new Set(collection.paperIds)
    const newPaperIds = data.paperIds.filter((id: string) => !existingPaperIds.has(id))
    const updatedPaperIds = [...collection.paperIds, ...newPaperIds]

    const updatedCollection = await collections.update(id, {
      paperIds: updatedPaperIds
    })

    return NextResponse.json({
      data: {
        message: "Papers added to collection",
        collection: updatedCollection,
        addedCount: newPaperIds.length,
        skippedCount: data.paperIds.length - newPaperIds.length
      }
    })
  } catch (error: any) {
    logger.error('Error adding papers to collection', { error: error.message, userId })
    return NextResponse.json({ error: "Failed to add papers to collection", correlationId }, { status: 500 })
  }
}, {
  params: validationSchemas.collection.params,
  body: validationSchemas.collection.addPapers
}, {
  allowUnverified: true,
  auditLog: {
    action: 'collection_papers_add',
    resourceType: 'collection'
  }
})
