import { type NextRequest, NextResponse } from "next/server"
import { collections, notes } from "@/lib/database"
import { withAuthSecurity } from "@/lib/security-middleware"
import { validationSchemas } from "@/lib/validation-schemas"

export const GET = withAuthSecurity(async (request: NextRequest, { correlationId, validatedData, user, userId, logger }, { params }: { params: { id: string } }) => {
  try {
    logger.info('Fetching collection notes', { userId, collectionId: params.id })

    const collection = await collections.getById(params.id)
    if (!collection) {
      return NextResponse.json({ error: "Collection not found", correlationId }, { status: 404 })
    }

    // Check if user can access this collection
    if (collection.userId !== userId) {
      logger.warn('Access denied to collection', { userId, collectionId: params.id, collectionUserId: collection.userId })
      return NextResponse.json({ error: "Access denied", correlationId }, { status: 403 })
    }

    const collectionNotes = await Promise.all(
      collection.paperIds.map(async (paperId) => await notes.getByPaperId(paperId))
    )

    return NextResponse.json(collectionNotes.filter(Boolean))
  } catch (error: any) {
    logger.error("Error fetching collection notes", { error: error.message, userId, collectionId: params.id })
    return NextResponse.json({ error: "Failed to fetch collection notes", correlationId }, { status: 500 })
  }
}, {
  params: validationSchemas.collection.params
}, {
  allowUnverified: true,
  auditLog: {
    action: 'collection_notes_list',
    resourceType: 'collection'
  }
})
