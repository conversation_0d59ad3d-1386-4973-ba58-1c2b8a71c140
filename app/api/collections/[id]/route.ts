import { type NextRequest, NextResponse } from "next/server"
import { collections } from "@/lib/database"
import { withAuthSecurity } from "@/lib/security-middleware"
import { validationSchemas } from "@/lib/validation-schemas"

export const GET = withAuthSecurity(async (request: NextRequest, { correlationId, validatedData, user, userId, logger }, { params }: { params: Promise<{ id: string }> }) => {
  try {
    const { id } = await params
    logger.info('Fetching collection', { userId, collectionId: id })

    const collection = await collections.getById(id)
    if (!collection) {
      return NextResponse.json({ error: "Collection not found", correlationId }, { status: 404 })
    }

    // Check if user can access this collection
    if (collection.userId !== userId) {
      logger.warn('Access denied to collection', { userId, collectionId: id, collectionUserId: collection.userId })
      return NextResponse.json({ error: "Access denied", correlationId }, { status: 403 })
    }

    return NextResponse.json(collection)
  } catch (error: any) {
    logger.error('Error fetching collection', { error: error.message, userId })
    return NextResponse.json({ error: "Failed to fetch collection", correlationId }, { status: 500 })
  }
}, {
  params: validationSchemas.collection.params
}, {
  allowUnverified: true,
  auditLog: {
    action: 'collection_get',
    resourceType: 'collection'
  }
})

export const PUT = withAuthSecurity(async (request: NextRequest, { correlationId, validatedData, user, userId, logger }, { params }: { params: Promise<{ id: string }> }) => {
  try {
    const { id } = await params
    logger.info('Updating collection', { userId, collectionId: id })

    const collection = await collections.getById(id)
    if (!collection) {
      return NextResponse.json({ error: "Collection not found", correlationId }, { status: 404 })
    }

    // Check if user can modify this collection
    if (collection.userId !== userId) {
      logger.warn('Access denied to update collection', { userId, collectionId: id, collectionUserId: collection.userId })
      return NextResponse.json({ error: "Access denied", correlationId }, { status: 403 })
    }

    const data = validatedData.body
    const updated = await collections.update(id, {
      ...data,
      updatedAt: new Date().toISOString(),
    })

    return NextResponse.json({ data: updated })
  } catch (error: any) {
    logger.error('Error updating collection', { error: error.message, userId })
    return NextResponse.json({ error: "Failed to update collection", correlationId }, { status: 500 })
  }
}, {
  params: validationSchemas.collection.params,
  body: validationSchemas.collection.update
}, {
  allowUnverified: true,
  auditLog: {
    action: 'collection_update',
    resourceType: 'collection'
  }
})

export const DELETE = withAuthSecurity(async (request: NextRequest, { correlationId, validatedData, user, userId, logger }, { params }: { params: Promise<{ id: string }> }) => {
  try {
    const { id } = await params
    logger.info('Deleting collection', { userId, collectionId: id })

    const collection = await collections.getById(id)
    if (!collection) {
      return NextResponse.json({ error: "Collection not found", correlationId }, { status: 404 })
    }

    // Check if user can delete this collection
    if (collection.userId !== userId) {
      logger.warn('Access denied to delete collection', { userId, collectionId: id, collectionUserId: collection.userId })
      return NextResponse.json({ error: "Access denied", correlationId }, { status: 403 })
    }

    await collections.delete(id)
    return NextResponse.json({ data: { success: true } })
  } catch (error: any) {
    logger.error('Error deleting collection', { error: error.message, userId })
    return NextResponse.json({ error: "Failed to delete collection", correlationId }, { status: 500 })
  }
}, {
  params: validationSchemas.collection.params
}, {
  allowUnverified: true,
  auditLog: {
    action: 'collection_delete',
    resourceType: 'collection'
  }
})
