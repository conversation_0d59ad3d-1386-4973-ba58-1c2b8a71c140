import { type NextRequest, NextResponse } from "next/server"
import { collections } from "@/lib/database"
import { withAuthSecurity } from "@/lib/security-middleware"
import { parsePaginationParams, parseSortingParams, applySorting, applyPagination, createListResponse, SORT_FIELD_MAPPINGS } from "@/lib/pagination"
import { createSuccessResponse } from "@/lib/validation"
import { validationSchemas } from "@/lib/validation-schemas"
import type { Collection } from "@/lib/types"

export const GET = withAuthSecurity(async (request: NextRequest, { correlationId, validatedData, user, userId, logger }) => {
  try {
    logger.info('Fetching user collections', { userId })

    // Parse pagination and sorting parameters
    const pagination = parsePaginationParams(request, 20)
    const sorting = parseSortingParams(request, SORT_FIELD_MAPPINGS.collections.allowed, SORT_FIELD_MAPPINGS.collections.default)

    // Get collections for the authenticated user
    const userCollections = await collections.getByUserId(userId)

    // Apply sorting and pagination
    const sortedCollections = applySorting(userCollections, sorting)
    const paginatedCollections = applyPagination(sortedCollections, pagination)

    const response = createListResponse(paginatedCollections, pagination, userCollections.length, sorting)

    return createSuccessResponse(response.data, correlationId, {
      pagination: response.pagination,
      sorting: response.sorting
    })
  } catch (error: any) {
    logger.error('Error fetching collections', { error: error.message, userId })
    return NextResponse.json({ error: "Failed to fetch collections", correlationId }, { status: 500 })
  }
}, {
  query: validationSchemas.collection.list
}, {
  allowUnverified: true,
  auditLog: {
    action: 'collections_list',
    resourceType: 'collection'
  }
})

export const POST = withAuthSecurity(async (request: NextRequest, { correlationId, validatedData, user, userId, logger }) => {
  try {
    const data = validatedData.body

    const collection: Collection = {
      id: crypto.randomUUID(),
      name: data.name,
      paperIds: [],
      userId: userId, // Associate collection with authenticated user
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      zoteroLibraryType: data.zoteroLibraryType,
      zoteroLibraryId: data.zoteroLibraryId,
    }

    const createdCollection = await collections.create(collection)
    return NextResponse.json({ data: createdCollection }, { status: 201 })
  } catch (error: any) {
    logger.error("Error creating collection", { error: error.message, userId })
    return NextResponse.json({ error: "Failed to create collection", correlationId }, { status: 500 })
  }
}, {
  body: validationSchemas.collection.create
})
