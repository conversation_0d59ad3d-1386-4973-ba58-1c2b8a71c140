import { NextRequest, NextResponse } from 'next/server'
import { config } from '@/lib/config'
import { withPublicSecurity } from '@/lib/security-middleware'
import { createSuccessResponse } from '@/lib/validation'

/**
 * Public configuration endpoint
 * Returns non-sensitive configuration values that the frontend needs
 */
export const GET = withPublicSecurity(async (request: NextRequest, { correlationId, logger }) => {
  try {
    logger.info('Public configuration requested')
    
    // Only return non-sensitive configuration values
    const publicConfig = {
      kofiUrl: config.kofiUrl || null,
      // Add other public config values here as needed
    }
    
    return createSuccessResponse(
      publicConfig,
      'Configuration retrieved successfully',
      correlationId
    )
  } catch (error) {
    logger.error('Error fetching public configuration', { error })
    return NextResponse.json(
      { 
        error: 'Failed to fetch configuration',
        correlationId 
      }, 
      { status: 500 }
    )
  }
})
