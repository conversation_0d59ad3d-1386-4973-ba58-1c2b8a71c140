import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import { users, auditLogs } from '@/lib/database'
import { withAuthSecurity } from '@/lib/security-middleware'
import { validationSchemas } from '@/lib/validation-schemas'
import { createSuccessResponse, createErrorResponse } from '@/lib/validation'
import { getClientIP, getUserAgent } from '@/lib/auth-middleware'
import { createZoteroClient } from '@/lib/zotero-client'
import { getZoteroSettingsFromPreferences, validateZoteroSettings } from '@/lib/zotero-sync-service'
import type { ZoteroSettings } from '@/lib/types'

// Helper function to mask API key with correct length
function maskApiKey(apiKey: string): string {
  return '*'.repeat(apiKey.length)
}

// Helper function to check if a string is a masked API key (all asterisks)
function isMaskedApiKey(value: string): boolean {
  return /^\*+$/.test(value)
}

export const GET = withAuthSecurity(async (request: NextRequest, { correlationId, validatedData, user, userId, logger }) => {
  try {
    logger.info('Fetching Zotero settings', { userId })

    // Check if this is a request for the actual API key (for visibility toggle)
    const url = new URL(request.url)
    const showActual = url.searchParams.get('showActual') === 'true'

    // Get current Zotero settings from user preferences
    const zoteroSettings = getZoteroSettingsFromPreferences(user.preferences || {})

    // Return actual API key only if explicitly requested (for visibility toggle)
    const safeSettings = {
      ...zoteroSettings,
      apiKey: zoteroSettings.apiKey ? (showActual ? zoteroSettings.apiKey : maskApiKey(zoteroSettings.apiKey)) : undefined
    }

    return createSuccessResponse(safeSettings, correlationId)
  } catch (error: any) {
    logger.error('Error fetching Zotero settings', { error: error.message, userId })
    return createErrorResponse(
      'Internal server error',
      500,
      correlationId,
      undefined,
      'Failed to fetch Zotero settings'
    )
  }
}, {}, {
  allowUnverified: true,
  auditLog: {
    action: 'zotero_settings_fetch',
    resourceType: 'user_settings'
  }
})

export const PUT = withAuthSecurity(async (request: NextRequest, { correlationId, validatedData, user, userId, logger }) => {
  try {
    const data = validatedData.body as ZoteroSettings
    logger.info('Updating Zotero settings', { userId, libraryType: data.libraryType })

    // Validate Zotero settings
    const validation = validateZoteroSettings(data)
    if (!validation.valid) {
      return createErrorResponse(
        'Invalid Zotero settings',
        400,
        correlationId,
        validation.errors,
        'Validation failed'
      )
    }

    // Test connection if API key is provided and not a placeholder (non-blocking)
    let connectionTestResult = null
    if (data.apiKey && !isMaskedApiKey(data.apiKey) && data.apiKey.trim() !== '') {
      try {
        const client = createZoteroClient(data.apiKey)
        connectionTestResult = await client.testConnection(data.libraryType, data.libraryId)

        if (!connectionTestResult) {
          logger.warn('Zotero connection test failed', {
            userId,
            message: 'Connection test returned false',
            libraryType: data.libraryType,
            libraryId: data.libraryId
          })
        } else {
          logger.info('Zotero connection test successful', {
            userId,
            libraryType: data.libraryType,
            libraryId: data.libraryId
          })
        }
      } catch (error) {
        logger.warn('Zotero connection test failed with error', {
          error: error.message,
          userId,
          libraryType: data.libraryType,
          libraryId: data.libraryId
        })
        connectionTestResult = false
      }
    }

    // Get current preferences
    const currentPreferences = user.preferences || {}
    
    // Update Zotero settings in preferences
    const updatedPreferences = {
      ...currentPreferences,
      zotero: {
        ...data,
        // Don't store the placeholder - keep existing key if masked input
        apiKey: isMaskedApiKey(data.apiKey || '') ? currentPreferences.zotero?.apiKey : data.apiKey
      }
    }

    // Update user preferences
    const updatedUser = await users.update(userId, {
      preferences: updatedPreferences
    })

    if (!updatedUser) {
      return createErrorResponse(
        'Internal server error',
        500,
        correlationId,
        undefined,
        'Failed to update Zotero settings'
      )
    }

    // Log the settings update
    await auditLogs.create({
      userId,
      action: 'zotero_settings_updated',
      resourceType: 'user_settings',
      resourceId: userId,
      ipAddress: getClientIP(request),
      userAgent: getUserAgent(request),
      details: {
        libraryType: data.libraryType,
        libraryId: data.libraryId,
        enabled: data.enabled,
        hasApiKey: !!data.apiKey
      },
    })

    // Return safe settings (without API key) and connection test result
    const safeSettings = getZoteroSettingsFromPreferences(updatedUser.preferences || {})
    const responseSettings = {
      ...safeSettings,
      apiKey: safeSettings.apiKey ? maskApiKey(safeSettings.apiKey) : undefined
    }

    const response = {
      settings: responseSettings,
      connectionTest: connectionTestResult
    }

    return createSuccessResponse(response, correlationId)
  } catch (error: any) {
    logger.error('Error updating Zotero settings', { error: error.message, userId })
    return createErrorResponse(
      'Internal server error',
      500,
      correlationId,
      undefined,
      'Failed to update Zotero settings'
    )
  }
}, {
  body: z.object({
    apiKey: z.string().optional(),
    libraryType: z.enum(['user', 'group']),
    libraryId: z.string().optional(),
    enabled: z.boolean()
  })
}, {
  allowUnverified: true,
  auditLog: {
    action: 'zotero_settings_update',
    resourceType: 'user_settings'
  }
})

export const DELETE = withAuthSecurity(async (request: NextRequest, { correlationId, validatedData, user, userId, logger }) => {
  try {
    logger.info('Deleting Zotero settings', { userId })

    // Get current preferences
    const currentPreferences = user.preferences || {}
    
    // Remove Zotero settings
    const updatedPreferences = {
      ...currentPreferences,
      zotero: {
        enabled: false
      }
    }

    // Update user preferences
    const updatedUser = await users.update(userId, {
      preferences: updatedPreferences
    })

    if (!updatedUser) {
      return createErrorResponse(
        'Internal server error',
        500,
        correlationId,
        undefined,
        'Failed to delete Zotero settings'
      )
    }

    // Log the settings deletion
    await auditLogs.create({
      userId,
      action: 'zotero_settings_deleted',
      resourceType: 'user_settings',
      resourceId: userId,
      ipAddress: getClientIP(request),
      userAgent: getUserAgent(request),
      details: {},
    })

    return createSuccessResponse({ message: 'Zotero settings deleted successfully' }, correlationId)
  } catch (error: any) {
    logger.error('Error deleting Zotero settings', { error: error.message, userId })
    return createErrorResponse(
      'Internal server error',
      500,
      correlationId,
      undefined,
      'Failed to delete Zotero settings'
    )
  }
}, {}, {
  allowUnverified: true,
  auditLog: {
    action: 'zotero_settings_delete',
    resourceType: 'user_settings'
  }
})
