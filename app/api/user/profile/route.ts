import { NextRequest, NextResponse } from 'next/server'
import { users, auditLogs } from '@/lib/database'
import { withAuth, getClientIP, getUserAgent } from '@/lib/auth-middleware'
import { sanitizeUser } from '@/lib/auth'
import type { UserProfileUpdate } from '@/lib/types'

export const GET = withAuth(async (request: NextRequest, { user, userId }) => {
  try {
    // Return the current user's profile
    return NextResponse.json(sanitizeUser(user))
  } catch (error) {
    console.error('Error fetching user profile:', error)
    return NextResponse.json({ error: 'Failed to fetch profile' }, { status: 500 })
  }
}, { allowUnverified: true })

export const PUT = withAuth(async (request: NextRequest, { user, userId }) => {
  try {
    const body: UserProfileUpdate = await request.json()
    const { displayName, preferences, privacySettings } = body

    // Validate input
    if (displayName !== undefined && (!displayName || displayName.trim().length === 0)) {
      return NextResponse.json(
        { error: 'Display name cannot be empty' },
        { status: 400 }
      )
    }

    if (displayName !== undefined && displayName.trim().length > 100) {
      return NextResponse.json(
        { error: 'Display name cannot exceed 100 characters' },
        { status: 400 }
      )
    }

    // Prepare update data
    const updateData: Partial<any> = {}

    if (displayName !== undefined) {
      updateData.displayName = displayName.trim()
    }

    if (preferences !== undefined) {
      updateData.preferences = preferences
    }

    if (privacySettings !== undefined) {
      updateData.privacySettings = privacySettings
    }

    // Update user profile
    const updatedUser = await users.update(userId, updateData)
    if (!updatedUser) {
      return NextResponse.json({ error: 'Failed to update profile' }, { status: 500 })
    }

    // Log the profile update
    await auditLogs.create({
      userId,
      action: 'profile_updated',
      resourceType: 'user',
      resourceId: userId,
      ipAddress: getClientIP(request),
      userAgent: getUserAgent(request),
      details: {
        updatedFields: Object.keys(updateData),
        email: updatedUser.email
      },
    })

    return NextResponse.json(sanitizeUser(updatedUser))
  } catch (error) {
    console.error('Error updating user profile:', error)
    return NextResponse.json({ error: 'Failed to update profile' }, { status: 500 })
  }
}, { allowUnverified: true })