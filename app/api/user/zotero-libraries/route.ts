import { NextRequest, NextResponse } from 'next/server'
import { withAuthSecurity } from '@/lib/security-middleware'
import { createSuccessResponse, createErrorResponse } from '@/lib/validation'
import { createZoteroClient } from '@/lib/zotero-client'
import { getZoteroSettingsFromPreferences } from '@/lib/zotero-sync-service'

export const GET = withAuthSecurity(async (request: NextRequest, { correlationId, validatedData, user, userId, logger }) => {
  try {
    logger.info('Fetching Zotero libraries', { userId })

    // Get current Zotero settings from user preferences
    const zoteroSettings = getZoteroSettingsFromPreferences(user.preferences || {})
    
    if (!zoteroSettings.apiKey) {
      return createErrorResponse(
        'Zotero API key not configured',
        400,
        correlationId,
        ['Please configure your Zotero API key first'],
        'API key required'
      )
    }

    try {
      const client = createZoteroClient(zoteroSettings.apiKey)
      const libraries = await client.getUserLibraries()
      
      return createSuccessResponse(libraries, correlationId)
    } catch (error) {
      logger.error('Error fetching Zotero libraries', { error: error.message, userId })
      return createErrorResponse(
        'Failed to fetch libraries',
        400,
        correlationId,
        ['Unable to connect to Zotero. Please check your API key.'],
        'Zotero API error'
      )
    }
  } catch (error: any) {
    logger.error('Error in Zotero libraries endpoint', { error: error.message, userId })
    return createErrorResponse(
      'Internal server error',
      500,
      correlationId,
      undefined,
      'Failed to fetch Zotero libraries'
    )
  }
}, {}, {
  allowUnverified: true,
  auditLog: {
    action: 'zotero_libraries_fetch',
    resourceType: 'user_settings'
  }
})
