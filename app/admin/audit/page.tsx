'use client'

import React, { useState, useEffect } from 'react'
import { SidebarTrigger } from '@/components/ui/sidebar'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { ProtectedRoute } from '@/components/auth/ProtectedRoute'
import { AuthModal } from '@/components/auth/AuthModal'
import { useAuth } from '@/lib/auth-context'
import { useToast } from '@/hooks/use-toast'
import { authenticatedFetch } from '@/lib/utils'
import { 
  Activity, 
  Search, 
  Shield,
  User,
  FileText,
  BookOpen,
  Calendar,
  ChevronLeft,
  ChevronRight,
  Filter,
  Download
} from 'lucide-react'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import Link from 'next/link'

interface AuditLog {
  id: string
  userId?: string
  action: string
  resourceType?: string
  resourceId?: string
  ipAddress?: string
  userAgent?: string
  details: any
  createdAt: string
}

function AdminAuditContent() {
  const { user } = useAuth()
  const { toast } = useToast()
  const [auditLogs, setAuditLogs] = useState<AuditLog[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [search, setSearch] = useState('')
  const [actionFilter, setActionFilter] = useState('all')
  const [resourceTypeFilter, setResourceTypeFilter] = useState('all')
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 50,
    total: 0,
    totalPages: 0
  })

  useEffect(() => {
    fetchAuditLogs()
  }, [pagination.page, search, actionFilter, resourceTypeFilter])

  const fetchAuditLogs = async () => {
    try {
      setIsLoading(true)
      const params = new URLSearchParams({
        page: pagination.page.toString(),
        limit: pagination.limit.toString(),
        ...(search && { search }),
        ...(actionFilter && actionFilter !== 'all' && { action: actionFilter }),
        ...(resourceTypeFilter && resourceTypeFilter !== 'all' && { resourceType: resourceTypeFilter })
      })

      const response = await authenticatedFetch(`/api/admin/audit?${params}`)
      if (!response.ok) {
        throw new Error('Failed to fetch audit logs')
      }

      const data = await response.json()
      setAuditLogs(data.data.data || [])
      setPagination(data.data.pagination || { page: 1, limit: 50, total: 0, totalPages: 0 })
    } catch (error) {
      console.error('Failed to fetch audit logs:', error)
      toast({
        title: 'Failed to load audit logs',
        description: 'Please try refreshing the page.',
        variant: 'destructive',
      })
    } finally {
      setIsLoading(false)
    }
  }

  const getActionBadge = (action: string) => {
    if (action.includes('admin')) {
      return <Badge variant="destructive">Admin</Badge>
    }
    if (action.includes('create')) {
      return <Badge variant="default">Create</Badge>
    }
    if (action.includes('update')) {
      return <Badge variant="secondary">Update</Badge>
    }
    if (action.includes('delete')) {
      return <Badge variant="destructive">Delete</Badge>
    }
    if (action.includes('view') || action.includes('list')) {
      return <Badge variant="outline">View</Badge>
    }
    return <Badge variant="outline">{action}</Badge>
  }

  const getResourceIcon = (resourceType?: string) => {
    switch (resourceType) {
      case 'user':
        return <User className="h-4 w-4" />
      case 'paper':
        return <FileText className="h-4 w-4" />
      case 'collection':
        return <BookOpen className="h-4 w-4" />
      case 'admin':
        return <Shield className="h-4 w-4" />
      default:
        return <Activity className="h-4 w-4" />
    }
  }

  const formatAction = (action: string) => {
    return action.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
  }

  const exportAuditLogs = () => {
    const csvContent = [
      ['Timestamp', 'User ID', 'Action', 'Resource Type', 'Resource ID', 'IP Address', 'Details'].join(','),
      ...auditLogs.map(log => [
        new Date(log.createdAt).toISOString(),
        log.userId || '',
        log.action,
        log.resourceType || '',
        log.resourceId || '',
        log.ipAddress || '',
        JSON.stringify(log.details).replace(/"/g, '""')
      ].map(field => `"${field}"`).join(','))
    ].join('\n')

    const blob = new Blob([csvContent], { type: 'text/csv' })
    const url = window.URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `audit-logs-${new Date().toISOString().split('T')[0]}.csv`
    a.click()
    window.URL.revokeObjectURL(url)
  }

  if (isLoading && auditLogs.length === 0) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    )
  }

  return (
    <div className="flex flex-col min-h-screen">
      <header className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="container flex h-14 items-center">
          <SidebarTrigger />
          <div className="ml-4 flex items-center gap-2">
            <Shield className="h-5 w-5 text-blue-500" />
            <h1 className="text-lg font-semibold">Audit Logs</h1>
          </div>
        </div>
      </header>

      <main className="flex-1 p-6 space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-2xl font-bold">System Audit Logs</h2>
            <p className="text-muted-foreground">
              Track all administrative and user actions
            </p>
          </div>
          <div className="flex gap-2">
            <Button variant="outline" onClick={exportAuditLogs}>
              <Download className="mr-2 h-4 w-4" />
              Export CSV
            </Button>
            <Link href="/admin">
              <Button variant="outline">
                Back to Dashboard
              </Button>
            </Link>
          </div>
        </div>

        {/* Filters */}
        <Card>
          <CardHeader>
            <CardTitle>Filters</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-wrap gap-4">
              <div className="flex items-center space-x-2">
                <Search className="h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search logs..."
                  value={search}
                  onChange={(e) => setSearch(e.target.value)}
                  className="w-64"
                />
              </div>
              <Select value={actionFilter} onValueChange={setActionFilter}>
                <SelectTrigger className="w-48">
                  <SelectValue placeholder="Filter by action" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Actions</SelectItem>
                  <SelectItem value="admin_">Admin Actions</SelectItem>
                  <SelectItem value="create">Create Actions</SelectItem>
                  <SelectItem value="update">Update Actions</SelectItem>
                  <SelectItem value="delete">Delete Actions</SelectItem>
                  <SelectItem value="view">View Actions</SelectItem>
                </SelectContent>
              </Select>
              <Select value={resourceTypeFilter} onValueChange={setResourceTypeFilter}>
                <SelectTrigger className="w-48">
                  <SelectValue placeholder="Filter by resource" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Resources</SelectItem>
                  <SelectItem value="user">Users</SelectItem>
                  <SelectItem value="paper">Papers</SelectItem>
                  <SelectItem value="collection">Collections</SelectItem>
                  <SelectItem value="admin">Admin</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>

        {/* Audit Logs Table */}
        <Card>
          <CardHeader>
            <CardTitle>Recent Activity ({auditLogs.length} entries)</CardTitle>
            <CardDescription>
              Latest system and administrative actions
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Timestamp</TableHead>
                  <TableHead>Action</TableHead>
                  <TableHead>Resource</TableHead>
                  <TableHead>User</TableHead>
                  <TableHead>Details</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {auditLogs.map((log) => (
                  <TableRow key={log.id}>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Calendar className="h-4 w-4 text-muted-foreground" />
                        <div>
                          <div className="text-sm font-medium">
                            {new Date(log.createdAt).toLocaleDateString()}
                          </div>
                          <div className="text-xs text-muted-foreground">
                            {new Date(log.createdAt).toLocaleTimeString()}
                          </div>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        {getActionBadge(log.action)}
                        <span className="text-sm">{formatAction(log.action)}</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        {getResourceIcon(log.resourceType)}
                        <span className="text-sm">{log.resourceType || 'N/A'}</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <span className="text-sm font-mono">
                        {log.userId ? log.userId.substring(0, 8) + '...' : 'System'}
                      </span>
                    </TableCell>
                    <TableCell>
                      <div className="max-w-xs">
                        {log.details && typeof log.details === 'object' ? (
                          <div className="text-xs text-muted-foreground">
                            {Object.entries(log.details).slice(0, 2).map(([key, value]) => (
                              <div key={key}>
                                <span className="font-medium">{key}:</span> {String(value)}
                              </div>
                            ))}
                            {Object.keys(log.details).length > 2 && (
                              <div className="text-xs text-muted-foreground">
                                +{Object.keys(log.details).length - 2} more
                              </div>
                            )}
                          </div>
                        ) : (
                          <span className="text-xs text-muted-foreground">
                            {String(log.details || 'No details')}
                          </span>
                        )}
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>

            {auditLogs.length === 0 && (
              <div className="text-center py-8">
                <Activity className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                <h3 className="text-lg font-semibold">No audit logs found</h3>
                <p className="text-muted-foreground">
                  No audit logs match your current filters.
                </p>
              </div>
            )}

            {/* Pagination */}
            {pagination.totalPages > 1 && (
              <div className="flex items-center justify-between space-x-2 py-4">
                <div className="text-sm text-muted-foreground">
                  Showing {((pagination.page - 1) * pagination.limit) + 1} to{' '}
                  {Math.min(pagination.page * pagination.limit, pagination.total)} of{' '}
                  {pagination.total} entries
                </div>
                <div className="flex items-center space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setPagination(prev => ({ ...prev, page: prev.page - 1 }))}
                    disabled={pagination.page <= 1}
                  >
                    <ChevronLeft className="h-4 w-4" />
                    Previous
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setPagination(prev => ({ ...prev, page: prev.page + 1 }))}
                    disabled={pagination.page >= pagination.totalPages}
                  >
                    Next
                    <ChevronRight className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </main>
    </div>
  )
}

export default function AdminAuditPage() {
  const [authModalOpen, setAuthModalOpen] = useState(false)

  return (
    <ProtectedRoute
      onAuthRequired={() => setAuthModalOpen(true)}
      requireEmailVerification={false}
      requiredRoles={['admin']}
      fallback={
        <AuthModal
          isOpen={true}
          onClose={() => {
            window.location.href = '/login'
          }}
        />
      }
    >
      <AdminAuditContent />
    </ProtectedRoute>
  )
}
