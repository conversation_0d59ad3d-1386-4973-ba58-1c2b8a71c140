'use client'

import React, { useState, useEffect } from 'react'
import { SidebarTrigger } from '@/components/ui/sidebar'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { ProtectedRoute } from '@/components/auth/ProtectedRoute'
import { AuthModal } from '@/components/auth/AuthModal'
import { useAuth } from '@/lib/auth-context'
import { useToast } from '@/hooks/use-toast'
import { authenticatedFetch } from '@/lib/utils'
import { 
  Users, 
  FileText, 
  BookOpen, 
  BarChart3, 
  TrendingUp, 
  Shield,
  Activity,
  Clock,
  UserCheck,
  UserX
} from 'lucide-react'
import Link from 'next/link'

interface AdminDashboardStats {
  users: {
    total: number
    active: number
    verified: number
    admins: number
    newThisWeek: number
    newThisMonth: number
  }
  content: {
    totalPapers: number
    totalCollections: number
    totalReviews: number
    papersThisWeek: number
    papersThisMonth: number
    collectionsThisWeek: number
    collectionsThisMonth: number
    reviewsThisWeek: number
    reviewsThisMonth: number
  }
  topUsers: Array<{
    id: string
    email: string
    displayName?: string
    paperCount: number
    collectionCount: number
    totalContent: number
  }>
  recentActivity: Array<{
    id: string
    action: string
    resourceType: string
    userId?: string
    createdAt: string
    details: any
  }>
}

function AdminDashboardContent() {
  const { user } = useAuth()
  const { toast } = useToast()
  const [stats, setStats] = useState<AdminDashboardStats | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    fetchDashboardStats()
  }, [])

  const fetchDashboardStats = async () => {
    try {
      const response = await authenticatedFetch('/api/admin/dashboard')
      if (!response.ok) {
        throw new Error('Failed to fetch dashboard stats')
      }
      const data = await response.json()
      setStats(data.data)
    } catch (error) {
      console.error('Failed to fetch dashboard stats:', error)
      toast({
        title: 'Failed to load dashboard',
        description: 'Please try refreshing the page.',
        variant: 'destructive',
      })
    } finally {
      setIsLoading(false)
    }
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    )
  }

  if (!stats) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <h3 className="text-lg font-semibold">Failed to load dashboard</h3>
          <p className="text-muted-foreground">Please try refreshing the page.</p>
          <Button onClick={fetchDashboardStats} className="mt-4">
            Retry
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="flex flex-col min-h-screen">
      <header className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="container flex h-14 items-center">
          <SidebarTrigger />
          <div className="ml-4 flex items-center gap-2">
            <Shield className="h-5 w-5 text-blue-500" />
            <h1 className="text-lg font-semibold">Admin Dashboard</h1>
          </div>
        </div>
      </header>

      <main className="flex-1 p-6 space-y-6">
        {/* Welcome Section */}
        <div className="space-y-2">
          <h2 className="text-2xl font-bold">Welcome, {user?.displayName || 'Administrator'}!</h2>
          <p className="text-muted-foreground">
            System overview and user management dashboard.
          </p>
        </div>

        {/* User Stats Grid */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Users</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.users.total}</div>
              <p className="text-xs text-muted-foreground">
                +{stats.users.newThisMonth} this month
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Active Users</CardTitle>
              <UserCheck className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.users.active}</div>
              <p className="text-xs text-muted-foreground">
                {Math.round((stats.users.active / stats.users.total) * 100)}% of total
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Papers</CardTitle>
              <FileText className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.content.totalPapers}</div>
              <p className="text-xs text-muted-foreground">
                +{stats.content.papersThisMonth} this month
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Collections</CardTitle>
              <BookOpen className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.content.totalCollections}</div>
              <p className="text-xs text-muted-foreground">
                +{stats.content.collectionsThisMonth} this month
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Quick Actions */}
        <Card>
          <CardHeader>
            <CardTitle>Admin Actions</CardTitle>
            <CardDescription>
              Manage users and system settings
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex flex-wrap gap-2">
              <Link href="/admin/users">
                <Button>
                  <Users className="mr-2 h-4 w-4" />
                  Manage Users
                </Button>
              </Link>
              <Link href="/admin/audit">
                <Button variant="outline">
                  <Activity className="mr-2 h-4 w-4" />
                  Audit Logs
                </Button>
              </Link>
              <Link href="/admin/metrics">
                <Button variant="outline">
                  <BarChart3 className="mr-2 h-4 w-4" />
                  System Metrics
                </Button>
              </Link>
            </div>
          </CardContent>
        </Card>

        {/* Top Users and Recent Activity */}
        <div className="grid gap-4 md:grid-cols-2">
          <Card>
            <CardHeader>
              <CardTitle>Top Users by Content</CardTitle>
              <CardDescription>Users with the most papers and collections</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {stats.topUsers.map((user, index) => (
                  <div key={user.id} className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <span className="text-sm font-medium text-muted-foreground">#{index + 1}</span>
                      <div>
                        <p className="text-sm font-medium">{user.displayName || user.email}</p>
                        <p className="text-xs text-muted-foreground">{user.email}</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="text-sm font-medium">{user.totalContent} items</p>
                      <p className="text-xs text-muted-foreground">
                        {user.paperCount}P, {user.collectionCount}C
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Recent Activity</CardTitle>
              <CardDescription>Latest admin and user actions</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {stats.recentActivity.map((activity) => (
                  <div key={activity.id} className="flex items-center gap-3">
                    <Activity className="h-4 w-4 text-muted-foreground" />
                    <div className="flex-1">
                      <p className="text-sm font-medium">{activity.action.replace(/_/g, ' ')}</p>
                      <p className="text-xs text-muted-foreground">
                        {new Date(activity.createdAt).toLocaleString()}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      </main>
    </div>
  )
}

export default function AdminDashboardPage() {
  const [authModalOpen, setAuthModalOpen] = useState(false)

  return (
    <ProtectedRoute
      onAuthRequired={() => setAuthModalOpen(true)}
      requireEmailVerification={false}
      requiredRoles={['admin']}
      fallback={
        <AuthModal
          isOpen={true}
          onClose={() => {
            window.location.href = '/login'
          }}
        />
      }
    >
      <AdminDashboardContent />
    </ProtectedRoute>
  )
}
