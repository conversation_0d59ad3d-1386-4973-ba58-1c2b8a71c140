"use client"

import { useState, useEffect } from "react"
import { Search, Star, Plus, Trash2, BookOpen, BookOpenCheck, ExternalLink, BarChart3, FileText, ArrowUpDown, ArrowUp, ArrowDown, Check, Square, CheckSquare, FolderPlus, RefreshCw, Grid, List, Play, Link as LinkIcon, CheckCircle, AlertCircle, Loader2 } from "lucide-react"
import Link from "next/link"
import { Input } from "@/components/ui/input"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { SidebarTrigger } from "@/components/ui/sidebar"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Checkbox } from "@/components/ui/checkbox"
import { useToast } from "@/hooks/use-toast"
import { ProtectedRoute } from "@/components/auth/ProtectedRoute"
import { AuthModal } from "@/components/auth/AuthModal"
import { PaperCompactView } from "@/components/paper-compact-view"
import { useApiRequest } from "@/hooks/use-logged-fetch"
import { authenticatedFetch, useErrorLogger } from "@/lib/utils"
import type { Paper, Note } from "@/lib/types"

function PapersPageContent() {
  const { toast } = useToast()
  const api = useApiRequest('PapersPageContent')
  const { logError } = useErrorLogger('PapersPageContent')
  const [papers, setPapers] = useState<Paper[]>([])
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedTags, setSelectedTags] = useState<string[]>([])
  const [showStarredOnly, setShowStarredOnly] = useState(false)
  const [reviewStatuses, setReviewStatuses] = useState<Record<string, {
    inReview: boolean
    isDue: boolean
    daysUntilDue: number | null
  }>>({})
  const [showInReviewOnly, setShowInReviewOnly] = useState(false)
  const [sortBy, setSortBy] = useState<'year' | 'title' | 'citations' | 'created'>('year')
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc')
  const [selectedPapers, setSelectedPapers] = useState<Set<string>>(new Set())
  const [isSelectionMode, setIsSelectionMode] = useState(false)
  const [collections, setCollections] = useState<Array<{id: string, name: string}>>([])
  const [showCollectionDialog, setShowCollectionDialog] = useState(false)
  const [viewMode, setViewMode] = useState<"list" | "compact">("list")
  const [notes, setNotes] = useState<Record<string, Note>>({})
  const [isSyncingZotero, setIsSyncingZotero] = useState(false)
  const [zoteroSyncResults, setZoteroSyncResults] = useState<{
    successful: string[]
    failed: Array<{ paperId: string; error: string }>
    total: number
  } | null>(null)
  const [notesLoading, setNotesLoading] = useState(false)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchPapers()
  }, [])

  useEffect(() => {
    if (papers.length > 0) {
      fetchAllReviewStatuses()
    }
  }, [papers])

  useEffect(() => {
    fetchNotesForCompactView()
  }, [viewMode, papers])

  useEffect(() => {
    fetchCollections()
  }, [])

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Don't handle shortcuts when typing in inputs
      const target = e.target as HTMLElement
      if (target.tagName === "INPUT" || target.tagName === "TEXTAREA") {
        return
      }

      // Handle keyboard shortcuts
      switch (e.key.toLowerCase()) {
        case "n":
          e.preventDefault()
          window.location.href = "/papers/new"
          break
        case "escape":
          e.preventDefault()
          // Clear search and filters
          setSearchQuery("")
          setSelectedTags([])
          setShowStarredOnly(false)
          setShowInReviewOnly(false)
          setSortBy('year')
          setSortOrder('desc')
          break
      }
    }

    window.addEventListener("keydown", handleKeyDown)
    return () => window.removeEventListener("keydown", handleKeyDown)
  }, [])

  const fetchPapers = async () => {
    setLoading(true)

    const result = await api.get<any>('/api/papers', {
      authenticated: true,
      onError: (error) => {
        logError('fetch_papers', error)
        if (error.message.includes('401')) {
          toast({
            title: "Authentication required",
            description: "Please log in to view your papers",
            variant: "destructive"
          })
        } else {
          toast({
            title: "Failed to load papers",
            description: "Please try again later",
            variant: "destructive"
          })
        }
        // Set empty array as fallback
        setPapers([])
      },
      onSuccess: (data) => {
        // Handle wrapped response format from createSuccessResponse and createListResponse
        // Structure: { data: { data: papers[], pagination: ..., sorting: ... }, correlationId: ..., timestamp: ... }
        const papers = data.data?.data || data.data || data || []
        setPapers(papers)
      }
    })

    setLoading(false)
  }

  const fetchAllReviewStatuses = async () => {
    try {
      const statuses = await Promise.all(
        papers.map(async (paper) => {
          try {
            const response = await authenticatedFetch(`/api/papers/${paper.id}/review`)
            if (response.ok) {
              const data = await response.json()
              return {
                id: paper.id,
                status: {
                  inReview: data.inReview,
                  isDue: data.isDue,
                  daysUntilDue: data.daysUntilDue
                }
              }
            }
            return { id: paper.id, status: null }
          } catch (error) {
            logError('fetch_review_status', error, { paperId: paper.id })
            return { id: paper.id, status: null }
          }
        })
      )

      const statusMap = statuses.reduce((acc, { id, status }) => {
        if (status) {
          acc[id] = status
        }
        return acc
      }, {} as Record<string, any>)

      setReviewStatuses(statusMap)
    } catch (error) {
      logError('fetch_review_statuses', error)
    }
  }

  const fetchNotesForCompactView = async () => {
    if (viewMode !== "compact" || papers.length === 0) return

    setNotesLoading(true)
    try {
      const notesData = await Promise.all(
        papers.map(async (paper) => {
          try {
            const response = await authenticatedFetch(`/api/notes/${paper.id}`)
            if (response.ok) {
              const note = await response.json()
              return { paperId: paper.id, note }
            }
            return { paperId: paper.id, note: null }
          } catch (error) {
            logError('fetch_note', error, { paperId: paper.id })
            return { paperId: paper.id, note: null }
          }
        })
      )

      const notesMap = notesData.reduce((acc, { paperId, note }) => {
        if (note) {
          acc[paperId] = note
        }
        return acc
      }, {} as Record<string, Note>)

      setNotes(notesMap)
    } catch (error) {
      logError('fetch_notes', error)
    } finally {
      setNotesLoading(false)
    }
  }

  const filteredAndSortedPapers = papers
    .filter((paper) => {
      const matchesSearch =
        paper.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        paper.authors.some((author) => author.toLowerCase().includes(searchQuery.toLowerCase())) ||
        paper.tags.some((tag) => tag.toLowerCase().includes(searchQuery.toLowerCase()))

      const matchesTags = selectedTags.length === 0 || selectedTags.some((tag) => paper.tags.includes(tag))

      const matchesStarred = !showStarredOnly || paper.starred

      const matchesReview = !showInReviewOnly || reviewStatuses[paper.id]?.inReview

      return matchesSearch && matchesTags && matchesStarred && matchesReview
    })
    .sort((a, b) => {
      let comparison = 0

      switch (sortBy) {
        case 'year':
          const yearA = a.year || 0
          const yearB = b.year || 0
          comparison = yearA - yearB
          break
        case 'title':
          comparison = a.title.localeCompare(b.title)
          break
        case 'citations':
          const citationsA = a.citationCount || 0
          const citationsB = b.citationCount || 0
          comparison = citationsA - citationsB
          break
        case 'created':
          comparison = new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
          break
        default:
          comparison = 0
      }

      return sortOrder === 'desc' ? -comparison : comparison
    })

  const allTags = Array.from(new Set(papers.flatMap((paper) => paper.tags || [])))

  const toggleTag = (tag: string) => {
    setSelectedTags((prev) => (prev.includes(tag) ? prev.filter((t) => t !== tag) : [...prev, tag]))
  }

  const toggleStar = async (paperId: string) => {
    try {
      await authenticatedFetch(`/api/papers/${paperId}/star`, { method: "POST" })
      fetchPapers()
    } catch (error) {
      logError('toggle_star', error, { paperId })
    }
  }

  const deletePaper = async (paperId: string, paperTitle: string) => {
    if (!confirm(`Are you sure you want to delete "${paperTitle}"? This action cannot be undone.`)) {
      return
    }

    try {
      const response = await authenticatedFetch(`/api/papers/${paperId}`, { method: "DELETE" })
      if (response.ok) {
        toast({ title: "Paper deleted successfully" })
        fetchPapers() // Refresh the list
      } else {
        throw new Error("Failed to delete paper")
      }
    } catch (error) {
      logError('delete_paper', error, { paperId })
      toast({ title: "Failed to delete paper", variant: "destructive" })
    }
  }

  const fetchCollections = async () => {
    const result = await api.get<any>('/api/collections', {
      authenticated: true,
      onError: (error) => {
        logError('fetch_collections', error)
        // Don't show toast for collections fetch failure as it's not critical
      },
      onSuccess: (data) => {
        // Handle wrapped response format from createSuccessResponse and createListResponse
        const collections = data.data?.data || data.data || data || []
        setCollections(collections)
      }
    })
  }

  const togglePaperSelection = (paperId: string) => {
    const newSelection = new Set(selectedPapers)
    if (newSelection.has(paperId)) {
      newSelection.delete(paperId)
    } else {
      newSelection.add(paperId)
    }
    setSelectedPapers(newSelection)
  }

  const selectAllPapers = () => {
    const allPaperIds = new Set(filteredAndSortedPapers.map(p => p.id))
    setSelectedPapers(allPaperIds)
  }

  const clearSelection = () => {
    setSelectedPapers(new Set())
    setIsSelectionMode(false)
  }

  const addSelectedPapersToCollection = async (collectionId: string) => {
    try {
      const paperIds = Array.from(selectedPapers)
      const response = await authenticatedFetch(`/api/collections/${collectionId}/papers`, {
        method: "POST",
        body: JSON.stringify({ paperIds }),
      })

      if (response.ok) {
        const data = await response.json()
        toast({
          title: "Papers added to collection",
          description: `${paperIds.length} papers added successfully`
        })
        clearSelection()
        setShowCollectionDialog(false)
      } else {
        throw new Error("Failed to add papers to collection")
      }
    } catch (error) {
      logError('add_papers_to_collection', error, { collectionId, paperCount: selectedPapers.size })
      toast({ title: "Failed to add papers to collection", variant: "destructive" })
    }
  }

  const enrichPaper = async (paper: Paper) => {
    try {
      const response = await authenticatedFetch("/api/papers/enrich", {
        method: "POST",
        body: JSON.stringify({
          title: paper.title,
          authors: paper.authors,
          doi: paper.doi
        })
      })

      if (response.ok) {
        const data = await response.json()
        if (data.enriched) {
          // Update the paper with enriched data
          const updateResponse = await authenticatedFetch(`/api/papers/${paper.id}`, {
            method: "PUT",
            body: JSON.stringify({
              ...paper,
              ...data.metadata
            })
          })

          if (updateResponse.ok) {
            toast({
              title: "Paper enriched successfully",
              description: `Added ${data.metadata.citationCount || 0} citations, ${data.metadata.referenceCount || 0} references`
            })
            fetchPapers() // Refresh the list
          }
        } else {
          toast({
            title: "No additional data found",
            description: "Paper not found in Semantic Scholar database"
          })
        }
      } else {
        throw new Error("Failed to enrich paper")
      }
    } catch (error) {
      logError('enrich_paper', error, { paperId: paper.id, paperTitle: paper.title })
      toast({ title: "Failed to enrich paper", variant: "destructive" })
    }
  }

  const syncSelectedToZotero = async () => {
    if (selectedPapers.size === 0) return

    setIsSyncingZotero(true)
    setZoteroSyncResults(null)

    try {
      const paperIds = Array.from(selectedPapers)
      const response = await authenticatedFetch("/api/papers/sync-zotero", {
        method: "POST",
        body: JSON.stringify({
          mode: "selected",
          paperIds
        })
      })

      const data = await response.json()

      if (response.ok) {
        const results = data.data
        setZoteroSyncResults(results)

        toast({
          title: "Zotero sync completed",
          description: `${results.successful.length} papers synced successfully, ${results.failed.length} failed`
        })

        // Refresh papers to update sync status
        fetchPapers()

        // Clear selection after successful sync
        if (results.failed.length === 0) {
          clearSelection()
        }
      } else {
        const errorMessage = data.errors?.[0] || data.error || "Failed to sync papers to Zotero"
        toast({
          title: "Sync failed",
          description: errorMessage,
          variant: "destructive"
        })
      }
    } catch (error) {
      logError('sync_papers_to_zotero', error, { paperCount: selectedPapers.size })
      toast({
        title: "Sync failed",
        description: "Network error occurred while syncing to Zotero",
        variant: "destructive"
      })
    } finally {
      setIsSyncingZotero(false)
    }
  }

  const syncAllChangedToZotero = async () => {
    setIsSyncingZotero(true)
    setZoteroSyncResults(null)

    try {
      const response = await authenticatedFetch("/api/papers/sync-zotero", {
        method: "POST",
        body: JSON.stringify({
          mode: "changed"
        })
      })

      const data = await response.json()

      if (response.ok) {
        const results = data.data
        setZoteroSyncResults(results)

        toast({
          title: "Zotero sync completed",
          description: `${results.successful.length} papers synced successfully, ${results.failed.length} failed`
        })

        // Refresh papers to update sync status
        fetchPapers()
      } else {
        const errorMessage = data.errors?.[0] || data.error || "Failed to sync papers to Zotero"
        toast({
          title: "Sync failed",
          description: errorMessage,
          variant: "destructive"
        })
      }
    } catch (error) {
      logError('sync_all_papers_to_zotero', error, { paperCount: papers.length })
      toast({
        title: "Sync failed",
        description: "Network error occurred while syncing to Zotero",
        variant: "destructive"
      })
    } finally {
      setIsSyncingZotero(false)
    }
  }

  return (
    <div className="flex flex-col h-screen">
      <header className="border-b p-4">
        <div className="flex items-center gap-4">
          <SidebarTrigger />
          <h1 className="text-2xl font-bold">Papers</h1>
          <div className="flex-1" />
          <div className="flex items-center gap-2">
            <Button
              variant={viewMode === "list" ? "default" : "outline"}
              size="sm"
              onClick={() => setViewMode("list")}
            >
              <List className="h-4 w-4" />
            </Button>
            <Button
              variant={viewMode === "compact" ? "default" : "outline"}
              size="sm"
              onClick={() => setViewMode("compact")}
            >
              <Grid className="h-4 w-4" />
            </Button>
          </div>
          <Button
            variant="outline"
            onClick={() => window.location.href = '/review/session'}
            className="bg-green-50 hover:bg-green-100 text-green-700 border-green-200"
          >
            <Play className="h-4 w-4 mr-2" />
            Review Session
          </Button>
          <Button
            variant={isSelectionMode ? "default" : "outline"}
            onClick={() => {
              setIsSelectionMode(!isSelectionMode)
              if (isSelectionMode) {
                clearSelection()
              }
            }}
          >
            <CheckSquare className="h-4 w-4 mr-2" />
            Select
          </Button>
          <Link href="/papers/new">
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              New Paper
            </Button>
          </Link>
        </div>
      </header>

      <div className="p-4 border-b">
        <div className="space-y-4">
          {/* Summary stats */}
          <div className="flex items-center gap-6 text-sm text-muted-foreground">
            <span>{filteredAndSortedPapers.length} papers</span>
            {filteredAndSortedPapers.filter(p => p.citationCount && p.citationCount > 0).length > 0 && (
              <span>{filteredAndSortedPapers.filter(p => p.citationCount && p.citationCount > 0).length} with citations</span>
            )}
            {filteredAndSortedPapers.filter(p => p.doi).length > 0 && (
              <span>{filteredAndSortedPapers.filter(p => p.doi).length} with DOI</span>
            )}
          </div>

          <div className="flex gap-4 items-center">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search papers, authors, or tags..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>
          <Button
            variant={showStarredOnly ? "default" : "outline"}
            onClick={() => setShowStarredOnly(!showStarredOnly)}
          >
            <Star className="h-4 w-4 mr-2" />
            Starred
          </Button>
          <Button
            variant={showInReviewOnly ? "default" : "outline"}
            onClick={() => setShowInReviewOnly(!showInReviewOnly)}
          >
            <BookOpenCheck className="h-4 w-4 mr-2" />
            In Review
          </Button>

          {/* Sorting Controls */}
          <div className="flex items-center gap-2 border-l pl-4">
            <span className="text-sm text-muted-foreground">Sort by:</span>
            <Select value={sortBy} onValueChange={(value: 'year' | 'title' | 'citations' | 'created') => setSortBy(value)}>
              <SelectTrigger className="w-32">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="year">Year</SelectItem>
                <SelectItem value="title">Title</SelectItem>
                <SelectItem value="citations">Citations</SelectItem>
                <SelectItem value="created">Created</SelectItem>
              </SelectContent>
            </Select>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')}
            >
              {sortOrder === 'asc' ? <ArrowUp className="h-4 w-4" /> : <ArrowDown className="h-4 w-4" />}
            </Button>
          </div>
        </div>

        <div className="flex gap-2 flex-wrap">
          {allTags.map((tag) => (
            <Badge
              key={tag}
              variant={selectedTags.includes(tag) ? "default" : "outline"}
              className="cursor-pointer"
              onClick={() => toggleTag(tag)}
            >
              {tag}
            </Badge>
          ))}
        </div>
        </div>
      </div>

      {/* Bulk Actions Toolbar */}
      {isSelectionMode && selectedPapers.size > 0 && (
        <div className="border-b bg-blue-50 dark:bg-blue-950/30 p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <span className="text-sm font-medium">
                {selectedPapers.size} paper{selectedPapers.size !== 1 ? 's' : ''} selected
              </span>
              <Button variant="outline" size="sm" onClick={selectAllPapers}>
                Select All ({filteredAndSortedPapers.length})
              </Button>
              <Button variant="outline" size="sm" onClick={clearSelection}>
                Clear Selection
              </Button>
            </div>
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                onClick={syncSelectedToZotero}
                disabled={isSyncingZotero}
                className="text-purple-600 border-purple-200 hover:bg-purple-50"
              >
                {isSyncingZotero ? (
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                ) : (
                  <LinkIcon className="h-4 w-4 mr-2" />
                )}
                {isSyncingZotero ? 'Syncing...' : 'Sync to Zotero'}
              </Button>
              <Dialog open={showCollectionDialog} onOpenChange={setShowCollectionDialog}>
                <DialogTrigger asChild>
                  <Button>
                    <FolderPlus className="h-4 w-4 mr-2" />
                    Add to Collection
                  </Button>
                </DialogTrigger>
                <DialogContent>
                  <DialogHeader>
                    <DialogTitle>Add {selectedPapers.size} papers to collection</DialogTitle>
                  </DialogHeader>
                  <div className="space-y-4">
                    <div className="text-sm text-muted-foreground">
                      Select a collection to add the selected papers to:
                    </div>
                    <div className="space-y-2">
                      {collections.map((collection) => (
                        <Button
                          key={collection.id}
                          variant="outline"
                          className="w-full justify-start"
                          onClick={() => addSelectedPapersToCollection(collection.id)}
                        >
                          <FolderPlus className="h-4 w-4 mr-2" />
                          {collection.name}
                        </Button>
                      ))}
                    </div>
                    {collections.length === 0 && (
                      <div className="text-center text-muted-foreground py-4">
                        No collections available. Create a collection first.
                      </div>
                    )}
                  </div>
                </DialogContent>
              </Dialog>
            </div>
          </div>
        </div>
      )}

      <div className="flex-1 overflow-auto p-4">
        {loading ? (
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-4 text-muted-foreground" />
              <p className="text-muted-foreground">Loading papers...</p>
            </div>
          </div>
        ) : filteredAndSortedPapers.length === 0 ? (
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <BookOpen className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
              <h3 className="text-lg font-semibold mb-2">No papers found</h3>
              <p className="text-muted-foreground mb-4">
                {papers.length === 0
                  ? "Start by adding your first paper"
                  : "Try adjusting your search or filters"
                }
              </p>
              {papers.length === 0 && (
                <Button asChild>
                  <Link href="/papers/new">Add Paper</Link>
                </Button>
              )}
            </div>
          </div>
        ) : viewMode === "compact" ? (
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {filteredAndSortedPapers.map((paper) => (
              <div key={paper.id} className="relative">
                {isSelectionMode && (
                  <div className="absolute top-2 left-2 z-10">
                    <Checkbox
                      checked={selectedPapers.has(paper.id)}
                      onCheckedChange={() => togglePaperSelection(paper.id)}
                      onClick={(e) => e.stopPropagation()}
                    />
                  </div>
                )}
                <Link href={`/papers/${paper.id}`}>
                  <PaperCompactView
                    paper={paper}
                    note={notes[paper.id]}
                    className="cursor-pointer"
                  />
                </Link>
              </div>
            ))}
          </div>
        ) : (
          <div className="grid gap-4">
            {filteredAndSortedPapers.map((paper, index) => (
            <Card
              key={paper.id}
              className="hover:shadow-md transition-shadow focus-within:ring-2 focus-within:ring-blue-500"
              tabIndex={0}
              onKeyDown={(e) => {
                switch (e.key.toLowerCase()) {
                  case "enter":
                    e.preventDefault()
                    window.location.href = `/papers/${paper.id}`
                    break
                  case "s":
                    e.preventDefault()
                    toggleStar(paper.id)
                    break
                  case "e":
                    e.preventDefault()
                    window.location.href = `/papers/${paper.id}`
                    break
                  case "delete":
                  case "backspace":
                    if (e.shiftKey) {
                      e.preventDefault()
                      deletePaper(paper.id, paper.title)
                    }
                    break
                }
              }}
            >
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div className="flex items-start gap-3 flex-1">
                    {isSelectionMode && (
                      <Checkbox
                        checked={selectedPapers.has(paper.id)}
                        onCheckedChange={() => togglePaperSelection(paper.id)}
                        onClick={(e) => e.stopPropagation()}
                      />
                    )}
                    <div className="flex-1">
                    <CardTitle className="text-lg">
                      <Link href={`/papers/${paper.id}`} className="hover:underline">
                        {paper.title}
                      </Link>
                    </CardTitle>
                    <p className="text-sm text-muted-foreground mt-1">{(paper.authors || []).join(", ")}</p>
                    {paper.venue && (
                      <p className="text-sm text-muted-foreground">
                        {paper.venue} {paper.year && `(${paper.year})`}
                      </p>
                    )}

                    {/* Enhanced metadata display */}
                    <div className="flex items-center gap-4 mt-3 text-sm">
                      {paper.citationCount !== undefined && paper.citationCount > 0 && (
                        <div className="flex items-center gap-1 text-green-700 dark:text-green-400">
                          <BarChart3 className="h-4 w-4" />
                          <span className="font-medium">{paper.citationCount}</span>
                          <span className="text-muted-foreground">citations</span>
                        </div>
                      )}
                      {paper.referenceCount !== undefined && paper.referenceCount > 0 && (
                        <div className="flex items-center gap-1 text-blue-700 dark:text-blue-400">
                          <FileText className="h-4 w-4" />
                          <span className="font-medium">{paper.referenceCount}</span>
                          <span className="text-muted-foreground">refs</span>
                        </div>
                      )}
                      {paper.doi && (
                        <div className="flex items-center gap-1">
                          <button
                            onClick={(e) => {
                              e.stopPropagation()
                              window.open(`https://doi.org/${paper.doi}`, '_blank')
                            }}
                            className="flex items-center gap-1 hover:text-blue-600 transition-colors text-blue-600 dark:text-blue-400"
                            title={`DOI: ${paper.doi}`}
                          >
                            <ExternalLink className="h-4 w-4" />
                            <span className="font-mono text-xs">{paper.doi}</span>
                          </button>
                        </div>
                      )}
                      {paper.url && !paper.doi && (
                        <button
                          onClick={(e) => {
                            e.stopPropagation()
                            window.open(paper.url, '_blank')
                          }}
                          className="flex items-center gap-1 hover:text-blue-600 transition-colors text-blue-600 dark:text-blue-400"
                        >
                          <ExternalLink className="h-4 w-4" />
                          <span>External Link</span>
                        </button>
                      )}
                    </div>

                    {reviewStatuses[paper.id]?.inReview && (
                      <div className="flex items-center gap-1 mt-2">
                        <BookOpenCheck className="h-3 w-3 text-green-600" />
                        <span className="text-xs text-green-700 font-medium">
                          In Review
                          {reviewStatuses[paper.id].isDue && (
                            <span className="ml-1 text-orange-600">• Due now</span>
                          )}
                          {!reviewStatuses[paper.id].isDue && reviewStatuses[paper.id].daysUntilDue !== null && (
                            <span className="ml-1 text-muted-foreground">
                              • Due in {reviewStatuses[paper.id].daysUntilDue} day{reviewStatuses[paper.id].daysUntilDue !== 1 ? 's' : ''}
                            </span>
                          )}
                        </span>
                      </div>
                    )}
                    </div>
                  </div>
                  <div className="flex gap-1">
                    <Button variant="ghost" size="sm" onClick={() => toggleStar(paper.id)}>
                      <Star className={`h-4 w-4 ${paper.starred ? "fill-yellow-400 text-yellow-400" : ""}`} />
                    </Button>
                    {(!paper.citationCount || !paper.referenceCount) && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation()
                          enrichPaper(paper)
                        }}
                        title="Enrich with Semantic Scholar data"
                      >
                        <RefreshCw className="h-4 w-4" />
                      </Button>
                    )}
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={(e) => {
                        e.stopPropagation()
                        deletePaper(paper.id, paper.title)
                      }}
                      className="text-red-600 hover:text-red-700 hover:bg-red-50"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="flex gap-2 flex-wrap">
                  {paper.tags.map((tag) => (
                    <Badge key={tag} variant="secondary">
                      {tag}
                    </Badge>
                  ))}
                </div>
              </CardContent>
            </Card>
          ))}
          </div>
        )}
      </div>
    </div>
  )
}

export default function PapersPage() {
  const [authModalOpen, setAuthModalOpen] = useState(false)

  return (
    <ProtectedRoute
      onAuthRequired={() => setAuthModalOpen(true)}
      requireEmailVerification={false}
      fallback={
        <AuthModal
          isOpen={true}
          onClose={() => {
            // Redirect to login page instead of just closing
            window.location.href = '/login'
          }}
        />
      }
    >
      <PapersPageContent />
    </ProtectedRoute>
  )
}
