"use client"

import { useState, useEffect, useCallback, useRef } from "react"
import { usePara<PERSON>, useRouter } from "next/navigation"
import { ArrowLeft, Star, Trash2, BookOpen, BookOpenCheck, ExternalLink, FileText, BarChart3, RefreshCw, ChevronDown, ChevronUp, Loader2, CheckCircle, AlertCircle, Link as LinkIcon } from "lucide-react"
import Link from "next/link"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { SidebarTrigger } from "@/components/ui/sidebar"
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible"
import { useToast } from "@/hooks/use-toast"
import { CollectionToggle } from "@/components/collection-toggle"
import { authenticatedFetch, useErrorLogger } from "@/lib/utils"
import type { Paper, Note } from "@/lib/types"

export default function PaperPage() {
  const params = useParams()
  const router = useRouter()
  const { toast } = useToast()
  const { logError } = useErrorLogger('PaperPage')
  const [paper, setPaper] = useState<Paper | null>(null)
  const [note, setNote] = useState<Note | null>(null)
  const [loading, setLoading] = useState(true)
  const [reviewStatus, setReviewStatus] = useState<{
    inReview: boolean
    isDue: boolean
    daysUntilDue: number | null
  } | null>(null)
  const [isEnriching, setIsEnriching] = useState(false)
  const [isAbstractOpen, setIsAbstractOpen] = useState(false)
  const [isDetailsOpen, setIsDetailsOpen] = useState(false)
  const [isSaving, setIsSaving] = useState(false)
  const [saveStatus, setSaveStatus] = useState<'idle' | 'saving' | 'saved' | 'error'>('idle')
  const [isSyncingZotero, setIsSyncingZotero] = useState(false)
  const [zoteroSyncStatus, setZoteroSyncStatus] = useState<'idle' | 'syncing' | 'success' | 'error'>('idle')
  const saveTimeoutRef = useRef<NodeJS.Timeout | null>(null)
  const debouncedSaveRef = useRef<NodeJS.Timeout | null>(null)

  const paperId = params.id as string

  // Get collection context from URL search params if available
  const [collectionId, setCollectionId] = useState<string | null>(null)

  useEffect(() => {
    if (typeof window !== 'undefined') {
      const urlParams = new URLSearchParams(window.location.search)
      const fromCollection = urlParams.get('collection')
      setCollectionId(fromCollection)
    }
  }, [])

  useEffect(() => {
    fetchPaper()
    fetchNote()
    fetchReviewStatus()
  }, [paperId])

  // Cleanup timeouts on unmount
  useEffect(() => {
    return () => {
      if (saveTimeoutRef.current) {
        clearTimeout(saveTimeoutRef.current)
      }
      if (debouncedSaveRef.current) {
        clearTimeout(debouncedSaveRef.current)
      }
    }
  }, [])

  const fetchPaper = async () => {
    try {
      const response = await fetch(`/api/papers/${paperId}`)
      if (response.ok) {
        const responseData = await response.json()
        // Handle wrapped response format
        const paperData = responseData.data || responseData
        setPaper(paperData)
      } else if (response.status === 404) {
        toast({
          title: "Paper not found",
          description: "The paper you're looking for doesn't exist",
          variant: "destructive"
        })
        router.push("/papers")
      } else if (response.status === 403) {
        toast({
          title: "Access denied",
          description: "You don't have permission to view this paper",
          variant: "destructive"
        })
        router.push("/papers")
      } else {
        throw new Error(`HTTP ${response.status}`)
      }
    } catch (error) {
      logError("fetch_paper", error, { paperId })
      toast({
        title: "Failed to load paper",
        description: "Please try again later",
        variant: "destructive"
      })
    } finally {
      setLoading(false)
    }
  }

  const fetchNote = async () => {
    try {
      const response = await fetch(`/api/notes/${paperId}`)
      if (response.ok) {
        const data = await response.json()
        setNote(data)
      } else {
        // Create empty note if none exists
        setNote({
          id: "",
          paperId,
          quickSummary: "",
          keyIdeas: ["", "", ""],
        })
      }
    } catch (error) {
      logError("fetch_note", error, { paperId })
    }
  }

  const fetchReviewStatus = async () => {
    try {
      const response = await fetch(`/api/papers/${paperId}/review`)
      if (response.ok) {
        const data = await response.json()
        setReviewStatus(data)
      }
    } catch (error) {
      logError("fetch_review_status", error, { paperId })
    }
  }

  const addToReview = async () => {
    try {
      const response = await fetch(`/api/papers/${paperId}/review`, {
        method: "POST",
      })

      if (response.ok) {
        const data = await response.json()
        toast({
          title: "Added to review queue",
          description: data.wasExisting ? "Paper review updated" : "Paper added to review queue"
        })
        fetchReviewStatus() // Refresh status
      } else {
        throw new Error("Failed to add to review")
      }
    } catch (error) {
      logError("add_to_review", error, { paperId })
      toast({ title: "Failed to add to review", variant: "destructive" })
    }
  }

  const removeFromReview = async () => {
    try {
      const response = await fetch(`/api/papers/${paperId}/review`, {
        method: "DELETE",
      })

      if (response.ok) {
        toast({ title: "Removed from review queue" })
        fetchReviewStatus() // Refresh status
      } else {
        throw new Error("Failed to remove from review")
      }
    } catch (error) {
      logError("remove_from_review", error, { paperId })
      toast({ title: "Failed to remove from review", variant: "destructive" })
    }
  }

  const enrichPaper = async () => {
    if (!paper) return

    setIsEnriching(true)
    try {
      const enrichRequest: any = {
        title: paper.title,
        authors: paper.authors,
        doi: paper.doi
      }

      // Include collection context if available
      if (collectionId) {
        enrichRequest.collectionId = collectionId
      }

      const response = await authenticatedFetch("/api/papers/enrich", {
        method: "POST",
        body: JSON.stringify(enrichRequest)
      })

      if (response.ok) {
        const data = await response.json()
        if (data.enriched) {
          // Update paper with enriched metadata
          const enrichedPaper = { ...paper, ...data.metadata }
          setPaper(enrichedPaper)

          // Save the enriched data
          await savePaper(data.metadata)

          toast({
            title: "Paper enriched successfully",
            description: `Added ${data.metadata.citationCount || 0} citations, ${data.metadata.referenceCount || 0} references`
          })
        } else {
          toast({
            title: "No additional data found",
            description: "Paper not found in Semantic Scholar database"
          })
        }
      } else {
        throw new Error("Failed to enrich paper")
      }
    } catch (error) {
      logError("enrich_paper", error, { paperId, paperTitle: paper?.title })
      toast({ title: "Failed to enrich paper", variant: "destructive" })
    } finally {
      setIsEnriching(false)
    }
  }

  const syncToZotero = async () => {
    if (!paper) return

    setIsSyncingZotero(true)
    setZoteroSyncStatus('syncing')

    try {
      const response = await fetch(`/api/papers/${paperId}/sync-zotero`, {
        method: "POST",
        headers: { "Content-Type": "application/json" }
      })

      const data = await response.json()

      if (response.ok) {
        setZoteroSyncStatus('success')
        toast({
          title: "Synced to Zotero",
          description: data.data?.message || "Paper synced successfully to Zotero"
        })

        // Update paper with sync information
        setPaper(prev => prev ? {
          ...prev,
          zoteroSyncStatus: 'synced',
          zoteroLastSynced: new Date().toISOString()
        } : null)

        // Reset status after 3 seconds
        setTimeout(() => setZoteroSyncStatus('idle'), 3000)
      } else {
        setZoteroSyncStatus('error')
        const errorMessage = data.errors?.[0] || data.error || "Failed to sync to Zotero"
        toast({
          title: "Sync failed",
          description: errorMessage,
          variant: "destructive"
        })

        // Reset status after 5 seconds
        setTimeout(() => setZoteroSyncStatus('idle'), 5000)
      }
    } catch (error) {
      logError("sync_to_zotero", error, { paperId, paperTitle: paper?.title })
      setZoteroSyncStatus('error')
      toast({
        title: "Sync failed",
        description: "Network error occurred while syncing to Zotero",
        variant: "destructive"
      })

      // Reset status after 5 seconds
      setTimeout(() => setZoteroSyncStatus('idle'), 5000)
    } finally {
      setIsSyncingZotero(false)
    }
  }

  const savePaper = useCallback(
    async (updatedPaper: Partial<Paper>) => {
      if (!paper) return

      // Clear any existing timeout
      if (saveTimeoutRef.current) {
        clearTimeout(saveTimeoutRef.current)
      }

      // Set saving status
      setSaveStatus('saving')
      setIsSaving(true)

      try {
        const response = await fetch(`/api/papers/${paperId}`, {
          method: "PUT",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(updatedPaper),
        })

        if (response.ok) {
          const responseData = await response.json()
          // Handle both wrapped and unwrapped responses
          const updated = responseData.data || responseData

          // Only update the specific fields that were changed, not the entire paper
          setPaper(prev => prev ? { ...prev, ...updated } : updated)

          setSaveStatus('saved')

          // Reset status after 2 seconds
          saveTimeoutRef.current = setTimeout(() => {
            setSaveStatus('idle')
          }, 2000)
        } else {
          throw new Error('Failed to save paper')
        }
      } catch (error) {
        logError("save_paper", error, { paperId, paperTitle: paper?.title })
        setSaveStatus('error')
        toast({
          title: "Failed to save changes",
          description: "Please try again",
          variant: "destructive"
        })

        // Reset error status after 3 seconds
        saveTimeoutRef.current = setTimeout(() => {
          setSaveStatus('idle')
        }, 3000)
      } finally {
        setIsSaving(false)
      }
    },
    [paper, paperId, toast],
  )

  // Debounced save function to prevent saving on every keystroke
  const debouncedSavePaper = useCallback(
    (updatedPaper: Partial<Paper>) => {
      // Clear existing debounce timeout
      if (debouncedSaveRef.current) {
        clearTimeout(debouncedSaveRef.current)
      }

      // Set new timeout
      debouncedSaveRef.current = setTimeout(() => {
        savePaper(updatedPaper)
      }, 1000) // Wait 1 second after user stops typing
    },
    [savePaper]
  )

  const saveNote = useCallback(
    async (updatedNote: Partial<Note>) => {
      if (!note) return

      try {
        const response = await fetch(`/api/notes/${paperId}`, {
          method: "PUT",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(updatedNote),
        })

        if (response.ok) {
          const updated = await response.json()
          setNote(updated)
        }
      } catch (error) {
        logError("save_note", error, { paperId, noteId: note?.id })
      }
    },
    [note, paperId],
  )

  const toggleStar = async () => {
    if (!paper) return

    try {
      await fetch(`/api/papers/${paperId}/star`, { method: "POST" })
      setPaper((prev) => (prev ? { ...prev, starred: !prev.starred } : null))
    } catch (error) {
      logError("toggle_star", error, { paperId, paperTitle: paper?.title })
    }
  }

  const deletePaper = async () => {
    if (!paper) return

    if (!confirm(`Are you sure you want to delete "${paper.title}"? This action cannot be undone.`)) {
      return
    }

    try {
      const response = await fetch(`/api/papers/${paperId}`, { method: "DELETE" })
      if (response.ok) {
        toast({ title: "Paper deleted successfully" })
        router.push("/papers")
      } else {
        throw new Error("Failed to delete paper")
      }
    } catch (error) {
      logError("delete_paper", error, { paperId, paperTitle: paper?.title })
      toast({ title: "Failed to delete paper", variant: "destructive" })
    }
  }

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Don't handle shortcuts when typing in inputs or textareas
      const target = e.target as HTMLElement
      if (target.tagName === "INPUT" || target.tagName === "TEXTAREA") {
        return
      }

      // Handle Ctrl/Cmd + S for save (though auto-save is already implemented)
      if ((e.metaKey || e.ctrlKey) && e.key === "s") {
        e.preventDefault()
        // Auto-save is handled by debounced updates
        return
      }

      // Handle keyboard shortcuts
      switch (e.key.toLowerCase()) {
        case "s":
          e.preventDefault()
          toggleStar()
          break
        case "e":
          e.preventDefault()
          // Focus on the title field to start editing
          const titleField = document.getElementById("title")
          if (titleField) {
            titleField.focus()
          }
          break
        case "escape":
          e.preventDefault()
          router.push("/papers")
          break
        case "delete":
        case "backspace":
          if (e.shiftKey) {
            e.preventDefault()
            deletePaper()
          }
          break
        case "r":
          e.preventDefault()
          if (reviewStatus?.inReview) {
            removeFromReview()
          } else {
            addToReview()
          }
          break
      }

      // Number keys 1-3 for key ideas
      if (e.key >= "1" && e.key <= "3") {
        const index = Number.parseInt(e.key) - 1
        const ideaInput = document.getElementById(`idea-${index}`)
        if (ideaInput) {
          e.preventDefault()
          ideaInput.focus()
        }
      }

      // 'q' key for quick summary
      if (e.key.toLowerCase() === "q") {
        const summaryInput = document.getElementById("quickSummary")
        if (summaryInput) {
          e.preventDefault()
          summaryInput.focus()
        }
      }
    }

    window.addEventListener("keydown", handleKeyDown)
    return () => window.removeEventListener("keydown", handleKeyDown)
  }, [router])

  if (loading) {
    return <div className="p-4">Loading...</div>
  }

  if (!paper) {
    return <div className="p-4">Paper not found</div>
  }

  return (
    <div className="flex flex-col h-screen">
      <header className="border-b p-4">
        <div className="flex items-center gap-4">
          <SidebarTrigger />
          <Link href="/papers">
            <Button variant="ghost" size="sm">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back
            </Button>
          </Link>
          <h1 className="text-xl font-semibold truncate flex-1">{paper.title}</h1>
          <CollectionToggle paperId={paperId} />
          <Button variant="ghost" size="sm" onClick={toggleStar}>
            <Star className={`h-4 w-4 ${paper.starred ? "fill-yellow-400 text-yellow-400" : ""}`} />
          </Button>
          {reviewStatus?.inReview ? (
            <Button
              variant="ghost"
              size="sm"
              onClick={removeFromReview}
              className="text-green-600 hover:text-green-700"
            >
              <BookOpenCheck className="h-4 w-4" />
            </Button>
          ) : (
            <Button variant="ghost" size="sm" onClick={addToReview}>
              <BookOpen className="h-4 w-4" />
            </Button>
          )}
          <Button
            variant="outline"
            size="sm"
            onClick={enrichPaper}
            disabled={isEnriching}
            className="text-blue-600 border-blue-200 hover:bg-blue-50"
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${isEnriching ? 'animate-spin' : ''}`} />
            {isEnriching ? 'Enriching...' : 'Enrich'}
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={syncToZotero}
            disabled={isSyncingZotero}
            className={`${
              zoteroSyncStatus === 'success'
                ? 'text-green-600 border-green-200 hover:bg-green-50'
                : zoteroSyncStatus === 'error'
                ? 'text-red-600 border-red-200 hover:bg-red-50'
                : 'text-purple-600 border-purple-200 hover:bg-purple-50'
            }`}
          >
            {zoteroSyncStatus === 'syncing' ? (
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
            ) : zoteroSyncStatus === 'success' ? (
              <CheckCircle className="h-4 w-4 mr-2" />
            ) : zoteroSyncStatus === 'error' ? (
              <AlertCircle className="h-4 w-4 mr-2" />
            ) : (
              <LinkIcon className="h-4 w-4 mr-2" />
            )}
            {zoteroSyncStatus === 'syncing'
              ? 'Syncing...'
              : zoteroSyncStatus === 'success'
              ? 'Synced'
              : zoteroSyncStatus === 'error'
              ? 'Failed'
              : 'Sync to Zotero'
            }
          </Button>
          <Button variant="ghost" size="sm" onClick={deletePaper}>
            <Trash2 className="h-4 w-4" />
          </Button>

          {/* Save Status Indicator */}
          {saveStatus !== 'idle' && (
            <div className="flex items-center gap-2 text-sm">
              {saveStatus === 'saving' && (
                <>
                  <Loader2 className="h-4 w-4 animate-spin text-blue-600" />
                  <span className="text-blue-600">Saving...</span>
                </>
              )}
              {saveStatus === 'saved' && (
                <>
                  <CheckCircle className="h-4 w-4 text-green-600" />
                  <span className="text-green-600">Saved</span>
                </>
              )}
              {saveStatus === 'error' && (
                <>
                  <AlertCircle className="h-4 w-4 text-red-600" />
                  <span className="text-red-600">Error saving</span>
                </>
              )}
            </div>
          )}
        </div>
      </header>

      <div className="flex-1 overflow-auto p-6">
        <div className="max-w-4xl mx-auto space-y-8">
          {/* Paper Details */}
          <div className="space-y-6">
            {reviewStatus?.inReview && (
              <div className="flex items-center gap-2 p-3 bg-blue-50 dark:bg-blue-950/30 rounded-lg border border-blue-200 dark:border-blue-800">
                <BookOpenCheck className="h-4 w-4 text-blue-600" />
                <span className="text-sm font-medium text-blue-900 dark:text-blue-100">
                  In Review Queue
                </span>
                {reviewStatus.isDue ? (
                  <span className="text-xs text-blue-700 dark:text-blue-300">• Due now</span>
                ) : reviewStatus.daysUntilDue !== null && (
                  <span className="text-xs text-blue-700 dark:text-blue-300">
                    • Due in {reviewStatus.daysUntilDue} day{reviewStatus.daysUntilDue !== 1 ? 's' : ''}
                  </span>
                )}
              </div>
            )}

            {/* Main Paper Information */}
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="title">Title</Label>
                <Textarea
                  id="title"
                  value={paper.title}
                  onChange={(e) => {
                    const newTitle = e.target.value
                    // Update UI immediately for responsive feel
                    setPaper(prev => prev ? { ...prev, title: newTitle } : null)
                    // Debounce the save to prevent excessive API calls
                    debouncedSavePaper({ title: newTitle })
                  }}
                  rows={3}
                  className="text-lg font-semibold"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="authors">Authors</Label>
                <Textarea
                  id="authors"
                  value={(paper.authors || []).join(", ")}
                  onChange={(e) => {
                    const authorsText = e.target.value
                    const authors = authorsText
                      .split(",")
                      .map((a) => a.trim())
                      .filter(Boolean)
                    // Update UI immediately
                    setPaper(prev => prev ? { ...prev, authors } : null)
                    // Debounce the save
                    debouncedSavePaper({ authors })
                  }}
                  rows={2}
                />
              </div>

              {/* Publication Info - Compact */}
              <div className="grid grid-cols-4 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="year">Year</Label>
                  <Input
                    id="year"
                    type="number"
                    value={paper.year || ""}
                    onChange={(e) => {
                      const year = e.target.value ? Number.parseInt(e.target.value) : undefined
                      // Update UI immediately
                      setPaper(prev => prev ? { ...prev, year } : null)
                      // Debounce the save
                      debouncedSavePaper({ year })
                    }}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="venue">Venue</Label>
                  <Input
                    id="venue"
                    value={paper.venue || ""}
                    onChange={(e) => {
                      const venue = e.target.value
                      // Update UI immediately
                      setPaper(prev => prev ? { ...prev, venue } : null)
                      // Debounce the save
                      debouncedSavePaper({ venue })
                    }}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="citationCount">Citations</Label>
                  <Input
                    id="citationCount"
                    type="number"
                    value={paper.citationCount || ""}
                    placeholder="0"
                    onChange={(e) => {
                      const citationCount = e.target.value ? Number.parseInt(e.target.value) : undefined
                      const updated = { ...paper, citationCount }
                      setPaper(updated)
                      savePaper({ citationCount })
                    }}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="referenceCount">References</Label>
                  <Input
                    id="referenceCount"
                    type="number"
                    value={paper.referenceCount || ""}
                    placeholder="0"
                    onChange={(e) => {
                      const referenceCount = e.target.value ? Number.parseInt(e.target.value) : undefined
                      const updated = { ...paper, referenceCount }
                      setPaper(updated)
                      savePaper({ referenceCount })
                    }}
                  />
                </div>
              </div>
            </div>

            {/* Tags */}
            <div className="space-y-2">
              <Label htmlFor="tags">Tags</Label>
              <Input
                id="tags"
                value={(paper.tags || []).join(", ")}
                onChange={(e) => {
                  const tagsText = e.target.value
                  const tags = tagsText
                    .split(",")
                    .map((t) => t.trim())
                    .filter(Boolean)
                  // Update UI immediately for responsive feel
                  setPaper(prev => prev ? { ...prev, tags } : null)
                  // Debounce the save to prevent excessive API calls
                  debouncedSavePaper({ tags })
                }}
              />
            </div>

            {/* Collapsible Abstract */}
            <Collapsible open={isAbstractOpen} onOpenChange={setIsAbstractOpen}>
              <CollapsibleTrigger asChild>
                <Button variant="ghost" className="w-full justify-between p-0 h-auto">
                  <Label className="text-base font-medium">Abstract</Label>
                  {isAbstractOpen ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
                </Button>
              </CollapsibleTrigger>
              <CollapsibleContent className="space-y-2 mt-2">
                <Textarea
                  id="abstract"
                  value={paper.abstract || ""}
                  placeholder="Paper abstract..."
                  rows={4}
                  onChange={(e) => {
                    const abstract = e.target.value
                    // Update UI immediately
                    setPaper(prev => prev ? { ...prev, abstract } : null)
                    // Debounce the save
                    debouncedSavePaper({ abstract })
                  }}
                />
              </CollapsibleContent>
            </Collapsible>

            {/* Collapsible Additional Details */}
            <Collapsible open={isDetailsOpen} onOpenChange={setIsDetailsOpen}>
              <CollapsibleTrigger asChild>
                <Button variant="ghost" className="w-full justify-between p-0 h-auto">
                  <Label className="text-base font-medium">Additional Details</Label>
                  {isDetailsOpen ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
                </Button>
              </CollapsibleTrigger>
              <CollapsibleContent className="space-y-4 mt-2">
                {/* DOI and URL */}
                <div className="grid grid-cols-1 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="doi">DOI</Label>
                    <div className="flex gap-2">
                      <Input
                        id="doi"
                        value={paper.doi || ""}
                        placeholder="10.1000/182"
                        onChange={(e) => {
                          const updated = { ...paper, doi: e.target.value }
                          setPaper(updated)
                          savePaper({ doi: e.target.value })
                        }}
                      />
                      {paper.doi && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => window.open(`https://doi.org/${paper.doi}`, '_blank')}
                          className="shrink-0"
                        >
                          <ExternalLink className="h-4 w-4" />
                        </Button>
                      )}
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="url">URL</Label>
                    <div className="flex gap-2">
                      <Input
                        id="url"
                        value={paper.url || ""}
                        placeholder="https://..."
                        onChange={(e) => {
                          const updated = { ...paper, url: e.target.value }
                          setPaper(updated)
                          savePaper({ url: e.target.value })
                        }}
                      />
                      {paper.url && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => window.open(paper.url, '_blank')}
                          className="shrink-0"
                        >
                          <ExternalLink className="h-4 w-4" />
                        </Button>
                      )}
                    </div>
                  </div>
                </div>

                {/* Journal and Publication Details */}
                <div className="grid grid-cols-3 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="journal">Journal</Label>
                    <Input
                      id="journal"
                      value={paper.journal || ""}
                      placeholder="Journal name"
                      onChange={(e) => {
                        const updated = { ...paper, journal: e.target.value }
                        setPaper(updated)
                        savePaper({ journal: e.target.value })
                      }}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="volume">Volume</Label>
                    <Input
                      id="volume"
                      value={paper.volume || ""}
                      placeholder="Vol. 1"
                      onChange={(e) => {
                        const updated = { ...paper, volume: e.target.value }
                        setPaper(updated)
                        savePaper({ volume: e.target.value })
                      }}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="issue">Issue</Label>
                    <Input
                      id="issue"
                      value={paper.issue || ""}
                      placeholder="Issue 1"
                      onChange={(e) => {
                        const updated = { ...paper, issue: e.target.value }
                        setPaper(updated)
                        savePaper({ issue: e.target.value })
                      }}
                    />
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="pages">Pages</Label>
                    <Input
                      id="pages"
                      value={paper.pages || ""}
                      placeholder="1-10"
                      onChange={(e) => {
                        const updated = { ...paper, pages: e.target.value }
                        setPaper(updated)
                        savePaper({ pages: e.target.value })
                      }}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="publicationDate">Publication Date</Label>
                    <Input
                      id="publicationDate"
                      type="date"
                      value={paper.publicationDate || ""}
                      onChange={(e) => {
                        const updated = { ...paper, publicationDate: e.target.value }
                        setPaper(updated)
                        savePaper({ publicationDate: e.target.value })
                      }}
                    />
                  </div>
                </div>
              </CollapsibleContent>
            </Collapsible>
          </div>

          {/* Notes Section */}
          {note && (
            <div className="space-y-6">
              <h2 className="text-xl font-semibold">Notes</h2>

              {/* Quick Summary */}
              <div className="space-y-2">
                <Label htmlFor="quickSummary">Quick Summary</Label>
                <Textarea
                  id="quickSummary"
                  value={note.quickSummary || ""}
                  onChange={(e) => {
                    const updated = { ...note, quickSummary: e.target.value }
                    setNote(updated)
                    saveNote({ quickSummary: e.target.value })
                  }}
                  placeholder="What is this paper about in one sentence?"
                  rows={2}
                  className="text-base"
                />
              </div>

              {/* Key Ideas */}
              <div className="space-y-4">
                <Label>Key Ideas (use keys 1-3 to focus)</Label>
                {note.keyIdeas.map((idea, index) => (
                  <div key={index} className="space-y-2">
                    <Label htmlFor={`idea-${index}`} className="text-sm text-muted-foreground flex items-center gap-2">
                      <span className="bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold">
                        {index + 1}
                      </span>
                      Key Idea {index + 1}
                    </Label>
                    <Textarea
                      id={`idea-${index}`}
                      value={idea}
                      onChange={(e) => {
                        const keyIdeas = [...note.keyIdeas]
                        keyIdeas[index] = e.target.value
                        const updated = { ...note, keyIdeas }
                        setNote(updated)
                        saveNote({ keyIdeas })
                      }}
                      placeholder={`Main contribution or finding ${index + 1}...`}
                      rows={2}
                    />
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
