#!/bin/bash

echo "🔨 Testing Docker build (build stage only)..."

# Build only the builder stage to test compilation
docker build --target builder -t papernugget-build-test .

if [ $? -eq 0 ]; then
    echo "✅ Docker build stage successful!"
    
    # Clean up test image
    docker rmi papernugget-build-test
    
    echo "🎉 Build test completed successfully!"
else
    echo "❌ Docker build stage failed!"
    exit 1
fi
