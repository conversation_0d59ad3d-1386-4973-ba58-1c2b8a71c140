#!/usr/bin/env node

import { readFileSync, readdirSync, statSync } from 'fs'
import { join } from 'path'

interface SyntaxCheckResult {
  file: string
  status: 'pass' | 'fail'
  errors?: string[]
}

function findRouteFiles(dir: string): string[] {
  const routeFiles: string[] = []
  
  function scanDirectory(currentDir: string) {
    try {
      const entries = readdirSync(currentDir)
      
      for (const entry of entries) {
        const fullPath = join(currentDir, entry)
        const stats = statSync(fullPath)
        
        if (stats.isDirectory()) {
          scanDirectory(fullPath)
        } else if (entry === 'route.ts' || entry === 'route.js') {
          routeFiles.push(fullPath)
        }
      }
    } catch (error) {
      console.warn(`Warning: Could not scan directory ${currentDir}:`, error.message)
    }
  }
  
  scanDirectory(dir)
  return routeFiles
}

function checkBasicSyntax(filePath: string): SyntaxCheckResult {
  try {
    const content = readFileSync(filePath, 'utf-8')
    const errors: string[] = []
    
    // Check for unmatched braces
    const openBraces = (content.match(/{/g) || []).length
    const closeBraces = (content.match(/}/g) || []).length
    if (openBraces !== closeBraces) {
      errors.push(`Unmatched braces: ${openBraces} opening, ${closeBraces} closing`)
    }
    
    // Check for unmatched parentheses
    const openParens = (content.match(/\(/g) || []).length
    const closeParens = (content.match(/\)/g) || []).length
    if (openParens !== closeParens) {
      errors.push(`Unmatched parentheses: ${openParens} opening, ${closeParens} closing`)
    }
    
    // Check for catch without try
    if (content.includes('} catch') && !content.includes('try {')) {
      errors.push("Found 'catch' without corresponding 'try'")
    }
    
    // Check for export statements
    if (!content.includes('export')) {
      errors.push('No export statements found - route files must export handlers')
    }
    
    // Check for withAuth usage
    if (content.includes('withAuth(')) {
      if (!content.includes('}, { allowUnverified:') && !content.includes('})')) {
        errors.push('withAuth function may not be properly closed')
      }
    }
    
    // Check for async without await
    if (content.includes('async (') && !content.includes('await ')) {
      errors.push('Async function found but no await statements - consider if async is needed')
    }
    
    // Check for duplicate catch blocks (common copy-paste error)
    // Look for consecutive catch blocks with similar error handling
    const lines = content.split('\n')
    let catchBlocks: { line: number, content: string }[] = []

    lines.forEach((line, index) => {
      if (line.includes('} catch (')) {
        // Get the next few lines to see the error handling
        const nextLines = lines.slice(index + 1, index + 4).join('\n')
        catchBlocks.push({ line: index, content: nextLines })
      }
    })

    // Check for very similar catch blocks (likely copy-paste errors)
    for (let i = 1; i < catchBlocks.length; i++) {
      const current = catchBlocks[i]
      const previous = catchBlocks[i - 1]

      // If catch blocks are very close and have similar content, flag as potential duplicate
      if (current.line - previous.line < 5 &&
          current.content.includes('return NextResponse.json') &&
          previous.content.includes('return NextResponse.json') &&
          current.content.includes('status: 500') &&
          previous.content.includes('status: 500')) {
        errors.push('Possible duplicate catch blocks with similar error handling detected')
        break
      }
    }
    
    return {
      file: filePath,
      status: errors.length === 0 ? 'pass' : 'fail',
      errors: errors.length > 0 ? errors : undefined
    }
  } catch (error) {
    return {
      file: filePath,
      status: 'fail',
      errors: [`Failed to read file: ${error.message}`]
    }
  }
}

function main() {
  console.log('🔍 Checking syntax of all route files...\n')
  
  const appDir = join(process.cwd(), 'app')
  const routeFiles = findRouteFiles(appDir)
  
  if (routeFiles.length === 0) {
    console.log('❌ No route files found!')
    process.exit(1)
  }
  
  console.log(`Found ${routeFiles.length} route files:\n`)
  
  const results: SyntaxCheckResult[] = []
  
  for (const file of routeFiles) {
    const relativePath = file.replace(process.cwd() + '/', '')
    process.stdout.write(`Checking ${relativePath.padEnd(50)} `)
    
    const result = checkBasicSyntax(file)
    results.push(result)
    
    if (result.status === 'pass') {
      console.log('✅ PASS')
    } else {
      console.log('❌ FAIL')
    }
  }
  
  console.log('\n' + '='.repeat(60))
  console.log('📊 SYNTAX CHECK SUMMARY')
  console.log('='.repeat(60))
  
  const passed = results.filter(r => r.status === 'pass')
  const failed = results.filter(r => r.status === 'fail')
  
  console.log(`✅ Passed: ${passed.length}`)
  console.log(`❌ Failed: ${failed.length}`)
  console.log(`📁 Total:  ${results.length}`)
  
  if (failed.length > 0) {
    console.log('\n🚨 FAILED FILES:')
    console.log('-'.repeat(40))
    
    failed.forEach(result => {
      console.log(`\n📄 ${result.file.replace(process.cwd() + '/', '')}`)
      if (result.errors) {
        result.errors.forEach(error => {
          console.log(`   ${error}`)
        })
      }
    })
    
    console.log('\n💡 Fix these syntax errors before deploying!')
    process.exit(1)
  } else {
    console.log('\n🎉 All route files have valid syntax!')
    process.exit(0)
  }
}

if (require.main === module) {
  main()
}
