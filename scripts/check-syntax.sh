#!/bin/bash

# Comprehensive syntax checker for all route files
# This script checks for common syntax errors before building

set -e

echo "🔍 Checking syntax of all route files..."
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Counters
TOTAL_FILES=0
PASSED_FILES=0
FAILED_FILES=0

# Find all route files
ROUTE_FILES=$(find app -name "route.ts" -o -name "route.js" 2>/dev/null || true)

if [ -z "$ROUTE_FILES" ]; then
    echo -e "${RED}❌ No route files found!${NC}"
    exit 1
fi

echo -e "${BLUE}Found route files:${NC}"
echo "$ROUTE_FILES" | while read -r file; do
    echo "  📄 $file"
done
echo ""

# Function to check basic syntax issues
check_basic_syntax() {
    local file="$1"
    local errors=()
    
    # Check for unmatched braces
    local open_braces=$(grep -o '{' "$file" | wc -l)
    local close_braces=$(grep -o '}' "$file" | wc -l)
    if [ "$open_braces" -ne "$close_braces" ]; then
        errors+=("Unmatched braces: $open_braces opening, $close_braces closing")
    fi
    
    # Check for unmatched parentheses
    local open_parens=$(grep -o '(' "$file" | wc -l)
    local close_parens=$(grep -o ')' "$file" | wc -l)
    if [ "$open_parens" -ne "$close_parens" ]; then
        errors+=("Unmatched parentheses: $open_parens opening, $close_parens closing")
    fi
    
    # Check for common syntax patterns
    if grep -q "} catch" "$file"; then
        if ! grep -q "try {" "$file"; then
            errors+=("Found 'catch' without corresponding 'try'")
        fi
    fi
    
    # Check for export statements
    if ! grep -q "export" "$file"; then
        errors+=("No export statements found - route files must export handlers")
    fi
    
    # Check for withAuth usage without proper closing
    if grep -q "withAuth(" "$file"; then
        if ! grep -q "}, { allowUnverified:" "$file" && ! grep -q "})" "$file"; then
            errors+=("withAuth function may not be properly closed")
        fi
    fi
    
    # Check for async function syntax
    if grep -q "async (" "$file"; then
        if ! grep -q "await " "$file"; then
            errors+=("Async function found but no await statements - consider if async is needed")
        fi
    fi
    
    # Return errors
    if [ ${#errors[@]} -eq 0 ]; then
        return 0
    else
        printf '%s\n' "${errors[@]}"
        return 1
    fi
}

# Check each file
echo -e "${YELLOW}Checking syntax...${NC}"
echo ""

for file in $ROUTE_FILES; do
    TOTAL_FILES=$((TOTAL_FILES + 1))
    
    printf "Checking %-50s " "$file..."
    
    # Check basic syntax
    if error_output=$(check_basic_syntax "$file" 2>&1); then
        echo -e "${GREEN}✅ PASS${NC}"
        PASSED_FILES=$((PASSED_FILES + 1))
    else
        echo -e "${RED}❌ FAIL${NC}"
        echo -e "${RED}  Errors:${NC}"
        echo "$error_output" | sed 's/^/    /'
        echo ""
        FAILED_FILES=$((FAILED_FILES + 1))
    fi
done

echo ""
echo "============================================================"
echo -e "${BLUE}📊 SYNTAX CHECK SUMMARY${NC}"
echo "============================================================"
echo -e "${GREEN}✅ Passed: $PASSED_FILES${NC}"
echo -e "${RED}❌ Failed: $FAILED_FILES${NC}"
echo -e "${BLUE}📁 Total:  $TOTAL_FILES${NC}"

if [ $FAILED_FILES -eq 0 ]; then
    echo ""
    echo -e "${GREEN}🎉 All route files passed basic syntax checks!${NC}"
    echo ""
    echo -e "${YELLOW}💡 Next steps:${NC}"
    echo "  1. Run 'docker compose build app' to test full compilation"
    echo "  2. Run 'docker compose up -d' to start the application"
    echo "  3. Test the application functionality"
    exit 0
else
    echo ""
    echo -e "${RED}🚨 Some files failed syntax checks!${NC}"
    echo ""
    echo -e "${YELLOW}💡 Fix these issues before building:${NC}"
    echo "  1. Review the errors above"
    echo "  2. Fix syntax issues in the failing files"
    echo "  3. Run this script again to verify fixes"
    echo "  4. Then run 'docker compose build app'"
    exit 1
fi
