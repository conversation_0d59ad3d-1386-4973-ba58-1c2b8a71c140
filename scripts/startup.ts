/**
 * Startup script to run database migrations and validation checks
 * This runs when the application starts, not during build
 */

import { runMigrations } from '../lib/db-migrations'
import { runStartupValidation } from '../lib/startup-checks'

async function startup() {
  console.log('🚀 Starting PaperNugget application...')

  try {
    // Run database migrations first
    await runMigrations()
    console.log('✅ Database migrations completed successfully')

    // Run startup validation checks
    const validation = await runStartupValidation()

    if (validation.success) {
      console.log('✅ All startup validation checks passed')
    } else {
      console.log('⚠️  Some startup validation checks failed:')
      validation.errors.forEach(error => console.log(`   ❌ ${error}`))

      if (validation.warnings.length > 0) {
        console.log('⚠️  Startup warnings:')
        validation.warnings.forEach(warning => console.log(`   ⚠️  ${warning}`))
      }

      // Don't exit on validation failures - let the app start
      // The health endpoint will report the issues
      console.log('🔄 Application will continue starting despite validation issues')
    }

  } catch (error) {
    console.error('❌ Failed during startup:', error)
    // Don't exit the process - let the app start anyway
    // This ensures the app can start even if some checks fail
  }

  console.log('🎯 PaperNugget startup sequence completed')
}

// Only run if this script is executed directly
if (require.main === module) {
  startup()
}

export { startup }
