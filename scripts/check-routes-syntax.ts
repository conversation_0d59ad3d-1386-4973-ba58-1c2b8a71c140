#!/usr/bin/env node

import { spawn } from 'child_process'
import { readdir, stat } from 'fs/promises'
import { join } from 'path'

interface SyntaxCheckResult {
  file: string
  status: 'pass' | 'fail'
  errors?: string[]
}

async function findRouteFiles(dir: string): Promise<string[]> {
  const routeFiles: string[] = []
  
  async function scanDirectory(currentDir: string) {
    try {
      const entries = await readdir(currentDir)
      
      for (const entry of entries) {
        const fullPath = join(currentDir, entry)
        const stats = await stat(fullPath)
        
        if (stats.isDirectory()) {
          await scanDirectory(fullPath)
        } else if (entry === 'route.ts' || entry === 'route.js') {
          routeFiles.push(fullPath)
        }
      }
    } catch (error) {
      console.warn(`Warning: Could not scan directory ${currentDir}:`, error.message)
    }
  }
  
  await scanDirectory(dir)
  return routeFiles
}

async function checkFileSyntax(filePath: string): Promise<SyntaxCheckResult> {
  return new Promise((resolve) => {
    const tsc = spawn('npx', ['tsc', '--noEmit', '--skipLibCheck', filePath], {
      stdio: ['pipe', 'pipe', 'pipe']
    })
    
    let stdout = ''
    let stderr = ''
    
    tsc.stdout.on('data', (data) => {
      stdout += data.toString()
    })
    
    tsc.stderr.on('data', (data) => {
      stderr += data.toString()
    })
    
    tsc.on('close', (code) => {
      if (code === 0) {
        resolve({ file: filePath, status: 'pass' })
      } else {
        const errors = stderr.split('\n').filter(line => line.trim().length > 0)
        resolve({ 
          file: filePath, 
          status: 'fail', 
          errors: errors.length > 0 ? errors : [stdout] 
        })
      }
    })
    
    tsc.on('error', (error) => {
      resolve({ 
        file: filePath, 
        status: 'fail', 
        errors: [`Failed to run TypeScript compiler: ${error.message}`] 
      })
    })
  })
}

async function main() {
  console.log('🔍 Checking syntax of all route files...\n')
  
  const appDir = join(process.cwd(), 'app')
  const routeFiles = await findRouteFiles(appDir)
  
  if (routeFiles.length === 0) {
    console.log('❌ No route files found!')
    process.exit(1)
  }
  
  console.log(`Found ${routeFiles.length} route files:\n`)
  
  const results: SyntaxCheckResult[] = []
  
  for (const file of routeFiles) {
    const relativePath = file.replace(process.cwd() + '/', '')
    process.stdout.write(`Checking ${relativePath}... `)
    
    const result = await checkFileSyntax(file)
    results.push(result)
    
    if (result.status === 'pass') {
      console.log('✅ PASS')
    } else {
      console.log('❌ FAIL')
    }
  }
  
  console.log('\n' + '='.repeat(60))
  console.log('📊 SYNTAX CHECK SUMMARY')
  console.log('='.repeat(60))
  
  const passed = results.filter(r => r.status === 'pass')
  const failed = results.filter(r => r.status === 'fail')
  
  console.log(`✅ Passed: ${passed.length}`)
  console.log(`❌ Failed: ${failed.length}`)
  console.log(`📁 Total:  ${results.length}`)
  
  if (failed.length > 0) {
    console.log('\n🚨 FAILED FILES:')
    console.log('-'.repeat(40))
    
    failed.forEach(result => {
      console.log(`\n📄 ${result.file.replace(process.cwd() + '/', '')}`)
      if (result.errors) {
        result.errors.forEach(error => {
          console.log(`   ${error}`)
        })
      }
    })
    
    console.log('\n💡 Fix these syntax errors before deploying!')
    process.exit(1)
  } else {
    console.log('\n🎉 All route files have valid syntax!')
    process.exit(0)
  }
}

main().catch(error => {
  console.error('❌ Script failed:', error)
  process.exit(1)
})
