#!/usr/bin/env node

/**
 * Manual database migration script
 * Run this script to add the missing last_interval column to existing databases
 * 
 * Usage: node scripts/migrate-database.js
 */

const { Pool } = require('pg')

const pool = new Pool({
  connectionString: process.env.DATABASE_URL || 'postgresql://papernugget:password@localhost:5432/papernugget',
  max: 20,
  idleTimeoutMillis: 30000,
  connectionTimeoutMillis: 2000,
})

async function runMigration() {
  try {
    console.log('🔄 Starting database migration...')
    
    // Check if the column exists
    const columnCheck = await pool.query(`
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'reviews' 
      AND column_name = 'last_interval'
    `)
    
    if (columnCheck.rows.length === 0) {
      console.log('📝 Adding last_interval column to reviews table...')
      
      // Add the column
      await pool.query('ALTER TABLE reviews ADD COLUMN last_interval INTEGER DEFAULT 1')
      
      // Update existing reviews to have a default last_interval of 1
      const updateResult = await pool.query('UPDATE reviews SET last_interval = 1 WHERE last_interval IS NULL')
      
      console.log(`✅ Successfully added last_interval column and updated ${updateResult.rowCount} existing reviews`)
    } else {
      console.log('ℹ️  last_interval column already exists - no migration needed')
    }
    
    console.log('🎉 Database migration completed successfully!')
    
  } catch (error) {
    console.error('❌ Error running database migration:', error)
    process.exit(1)
  } finally {
    await pool.end()
  }
}

// Run the migration
runMigration()
