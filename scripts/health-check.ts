#!/usr/bin/env npx tsx

/**
 * Health check script for PaperNugget
 * Can be used for monitoring, CI/CD, or manual health verification
 * 
 * Usage:
 *   npx tsx scripts/health-check.ts [--detailed] [--url=http://localhost:3000]
 */

import { runStartupValidation } from '../lib/startup-checks'

interface HealthCheckOptions {
  detailed: boolean
  url: string
  timeout: number
}

function parseArgs(): HealthCheckOptions {
  const args = process.argv.slice(2)
  
  const options: HealthCheckOptions = {
    detailed: false,
    url: 'http://localhost:3000',
    timeout: 30000, // 30 seconds
  }
  
  for (const arg of args) {
    if (arg === '--detailed') {
      options.detailed = true
    } else if (arg.startsWith('--url=')) {
      options.url = arg.split('=')[1]
    } else if (arg.startsWith('--timeout=')) {
      options.timeout = parseInt(arg.split('=')[1]) * 1000
    } else if (arg === '--help' || arg === '-h') {
      console.log(`
PaperNugget Health Check Script

Usage: npx tsx scripts/health-check.ts [options]

Options:
  --detailed          Run detailed health checks (slower but comprehensive)
  --url=URL          Health endpoint URL (default: http://localhost:3000)
  --timeout=SECONDS  Request timeout in seconds (default: 30)
  --help, -h         Show this help message

Examples:
  npx tsx scripts/health-check.ts
  npx tsx scripts/health-check.ts --detailed
  npx tsx scripts/health-check.ts --url=http://production.example.com
      `)
      process.exit(0)
    }
  }
  
  return options
}

async function checkHttpHealth(url: string, detailed: boolean, timeout: number): Promise<any> {
  const healthUrl = `${url}/api/health${detailed ? '?detailed=true' : ''}`
  
  try {
    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), timeout)
    
    const response = await fetch(healthUrl, {
      signal: controller.signal,
      headers: {
        'Accept': 'application/json',
      },
    })
    
    clearTimeout(timeoutId)
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }
    
    return await response.json()
  } catch (error) {
    if (error instanceof Error && error.name === 'AbortError') {
      throw new Error(`Health check timed out after ${timeout / 1000} seconds`)
    }
    throw error
  }
}

async function runDirectHealthCheck(): Promise<any> {
  console.log('🔍 Running direct health check (bypassing HTTP)...')
  
  try {
    const validation = await runStartupValidation()
    
    return {
      status: validation.success ? 'healthy' : 'unhealthy',
      timestamp: new Date().toISOString(),
      summary: {
        totalChecks: validation.checks.length,
        passed: validation.checks.filter(c => c.status === 'pass').length,
        warnings: validation.warnings.length,
        errors: validation.errors.length,
      },
      checks: validation.checks,
      errors: validation.errors,
      warnings: validation.warnings,
      mode: 'direct',
    }
  } catch (error) {
    return {
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      error: error instanceof Error ? error.message : 'Direct health check failed',
      mode: 'direct',
    }
  }
}

function printHealthResult(result: any, options: HealthCheckOptions) {
  console.log('\n📊 Health Check Results')
  console.log('========================')
  console.log(`Status: ${result.status === 'healthy' ? '✅ HEALTHY' : '❌ UNHEALTHY'}`)
  console.log(`Timestamp: ${result.timestamp}`)
  
  if (result.summary) {
    console.log(`\n📈 Summary:`)
    console.log(`  Total Checks: ${result.summary.totalChecks || result.summary.total || 0}`)
    console.log(`  Passed: ${result.summary.passed || result.summary.healthy || 0}`)
    console.log(`  Warnings: ${result.summary.warnings || 0}`)
    console.log(`  Errors: ${result.summary.errors || result.summary.unhealthy || 0}`)
  }
  
  if (options.detailed && result.checks) {
    console.log('\n🔍 Detailed Checks:')
    for (const check of result.checks) {
      const icon = check.status === 'pass' ? '✅' : 
                   check.status === 'warning' ? '⚠️' : '❌'
      console.log(`  ${icon} ${check.name}: ${check.message}`)
      
      if (check.details && typeof check.details === 'object') {
        const details = JSON.stringify(check.details, null, 2)
          .split('\n')
          .map(line => `    ${line}`)
          .join('\n')
        console.log(details)
      }
    }
  }
  
  if (result.errors && result.errors.length > 0) {
    console.log('\n❌ Errors:')
    result.errors.forEach((error: string) => console.log(`  - ${error}`))
  }
  
  if (result.warnings && result.warnings.length > 0) {
    console.log('\n⚠️  Warnings:')
    result.warnings.forEach((warning: string) => console.log(`  - ${warning}`))
  }
  
  if (result.error) {
    console.log(`\n❌ Error: ${result.error}`)
  }
  
  console.log('\n' + '='.repeat(50))
}

async function main() {
  const options = parseArgs()
  
  console.log('🏥 PaperNugget Health Check')
  console.log(`Mode: ${options.detailed ? 'Detailed' : 'Standard'}`)
  console.log(`Target: ${options.url}`)
  console.log(`Timeout: ${options.timeout / 1000}s`)
  
  let result: any
  let success = false
  
  try {
    // Try HTTP health check first
    console.log('\n🌐 Checking health via HTTP endpoint...')
    result = await checkHttpHealth(options.url, options.detailed, options.timeout)
    success = result.status === 'healthy'
    
  } catch (httpError) {
    console.log(`❌ HTTP health check failed: ${httpError instanceof Error ? httpError.message : 'Unknown error'}`)
    
    // Fall back to direct health check if HTTP fails
    console.log('\n🔄 Falling back to direct health check...')
    try {
      result = await runDirectHealthCheck()
      success = result.status === 'healthy'
    } catch (directError) {
      console.log(`❌ Direct health check also failed: ${directError instanceof Error ? directError.message : 'Unknown error'}`)
      result = {
        status: 'unhealthy',
        timestamp: new Date().toISOString(),
        error: 'Both HTTP and direct health checks failed',
        httpError: httpError instanceof Error ? httpError.message : 'Unknown HTTP error',
        directError: directError instanceof Error ? directError.message : 'Unknown direct error',
      }
      success = false
    }
  }
  
  // Print results
  printHealthResult(result, options)
  
  // Exit with appropriate code
  process.exit(success ? 0 : 1)
}

// Handle unhandled errors
process.on('unhandledRejection', (error) => {
  console.error('❌ Unhandled error during health check:', error)
  process.exit(1)
})

process.on('SIGINT', () => {
  console.log('\n⚠️  Health check interrupted')
  process.exit(1)
})

// Run the health check
main().catch((error) => {
  console.error('❌ Health check failed:', error)
  process.exit(1)
})
