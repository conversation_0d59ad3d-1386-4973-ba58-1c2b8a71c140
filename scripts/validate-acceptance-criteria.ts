#!/usr/bin/env npx tsx

/**
 * Acceptance Criteria Validation Script
 * 
 * Tests that a fresh clone followed by a single bootstrap command results in:
 * 1. A fully working app with seeded admin account
 * 2. Passing all tests
 * 3. Functioning email verification
 * 
 * Usage:
 *   npx tsx scripts/validate-acceptance-criteria.ts [--skip-clone] [--verbose]
 */

import { spawn } from 'child_process'
import { existsSync, rmSync } from 'fs'
import { join } from 'path'

interface ValidationOptions {
  skipClone: boolean
  verbose: boolean
  testDir: string
}

interface ValidationResult {
  step: string
  success: boolean
  message: string
  duration: number
  details?: any
}

function parseArgs(): ValidationOptions {
  const args = process.argv.slice(2)
  
  const options: ValidationOptions = {
    skipClone: false,
    verbose: false,
    testDir: '/tmp/papernugget-acceptance-test'
  }
  
  for (const arg of args) {
    if (arg === '--skip-clone') {
      options.skipClone = true
    } else if (arg === '--verbose') {
      options.verbose = true
    } else if (arg.startsWith('--test-dir=')) {
      options.testDir = arg.split('=')[1]
    } else if (arg === '--help' || arg === '-h') {
      console.log(`
Acceptance Criteria Validation Script

Usage: npx tsx scripts/validate-acceptance-criteria.ts [options]

Options:
  --skip-clone          Skip git clone step (use current directory)
  --verbose             Show detailed output
  --test-dir=PATH       Directory for test clone (default: /tmp/papernugget-acceptance-test)
  --help, -h           Show this help message

This script validates that PaperNugget meets all acceptance criteria:
1. Fresh clone + bootstrap results in working app
2. Admin account is seeded and functional
3. All tests pass
4. Email verification works end-to-end
      `)
      process.exit(0)
    }
  }
  
  return options
}

async function runCommand(command: string, cwd: string, timeout = 300000): Promise<{ success: boolean; output: string; duration: number }> {
  const start = Date.now()
  
  return new Promise((resolve) => {
    const child = spawn('bash', ['-c', command], {
      cwd,
      stdio: 'pipe',
      env: { ...process.env, CI: 'true' }
    })

    let output = ''
    let errorOutput = ''

    child.stdout?.on('data', (data) => {
      output += data.toString()
    })

    child.stderr?.on('data', (data) => {
      errorOutput += data.toString()
    })

    const timeoutId = setTimeout(() => {
      child.kill('SIGKILL')
      resolve({
        success: false,
        output: output + errorOutput + '\n[TIMEOUT]',
        duration: Date.now() - start
      })
    }, timeout)

    child.on('close', (code) => {
      clearTimeout(timeoutId)
      resolve({
        success: code === 0,
        output: output + errorOutput,
        duration: Date.now() - start
      })
    })

    child.on('error', (error) => {
      clearTimeout(timeoutId)
      resolve({
        success: false,
        output: error.message,
        duration: Date.now() - start
      })
    })
  })
}

async function validateStep(
  step: string,
  command: string,
  cwd: string,
  options: ValidationOptions,
  timeout = 300000
): Promise<ValidationResult> {
  console.log(`\n🔍 ${step}...`)
  
  const start = Date.now()
  const result = await runCommand(command, cwd, timeout)
  const duration = Date.now() - start
  
  if (result.success) {
    console.log(`✅ ${step} - Success (${duration}ms)`)
    if (options.verbose) {
      console.log(`Output: ${result.output.slice(-500)}`) // Last 500 chars
    }
  } else {
    console.log(`❌ ${step} - Failed (${duration}ms)`)
    console.log(`Error: ${result.output.slice(-1000)}`) // Last 1000 chars
  }
  
  return {
    step,
    success: result.success,
    message: result.success ? 'Success' : 'Failed',
    duration,
    details: options.verbose ? { output: result.output } : undefined
  }
}

async function main() {
  const options = parseArgs()
  const results: ValidationResult[] = []
  
  console.log('🧪 PaperNugget Acceptance Criteria Validation')
  console.log('==============================================')
  console.log(`Test Directory: ${options.testDir}`)
  console.log(`Skip Clone: ${options.skipClone}`)
  console.log(`Verbose: ${options.verbose}`)
  console.log('')
  
  let workingDir = options.testDir
  
  try {
    // Step 1: Fresh clone (unless skipped)
    if (!options.skipClone) {
      // Clean up any existing test directory
      if (existsSync(options.testDir)) {
        console.log(`🧹 Cleaning up existing test directory: ${options.testDir}`)
        rmSync(options.testDir, { recursive: true, force: true })
      }
      
      const currentRepo = process.cwd()
      const cloneResult = await validateStep(
        'Clone repository',
        `git clone ${currentRepo} ${options.testDir}`,
        '/tmp',
        options,
        60000 // 1 minute timeout for clone
      )
      results.push(cloneResult)
      
      if (!cloneResult.success) {
        throw new Error('Failed to clone repository')
      }
    } else {
      workingDir = process.cwd()
      console.log(`⏭️  Skipping clone, using current directory: ${workingDir}`)
    }
    
    // Step 2: Bootstrap process
    const bootstrapResult = await validateStep(
      'Bootstrap application',
      './bootstrap.sh',
      workingDir,
      options,
      600000 // 10 minutes timeout for bootstrap
    )
    results.push(bootstrapResult)
    
    if (!bootstrapResult.success) {
      throw new Error('Bootstrap process failed')
    }
    
    // Step 3: Verify application is running
    const healthResult = await validateStep(
      'Verify application health',
      'curl -f http://localhost:3000/api/health',
      workingDir,
      options,
      30000 // 30 seconds timeout
    )
    results.push(healthResult)
    
    // Step 4: Verify detailed health check
    const detailedHealthResult = await validateStep(
      'Verify detailed health check',
      'docker compose exec -T app npm run health:detailed',
      workingDir,
      options,
      60000 // 1 minute timeout
    )
    results.push(detailedHealthResult)
    
    // Step 5: Verify admin account exists and works
    const adminLoginResult = await validateStep(
      'Verify admin account login',
      `curl -f -X POST http://localhost:3000/api/auth/login -H "Content-Type: application/json" -d '{"email":"<EMAIL>","password":"admin123"}' | grep -q token`,
      workingDir,
      options,
      30000
    )
    results.push(adminLoginResult)
    
    // Step 6: Verify database seeding
    const seedingResult = await validateStep(
      'Verify database seeding',
      'docker compose exec -T db psql -U papernugget -d papernugget -c "SELECT COUNT(*) FROM papers;" | grep -q "3"',
      workingDir,
      options,
      30000
    )
    results.push(seedingResult)
    
    // Step 7: Run all tests
    const testsResult = await validateStep(
      'Run all tests',
      'docker compose exec -T app npm test',
      workingDir,
      options,
      300000 // 5 minutes timeout for tests
    )
    results.push(testsResult)
    
    // Step 8: Verify email system
    const emailResult = await validateStep(
      'Verify email system',
      'docker compose exec -T app npm run email:test',
      workingDir,
      options,
      60000
    )
    results.push(emailResult)
    
    // Step 9: Verify Mailpit is accessible
    const mailpitResult = await validateStep(
      'Verify Mailpit accessibility',
      'curl -f http://localhost:8025/api/v1/info',
      workingDir,
      options,
      30000
    )
    results.push(mailpitResult)
    
    // Step 10: Test email verification flow
    const emailVerificationResult = await validateStep(
      'Test email verification flow',
      'docker compose exec -T app npm run test:integration -- tests/integration/email-verification-e2e.test.ts',
      workingDir,
      options,
      120000 // 2 minutes timeout
    )
    results.push(emailVerificationResult)
    
  } catch (error) {
    console.error(`\n❌ Validation failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
  } finally {
    // Cleanup
    if (!options.skipClone && workingDir !== process.cwd()) {
      console.log(`\n🧹 Cleaning up test environment...`)
      try {
        await runCommand('docker compose down -v', workingDir, 60000)
        rmSync(workingDir, { recursive: true, force: true })
      } catch (error) {
        console.warn(`Warning: Failed to cleanup: ${error}`)
      }
    }
  }
  
  // Print summary
  console.log('\n📊 Validation Results Summary')
  console.log('==============================')
  
  const totalSteps = results.length
  const passedSteps = results.filter(r => r.success).length
  const failedSteps = totalSteps - passedSteps
  const totalDuration = results.reduce((sum, r) => sum + r.duration, 0)
  
  console.log(`Total Steps: ${totalSteps}`)
  console.log(`✅ Passed: ${passedSteps}`)
  console.log(`❌ Failed: ${failedSteps}`)
  console.log(`⏱️  Total Duration: ${Math.round(totalDuration / 1000)}s`)
  
  console.log('\n📋 Step Details:')
  for (const result of results) {
    const status = result.success ? '✅' : '❌'
    const duration = Math.round(result.duration / 1000)
    console.log(`  ${status} ${result.step} (${duration}s)`)
  }
  
  if (failedSteps > 0) {
    console.log('\n❌ Failed Steps:')
    const failed = results.filter(r => !r.success)
    for (const result of failed) {
      console.log(`  - ${result.step}: ${result.message}`)
    }
  }
  
  console.log('\n' + '='.repeat(50))
  
  if (failedSteps === 0) {
    console.log('🎉 All acceptance criteria validated successfully!')
    console.log('')
    console.log('✅ Fresh clone + bootstrap results in working app')
    console.log('✅ Admin account is seeded and functional')
    console.log('✅ All tests pass')
    console.log('✅ Email verification works end-to-end')
    process.exit(0)
  } else {
    console.log(`💥 ${failedSteps} acceptance criteria failed!`)
    console.log('')
    console.log('Please review the failed steps and fix the issues.')
    console.log('See docs/TROUBLESHOOTING.md for help.')
    process.exit(1)
  }
}

// Handle unhandled errors
process.on('unhandledRejection', (error) => {
  console.error('❌ Unhandled error during validation:', error)
  process.exit(1)
})

process.on('SIGINT', () => {
  console.log('\n⚠️  Validation interrupted')
  process.exit(1)
})

// Run the validation
main().catch((error) => {
  console.error('❌ Validation script failed:', error)
  process.exit(1)
})
