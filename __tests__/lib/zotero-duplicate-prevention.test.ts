import { ZoteroClient } from '@/lib/zotero-client'
import { ZoteroSyncService } from '@/lib/zotero-sync-service'
import { Paper, Note, ZoteroSettings } from '@/lib/types'

// Mock the database
jest.mock('@/lib/database', () => ({
  papers: {
    getById: jest.fn(),
    update: jest.fn(),
  },
  notes: {
    getByPaperId: jest.fn(),
  },
}))

// Mock fetch for Zotero API calls
global.fetch = jest.fn()

describe('Zotero Duplicate Prevention', () => {
  let zoteroClient: ZoteroClient
  let syncService: ZoteroSyncService
  const mockApiKey = 'test-api-key'
  const mockSettings: ZoteroSettings = {
    enabled: true,
    apiKey: mockApiKey,
    libraryType: 'user',
    libraryId: 'user',
    userId: 'test-user-id'
  }

  beforeEach(() => {
    jest.clearAllMocks()
    zoteroClient = new ZoteroClient(mockApiKey)
    syncService = new ZoteroSyncService(mockApiKey, mockSettings)
  })

  describe('ZoteroClient.findItemByTitle', () => {
    it('should find exact title match', async () => {
      const mockResponse = {
        ok: true,
        json: () => Promise.resolve([
          {
            data: {
              key: 'ITEM123',
              title: 'Machine Learning in Healthcare',
              date: '2023',
              itemType: 'journalArticle'
            }
          }
        ])
      }
      
      ;(fetch as jest.Mock).mockResolvedValue(mockResponse)

      const result = await zoteroClient.findItemByTitle('user', 'user', 'Machine Learning in Healthcare', 2023)
      
      expect(result).toBeTruthy()
      expect(result?.key).toBe('ITEM123')
      expect(result?.title).toBe('Machine Learning in Healthcare')
    })

    it('should find title match with normalized punctuation', async () => {
      const mockResponse = {
        ok: true,
        json: () => Promise.resolve([
          {
            data: {
              key: 'ITEM456',
              title: 'Deep Learning: A Comprehensive Review',
              date: '2022',
              itemType: 'journalArticle'
            }
          }
        ])
      }
      
      ;(fetch as jest.Mock).mockResolvedValue(mockResponse)

      const result = await zoteroClient.findItemByTitle('user', 'user', 'Deep Learning - A Comprehensive Review', 2022)
      
      expect(result).toBeTruthy()
      expect(result?.key).toBe('ITEM456')
    })

    it('should not match titles that are too different', async () => {
      const mockResponse = {
        ok: true,
        json: () => Promise.resolve([
          {
            data: {
              key: 'ITEM789',
              title: 'Completely Different Paper Title',
              date: '2023',
              itemType: 'journalArticle'
            }
          }
        ])
      }
      
      ;(fetch as jest.Mock).mockResolvedValue(mockResponse)

      const result = await zoteroClient.findItemByTitle('user', 'user', 'Machine Learning in Healthcare', 2023)
      
      expect(result).toBeNull()
    })

    it('should match year when provided', async () => {
      const mockResponse = {
        ok: true,
        json: () => Promise.resolve([
          {
            data: {
              key: 'ITEM101',
              title: 'AI Research Paper',
              date: '2023',
              itemType: 'journalArticle'
            }
          },
          {
            data: {
              key: 'ITEM102',
              title: 'AI Research Paper',
              date: '2022',
              itemType: 'journalArticle'
            }
          }
        ])
      }
      
      ;(fetch as jest.Mock).mockResolvedValue(mockResponse)

      const result = await zoteroClient.findItemByTitle('user', 'user', 'AI Research Paper', 2022)
      
      expect(result).toBeTruthy()
      expect(result?.key).toBe('ITEM102')
    })

    it('should return first match when no year provided', async () => {
      const mockResponse = {
        ok: true,
        json: () => Promise.resolve([
          {
            data: {
              key: 'ITEM201',
              title: 'Neural Networks Study',
              date: '2023',
              itemType: 'journalArticle'
            }
          }
        ])
      }
      
      ;(fetch as jest.Mock).mockResolvedValue(mockResponse)

      const result = await zoteroClient.findItemByTitle('user', 'user', 'Neural Networks Study')
      
      expect(result).toBeTruthy()
      expect(result?.key).toBe('ITEM201')
    })

    it('should handle API errors gracefully', async () => {
      ;(fetch as jest.Mock).mockRejectedValue(new Error('API Error'))

      const result = await zoteroClient.findItemByTitle('user', 'user', 'Some Paper Title')
      
      expect(result).toBeNull()
    })
  })

  describe('Title Similarity Matching', () => {
    it('should match titles with different punctuation', () => {
      const client = new ZoteroClient('test')
      const isTitleMatch = (client as any).isTitleMatch.bind(client)
      
      expect(isTitleMatch(
        'Deep Learning: A Survey',
        'Deep Learning - A Survey'
      )).toBe(true)
      
      expect(isTitleMatch(
        'Machine Learning (2023)',
        'Machine Learning 2023'
      )).toBe(true)
    })

    it('should match titles with different case', () => {
      const client = new ZoteroClient('test')
      const isTitleMatch = (client as any).isTitleMatch.bind(client)
      
      expect(isTitleMatch(
        'ARTIFICIAL INTELLIGENCE REVIEW',
        'artificial intelligence review'
      )).toBe(true)
    })

    it('should handle truncated titles', () => {
      const client = new ZoteroClient('test')
      const isTitleMatch = (client as any).isTitleMatch.bind(client)
      
      expect(isTitleMatch(
        'A Very Long Paper Title That Might Be Truncated',
        'A Very Long Paper Title That Might Be'
      )).toBe(true)
    })

    it('should reject titles that are too different', () => {
      const client = new ZoteroClient('test')
      const isTitleMatch = (client as any).isTitleMatch.bind(client)
      
      expect(isTitleMatch(
        'Machine Learning Paper',
        'Computer Vision Study'
      )).toBe(false)
    })
  })

  describe('Year Extraction', () => {
    it('should extract year from date field', () => {
      const client = new ZoteroClient('test')
      const extractYear = (client as any).extractYearFromItem.bind(client)

      expect(extractYear({ date: '2023' })).toBe(2023)
      expect(extractYear({ date: '2023-05-15' })).toBe(2023)
      expect(extractYear({ date: 'May 2023' })).toBe(2023)
    })

    it('should extract year from dateAdded field', () => {
      const client = new ZoteroClient('test')
      const extractYear = (client as any).extractYearFromItem.bind(client)

      expect(extractYear({
        dateAdded: '2023-05-15T10:30:00Z'
      })).toBe(2023)
    })

    it('should return null for invalid dates', () => {
      const client = new ZoteroClient('test')
      const extractYear = (client as any).extractYearFromItem.bind(client)

      expect(extractYear({ date: 'invalid' })).toBeNull()
      expect(extractYear({})).toBeNull()
    })
  })

  describe('ZoteroSyncService Integration', () => {
    const mockPaper: Paper = {
      id: 'paper-123',
      title: 'Machine Learning in Healthcare Applications',
      authors: ['Dr. Smith', 'Dr. Johnson'],
      venue: 'AI Conference',
      year: 2023,
      doi: '10.1000/test.doi',
      url: 'https://example.com/paper',
      abstract: 'This paper explores ML in healthcare',
      tags: ['machine-learning', 'healthcare'],
      starred: false,
      userId: 'user-123',
      createdAt: '2023-01-01T00:00:00Z',
      updatedAt: '2023-01-01T00:00:00Z'
    }

    const mockNote: Note = {
      paperId: 'paper-123',
      quickSummary: 'ML techniques for healthcare',
      keyIdeas: ['Idea 1', 'Idea 2', 'Idea 3'],
      createdAt: '2023-01-01T00:00:00Z',
      updatedAt: '2023-01-01T00:00:00Z'
    }

    beforeEach(() => {
      const { papers, notes } = require('@/lib/database')
      papers.getById.mockResolvedValue(mockPaper)
      papers.update.mockResolvedValue(true)
      notes.getByPaperId.mockResolvedValue(mockNote)
    })

    it('should find existing item by DOI and not create duplicate', async () => {
      // Mock DOI search returning existing item
      const mockExistingItem = {
        key: 'EXISTING123',
        title: 'Machine Learning in Healthcare Applications',
        DOI: '10.1000/test.doi'
      }

      ;(fetch as jest.Mock)
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve([{ data: mockExistingItem }])
        })
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({ successful: [{ key: 'EXISTING123' }] })
        })
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({ successful: [{ key: 'NOTE123' }] })
        })

      const result = await syncService.syncPaper('paper-123')

      expect(result.success).toBe(true)
      expect(result.itemKey).toBe('EXISTING123')

      // Verify that createOrUpdateItem was called with existing key
      expect(fetch).toHaveBeenCalledWith(
        expect.stringContaining('/users/self/items/EXISTING123'),
        expect.objectContaining({ method: 'PUT' })
      )
    })

    it('should find existing item by title when DOI search fails', async () => {
      // Mock paper without DOI
      const paperWithoutDOI = { ...mockPaper, doi: undefined }
      const { papers } = require('@/lib/database')
      papers.getById.mockResolvedValue(paperWithoutDOI)

      // Mock title search returning existing item
      const mockExistingItem = {
        key: 'EXISTING456',
        title: 'Machine Learning in Healthcare Applications',
        date: '2023'
      }

      ;(fetch as jest.Mock)
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve([{ data: mockExistingItem }])
        })
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({ successful: [{ key: 'EXISTING456' }] })
        })
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({ successful: [{ key: 'NOTE456' }] })
        })

      const result = await syncService.syncPaper('paper-123')

      expect(result.success).toBe(true)
      expect(result.itemKey).toBe('EXISTING456')
    })

    it('should create new item when no duplicates found', async () => {
      // Mock searches returning no results
      ;(fetch as jest.Mock)
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve([]) // DOI search - no results
        })
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve([]) // Title search - no results
        })
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({ successful: [{ key: 'NEW123' }] })
        })
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({ successful: [{ key: 'NEWNOTE123' }] })
        })

      const result = await syncService.syncPaper('paper-123')

      expect(result.success).toBe(true)
      expect(result.itemKey).toBe('NEW123')

      // Verify that createOrUpdateItem was called without existing key (POST request)
      expect(fetch).toHaveBeenCalledWith(
        expect.stringContaining('/users/self/items'),
        expect.objectContaining({ method: 'POST' })
      )
    })
  })
})
