import { NextRequest } from 'next/server'
import { GET } from '@/app/api/health/route'

// Mock dependencies
jest.mock('@/lib/db', () => ({
  query: jest.fn(),
}))

jest.mock('@/lib/startup-checks', () => ({
  runStartupValidation: jest.fn(),
  getSystemHealthSummary: jest.fn(),
}))

jest.mock('@/lib/mailer', () => ({
  checkSMTPHealth: jest.fn(),
}))

describe('/api/health', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('GET', () => {
    it('should return basic health status', async () => {
      const request = new NextRequest('http://localhost:3000/api/health')
      
      const response = await GET(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data).toHaveProperty('status')
      expect(data).toHaveProperty('timestamp')
      expect(data).toHaveProperty('uptime')
      expect(data).toHaveProperty('version')
    })

    it('should return detailed health status when requested', async () => {
      const { runStartupValidation } = require('@/lib/startup-checks')
      
      runStartupValidation.mockResolvedValue({
        success: true,
        checks: [
          { name: 'database', status: 'pass', message: 'Connected' },
          { name: 'environment', status: 'pass', message: 'All vars set' }
        ],
        warnings: [],
        errors: []
      })

      const request = new NextRequest('http://localhost:3000/api/health?detailed=true')
      
      const response = await GET(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data).toHaveProperty('status', 'healthy')
      expect(data).toHaveProperty('checks')
      expect(data).toHaveProperty('summary')
      expect(data.summary).toHaveProperty('totalChecks', 2)
      expect(data.summary).toHaveProperty('passed', 2)
    })

    it('should return unhealthy status when checks fail', async () => {
      const { runStartupValidation } = require('@/lib/startup-checks')
      
      runStartupValidation.mockResolvedValue({
        success: false,
        checks: [
          { name: 'database', status: 'fail', message: 'Connection failed' }
        ],
        warnings: [],
        errors: ['Database connection failed']
      })

      const request = new NextRequest('http://localhost:3000/api/health?detailed=true')
      
      const response = await GET(request)
      const data = await response.json()

      expect(response.status).toBe(503)
      expect(data).toHaveProperty('status', 'unhealthy')
      expect(data.errors).toHaveLength(1)
    })

    it('should handle errors gracefully', async () => {
      const { runStartupValidation } = require('@/lib/startup-checks')
      
      runStartupValidation.mockRejectedValue(new Error('Health check failed'))

      const request = new NextRequest('http://localhost:3000/api/health?detailed=true')
      
      const response = await GET(request)
      const data = await response.json()

      expect(response.status).toBe(503)
      expect(data).toHaveProperty('status', 'unhealthy')
      expect(data).toHaveProperty('error')
    })

    it('should include cache control headers', async () => {
      const request = new NextRequest('http://localhost:3000/api/health?detailed=true')
      
      const { runStartupValidation } = require('@/lib/startup-checks')
      runStartupValidation.mockResolvedValue({
        success: true,
        checks: [],
        warnings: [],
        errors: []
      })
      
      const response = await GET(request)

      expect(response.headers.get('Cache-Control')).toBe('no-cache, no-store, must-revalidate')
      expect(response.headers.get('Pragma')).toBe('no-cache')
      expect(response.headers.get('Expires')).toBe('0')
    })
  })
})
