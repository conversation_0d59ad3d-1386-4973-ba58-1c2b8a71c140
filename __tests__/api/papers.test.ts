import { NextRequest } from 'next/server'
import { GET, POST } from '@/app/api/papers/route'

// Mock dependencies
jest.mock('@/lib/database', () => ({
  papers: {
    getByUserId: jest.fn(),
    create: jest.fn(),
  },
  reviews: {
    create: jest.fn(),
  }
}))

jest.mock('@/lib/transaction-wrapper', () => ({
  createPaperWithReview: jest.fn(),
}))

jest.mock('@/lib/auth', () => ({
  verifyToken: jest.fn(),
}))

jest.mock('@/lib/logging', () => ({
  createRequestLogger: jest.fn(() => ({
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    recordSuccess: jest.fn(),
  })),
  logAuditEvent: jest.fn(),
}))

describe('/api/papers', () => {
  const mockUser = {
    id: 'user-123',
    email: '<EMAIL>',
    displayName: 'Test User',
    role: 'user',
    emailVerified: true
  }

  const mockPaper = {
    id: 'paper-123',
    title: 'Test Paper',
    authors: ['Author One', 'Author Two'],
    venue: 'Test Conference',
    year: 2023,
    doi: '10.1000/test.doi',
    abstract: 'This is a test paper abstract.',
    userId: 'user-123',
    starred: false,
    tags: ['test', 'paper'],
    quickSummary: 'A test paper for testing',
    keyIdeas: ['Idea 1', 'Idea 2', 'Idea 3'],
    createdAt: '2025-01-01T00:00:00.000Z',
    updatedAt: '2025-01-01T00:00:00.000Z'
  }

  beforeEach(() => {
    jest.clearAllMocks()
    
    // Mock authentication
    const { verifyToken } = require('@/lib/auth')
    verifyToken.mockResolvedValue(mockUser)
  })

  describe('GET', () => {
    it('should return user papers with pagination', async () => {
      const { papers } = require('@/lib/database')
      
      papers.getByUserId.mockResolvedValue([mockPaper])

      const request = new NextRequest('http://localhost:3000/api/papers', {
        headers: { 'Authorization': 'Bearer valid-token' }
      })

      const response = await GET(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.data).toHaveLength(1)
      expect(data.data[0]).toEqual(mockPaper)
      expect(data).toHaveProperty('pagination')
      expect(data.pagination).toHaveProperty('page', 1)
      expect(data.pagination).toHaveProperty('limit', 20)
      expect(data.pagination).toHaveProperty('total', 1)
    })

    it('should filter papers by search term', async () => {
      const { papers } = require('@/lib/database')
      
      const papers_list = [
        { ...mockPaper, title: 'Machine Learning Paper' },
        { ...mockPaper, id: 'paper-456', title: 'Deep Learning Study' },
        { ...mockPaper, id: 'paper-789', title: 'Computer Vision Research' }
      ]
      
      papers.getByUserId.mockResolvedValue(papers_list)

      const request = new NextRequest('http://localhost:3000/api/papers?search=learning', {
        headers: { 'Authorization': 'Bearer valid-token' }
      })

      const response = await GET(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.data).toHaveLength(2)
      expect(data.data.every(paper => 
        paper.title.toLowerCase().includes('learning')
      )).toBe(true)
    })

    it('should filter papers by starred status', async () => {
      const { papers } = require('@/lib/database')
      
      const papers_list = [
        { ...mockPaper, starred: true },
        { ...mockPaper, id: 'paper-456', starred: false }
      ]
      
      papers.getByUserId.mockResolvedValue(papers_list)

      const request = new NextRequest('http://localhost:3000/api/papers?starred=true', {
        headers: { 'Authorization': 'Bearer valid-token' }
      })

      const response = await GET(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.data).toHaveLength(1)
      expect(data.data[0].starred).toBe(true)
    })

    it('should sort papers by specified field', async () => {
      const { papers } = require('@/lib/database')
      
      const papers_list = [
        { ...mockPaper, title: 'B Paper', year: 2023 },
        { ...mockPaper, id: 'paper-456', title: 'A Paper', year: 2022 },
        { ...mockPaper, id: 'paper-789', title: 'C Paper', year: 2024 }
      ]
      
      papers.getByUserId.mockResolvedValue(papers_list)

      const request = new NextRequest('http://localhost:3000/api/papers?sortBy=title&sortOrder=asc', {
        headers: { 'Authorization': 'Bearer valid-token' }
      })

      const response = await GET(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.data[0].title).toBe('A Paper')
      expect(data.data[1].title).toBe('B Paper')
      expect(data.data[2].title).toBe('C Paper')
    })

    it('should require authentication', async () => {
      const { verifyToken } = require('@/lib/auth')
      verifyToken.mockRejectedValue(new Error('Invalid token'))

      const request = new NextRequest('http://localhost:3000/api/papers')

      const response = await GET(request)
      const data = await response.json()

      expect(response.status).toBe(401)
      expect(data).toHaveProperty('error', 'Authentication required')
    })
  })

  describe('POST', () => {
    const validPaperData = {
      title: 'New Test Paper',
      authors: ['New Author'],
      venue: 'New Conference',
      year: 2024,
      abstract: 'New paper abstract',
      tags: ['new', 'test'],
      quickSummary: 'A new test paper',
      keyIdeas: ['New idea 1', 'New idea 2']
    }

    it('should create a new paper with review', async () => {
      const { createPaperWithReview } = require('@/lib/transaction-wrapper')
      
      const createdPaper = {
        ...validPaperData,
        id: 'paper-new-123',
        userId: 'user-123',
        starred: false,
        createdAt: '2025-01-01T00:00:00.000Z',
        updatedAt: '2025-01-01T00:00:00.000Z'
      }
      
      createPaperWithReview.mockResolvedValue(createdPaper)

      const request = new NextRequest('http://localhost:3000/api/papers', {
        method: 'POST',
        headers: { 
          'Authorization': 'Bearer valid-token',
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(validPaperData)
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(201)
      expect(data.data).toEqual(createdPaper)
      expect(createPaperWithReview).toHaveBeenCalledWith(
        expect.objectContaining({
          ...validPaperData,
          userId: 'user-123'
        }),
        expect.objectContaining({
          ease: 2.5,
          lastInterval: 1
        })
      )
    })

    it('should support idempotency', async () => {
      const { createPaperWithReview } = require('@/lib/transaction-wrapper')
      
      const createdPaper = {
        ...validPaperData,
        id: 'paper-new-123',
        userId: 'user-123',
        starred: false,
        createdAt: '2025-01-01T00:00:00.000Z',
        updatedAt: '2025-01-01T00:00:00.000Z'
      }
      
      createPaperWithReview.mockResolvedValue(createdPaper)

      const request = new NextRequest('http://localhost:3000/api/papers', {
        method: 'POST',
        headers: { 
          'Authorization': 'Bearer valid-token',
          'Content-Type': 'application/json',
          'Idempotency-Key': 'test-key-123'
        },
        body: JSON.stringify(validPaperData)
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(201)
      expect(data.data).toEqual(createdPaper)
    })

    it('should validate required fields', async () => {
      const request = new NextRequest('http://localhost:3000/api/papers', {
        method: 'POST',
        headers: { 
          'Authorization': 'Bearer valid-token',
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          // Missing title
          authors: ['Author']
        })
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data).toHaveProperty('error', 'Validation failed')
    })

    it('should validate key ideas limit', async () => {
      const request = new NextRequest('http://localhost:3000/api/papers', {
        method: 'POST',
        headers: { 
          'Authorization': 'Bearer valid-token',
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          ...validPaperData,
          keyIdeas: ['Idea 1', 'Idea 2', 'Idea 3', 'Idea 4'] // Too many
        })
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data).toHaveProperty('error', 'Validation failed')
    })

    it('should handle database errors', async () => {
      const { createPaperWithReview } = require('@/lib/transaction-wrapper')
      
      createPaperWithReview.mockRejectedValue(new Error('Database error'))

      const request = new NextRequest('http://localhost:3000/api/papers', {
        method: 'POST',
        headers: { 
          'Authorization': 'Bearer valid-token',
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(validPaperData)
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(500)
      expect(data).toHaveProperty('error', 'Internal server error')
    })

    it('should require authentication', async () => {
      const { verifyToken } = require('@/lib/auth')
      verifyToken.mockRejectedValue(new Error('Invalid token'))

      const request = new NextRequest('http://localhost:3000/api/papers', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(validPaperData)
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(401)
      expect(data).toHaveProperty('error', 'Authentication required')
    })
  })
})
