import { NextRequest } from 'next/server'
import { POST } from '@/app/api/auth/login/route'

// Mock dependencies
jest.mock('@/lib/database', () => ({
  users: {
    getByEmail: jest.fn(),
    updateLastLogin: jest.fn(),
  },
  userSessions: {
    create: jest.fn(),
  }
}))

jest.mock('@/lib/auth', () => ({
  verifyPassword: jest.fn(),
  generateToken: jest.fn(),
}))

jest.mock('@/lib/logging', () => ({
  createRequestLogger: jest.fn(() => ({
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
  })),
  logSecurityEvent: jest.fn(),
  logAuditEvent: jest.fn(),
}))

describe('/api/auth/login', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('POST', () => {
    const validLoginData = {
      email: '<EMAIL>',
      password: 'validPassword123'
    }

    const mockUser = {
      id: 'user-123',
      email: '<EMAIL>',
      passwordHash: 'hashed-password',
      displayName: 'Test User',
      role: 'user',
      emailVerified: true,
      createdAt: '2025-01-01T00:00:00.000Z',
      updatedAt: '2025-01-01T00:00:00.000Z'
    }

    it('should login successfully with valid credentials', async () => {
      const { users, userSessions } = require('@/lib/database')
      const { verifyPassword, generateToken } = require('@/lib/auth')

      users.getByEmail.mockResolvedValue(mockUser)
      verifyPassword.mockResolvedValue(true)
      generateToken.mockReturnValue('jwt-token-123')
      userSessions.create.mockResolvedValue({
        id: 'session-123',
        userId: 'user-123',
        tokenHash: 'hashed-token',
        expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString()
      })

      const request = new NextRequest('http://localhost:3000/api/auth/login', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(validLoginData)
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.data).toHaveProperty('token', 'jwt-token-123')
      expect(data.data).toHaveProperty('user')
      expect(data.data.user).toHaveProperty('id', 'user-123')
      expect(data.data.user).toHaveProperty('email', '<EMAIL>')
      expect(data.data.user).not.toHaveProperty('passwordHash')

      expect(users.getByEmail).toHaveBeenCalledWith('<EMAIL>')
      expect(verifyPassword).toHaveBeenCalledWith('validPassword123', 'hashed-password')
      expect(generateToken).toHaveBeenCalledWith(mockUser)
      expect(users.updateLastLogin).toHaveBeenCalledWith('user-123')
    })

    it('should fail with invalid email', async () => {
      const { users } = require('@/lib/database')
      
      users.getByEmail.mockResolvedValue(null)

      const request = new NextRequest('http://localhost:3000/api/auth/login', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          email: '<EMAIL>',
          password: 'password123'
        })
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(401)
      expect(data).toHaveProperty('error', 'Invalid credentials')
      expect(data).toHaveProperty('message', 'Email or password is incorrect')
    })

    it('should fail with invalid password', async () => {
      const { users } = require('@/lib/database')
      const { verifyPassword } = require('@/lib/auth')

      users.getByEmail.mockResolvedValue(mockUser)
      verifyPassword.mockResolvedValue(false)

      const request = new NextRequest('http://localhost:3000/api/auth/login', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          email: '<EMAIL>',
          password: 'wrongPassword'
        })
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(401)
      expect(data).toHaveProperty('error', 'Invalid credentials')
      expect(verifyPassword).toHaveBeenCalledWith('wrongPassword', 'hashed-password')
    })

    it('should validate required fields', async () => {
      const request = new NextRequest('http://localhost:3000/api/auth/login', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          email: '<EMAIL>'
          // Missing password
        })
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data).toHaveProperty('error', 'Validation failed')
    })

    it('should validate email format', async () => {
      const request = new NextRequest('http://localhost:3000/api/auth/login', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          email: 'invalid-email',
          password: 'password123'
        })
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data).toHaveProperty('error', 'Validation failed')
    })

    it('should handle database errors gracefully', async () => {
      const { users } = require('@/lib/database')
      
      users.getByEmail.mockRejectedValue(new Error('Database connection failed'))

      const request = new NextRequest('http://localhost:3000/api/auth/login', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(validLoginData)
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(500)
      expect(data).toHaveProperty('error', 'Internal server error')
    })

    it('should include rate limiting headers', async () => {
      const { users } = require('@/lib/database')
      const { verifyPassword, generateToken } = require('@/lib/auth')

      users.getByEmail.mockResolvedValue(mockUser)
      verifyPassword.mockResolvedValue(true)
      generateToken.mockReturnValue('jwt-token-123')

      const request = new NextRequest('http://localhost:3000/api/auth/login', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(validLoginData)
      })

      const response = await POST(request)

      expect(response.headers.get('X-RateLimit-Limit')).toBeTruthy()
      expect(response.headers.get('X-RateLimit-Remaining')).toBeTruthy()
      expect(response.headers.get('X-RateLimit-Reset')).toBeTruthy()
    })

    it('should include correlation ID in response', async () => {
      const { users } = require('@/lib/database')
      const { verifyPassword, generateToken } = require('@/lib/auth')

      users.getByEmail.mockResolvedValue(mockUser)
      verifyPassword.mockResolvedValue(true)
      generateToken.mockReturnValue('jwt-token-123')

      const request = new NextRequest('http://localhost:3000/api/auth/login', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(validLoginData)
      })

      const response = await POST(request)
      const data = await response.json()

      expect(data).toHaveProperty('correlationId')
      expect(typeof data.correlationId).toBe('string')
      expect(data.correlationId.length).toBeGreaterThan(0)
    })

    it('should log security events for failed login attempts', async () => {
      const { users } = require('@/lib/database')
      const { logSecurityEvent } = require('@/lib/logging')
      
      users.getByEmail.mockResolvedValue(null)

      const request = new NextRequest('http://localhost:3000/api/auth/login', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          email: '<EMAIL>',
          password: 'password123'
        })
      })

      await POST(request)

      expect(logSecurityEvent).toHaveBeenCalledWith(
        'login_failed',
        expect.objectContaining({
          email: '<EMAIL>',
          reason: 'user_not_found'
        }),
        expect.any(Object),
        expect.any(String)
      )
    })
  })
})
