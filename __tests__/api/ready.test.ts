import { NextRequest } from 'next/server'
import { GET } from '@/app/api/ready/route'

// Mock dependencies
jest.mock('@/lib/db', () => ({
  query: jest.fn(),
}))

describe('/api/ready', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    // Set required environment variables
    process.env.DATABASE_URL = 'postgresql://test:test@localhost:5432/test'
    process.env.JWT_SECRET = 'test-jwt-secret-key-for-testing-only'
    process.env.NEXTAUTH_SECRET = 'test-nextauth-secret-for-testing'
  })

  describe('GET', () => {
    it('should return ready status when all checks pass', async () => {
      const { query } = require('@/lib/db')
      
      // Mock successful database connection and table check
      query
        .mockResolvedValueOnce({ rows: [] }) // SELECT 1
        .mockResolvedValueOnce({ 
          rows: [
            { table_name: 'users' },
            { table_name: 'papers' },
            { table_name: 'collections' }
          ]
        })

      const request = new NextRequest('http://localhost:3000/api/ready')
      
      const response = await GET(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.data).toHaveProperty('status', 'ready')
      expect(data.data).toHaveProperty('checks')
      expect(data.data.checks).toHaveLength(3) // database, environment, memory
      expect(data.data.checks.every(check => check.status === 'ready')).toBe(true)
    })

    it('should return not ready when database is unavailable', async () => {
      const { query } = require('@/lib/db')
      
      query.mockRejectedValue(new Error('Connection failed'))

      const request = new NextRequest('http://localhost:3000/api/ready')
      
      const response = await GET(request)
      const data = await response.json()

      expect(response.status).toBe(503)
      expect(data.data).toHaveProperty('status', 'not_ready')
      
      const dbCheck = data.data.checks.find(check => check.service === 'database')
      expect(dbCheck).toHaveProperty('status', 'not_ready')
      expect(dbCheck).toHaveProperty('message')
      expect(dbCheck.message).toContain('Connection failed')
    })

    it('should return not ready when required tables are missing', async () => {
      const { query } = require('@/lib/db')
      
      query
        .mockResolvedValueOnce({ rows: [] }) // SELECT 1
        .mockResolvedValueOnce({ 
          rows: [
            { table_name: 'users' }
            // Missing papers and collections tables
          ]
        })

      const request = new NextRequest('http://localhost:3000/api/ready')
      
      const response = await GET(request)
      const data = await response.json()

      expect(response.status).toBe(503)
      expect(data.data).toHaveProperty('status', 'not_ready')
      
      const dbCheck = data.data.checks.find(check => check.service === 'database')
      expect(dbCheck).toHaveProperty('status', 'not_ready')
      expect(dbCheck.message).toContain('Missing tables')
    })

    it('should return not ready when environment variables are missing', async () => {
      // Remove required environment variable
      delete process.env.JWT_SECRET

      const { query } = require('@/lib/db')
      query
        .mockResolvedValueOnce({ rows: [] })
        .mockResolvedValueOnce({ 
          rows: [
            { table_name: 'users' },
            { table_name: 'papers' },
            { table_name: 'collections' }
          ]
        })

      const request = new NextRequest('http://localhost:3000/api/ready')
      
      const response = await GET(request)
      const data = await response.json()

      expect(response.status).toBe(503)
      expect(data.data).toHaveProperty('status', 'not_ready')
      
      const envCheck = data.data.checks.find(check => check.service === 'environment')
      expect(envCheck).toHaveProperty('status', 'not_ready')
      expect(envCheck.message).toContain('Missing environment variables')
    })

    it('should include response time in database check', async () => {
      const { query } = require('@/lib/db')
      
      query
        .mockResolvedValueOnce({ rows: [] })
        .mockResolvedValueOnce({ 
          rows: [
            { table_name: 'users' },
            { table_name: 'papers' },
            { table_name: 'collections' }
          ]
        })

      const request = new NextRequest('http://localhost:3000/api/ready')
      
      const response = await GET(request)
      const data = await response.json()

      const dbCheck = data.data.checks.find(check => check.service === 'database')
      expect(dbCheck).toHaveProperty('responseTime')
      expect(typeof dbCheck.responseTime).toBe('number')
      expect(dbCheck.responseTime).toBeGreaterThan(0)
    })

    it('should include summary information', async () => {
      const { query } = require('@/lib/db')
      
      query
        .mockResolvedValueOnce({ rows: [] })
        .mockResolvedValueOnce({ 
          rows: [
            { table_name: 'users' },
            { table_name: 'papers' },
            { table_name: 'collections' }
          ]
        })

      const request = new NextRequest('http://localhost:3000/api/ready')
      
      const response = await GET(request)
      const data = await response.json()

      expect(data.data).toHaveProperty('summary')
      expect(data.data.summary).toHaveProperty('total', 3)
      expect(data.data.summary).toHaveProperty('ready', 3)
      expect(data.data.summary).toHaveProperty('notReady', 0)
    })

    it('should include correlation ID and timestamp', async () => {
      const { query } = require('@/lib/db')
      
      query
        .mockResolvedValueOnce({ rows: [] })
        .mockResolvedValueOnce({ 
          rows: [
            { table_name: 'users' },
            { table_name: 'papers' },
            { table_name: 'collections' }
          ]
        })

      const request = new NextRequest('http://localhost:3000/api/ready')
      
      const response = await GET(request)
      const data = await response.json()

      expect(data).toHaveProperty('correlationId')
      expect(data).toHaveProperty('timestamp')
      expect(typeof data.correlationId).toBe('string')
      expect(data.correlationId.length).toBeGreaterThan(0)
    })
  })
})
