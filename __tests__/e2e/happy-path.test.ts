/**
 * End-to-End Happy Path Tests
 * 
 * These tests simulate real user workflows from registration to paper management
 * and review sessions. They test the complete integration of all components.
 */

import { NextRequest } from 'next/server'

// Import route handlers
import { POST as registerPOST } from '@/app/api/auth/register/route'
import { POST as loginPOST } from '@/app/api/auth/login/route'
import { GET as meGET } from '@/app/api/auth/me/route'
import { GET as papersGET, POST as papersPOST } from '@/app/api/papers/route'
import { GET as collectionsGET, POST as collectionsPOST } from '@/app/api/collections/route'
import { GET as reviewStatsGET } from '@/app/api/review/stats/route'
import { POST as reviewSessionPOST } from '@/app/api/review/session/route'

// Mock all external dependencies
jest.mock('@/lib/db', () => ({
  query: jest.fn(),
  getPool: jest.fn(() => ({
    connect: jest.fn(() => ({
      query: jest.fn(),
      release: jest.fn(),
    })),
  })),
}))

jest.mock('@/lib/database', () => ({
  users: {
    create: jest.fn(),
    getByEmail: jest.fn(),
    updateLastLogin: jest.fn(),
  },
  papers: {
    create: jest.fn(),
    getByUserId: jest.fn(),
  },
  collections: {
    create: jest.fn(),
    getByUserId: jest.fn(),
  },
  reviews: {
    create: jest.fn(),
    getByUserId: jest.fn(),
    getDue: jest.fn(),
  },
  userSessions: {
    create: jest.fn(),
  },
}))

jest.mock('@/lib/auth', () => ({
  hashPassword: jest.fn(),
  verifyPassword: jest.fn(),
  generateToken: jest.fn(),
  verifyToken: jest.fn(),
}))

jest.mock('@/lib/transaction-wrapper', () => ({
  createPaperWithReview: jest.fn(),
}))

jest.mock('@/lib/logging', () => ({
  createRequestLogger: jest.fn(() => ({
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    recordSuccess: jest.fn(),
  })),
  logAuditEvent: jest.fn(),
  logSecurityEvent: jest.fn(),
}))

describe('E2E Happy Path', () => {
  let userToken: string
  let userId: string
  let paperId: string
  let collectionId: string

  const testUser = {
    email: '<EMAIL>',
    password: 'SecurePassword123!',
    displayName: 'E2E Test User'
  }

  const testPaper = {
    title: 'Attention Is All You Need',
    authors: ['Vaswani, A.', 'Shazeer, N.', 'Parmar, N.'],
    venue: 'NIPS',
    year: 2017,
    doi: '10.5555/3295222.3295349',
    abstract: 'The dominant sequence transduction models are based on complex recurrent or convolutional neural networks...',
    quickSummary: 'Introduces the Transformer architecture using only attention mechanisms',
    keyIdeas: [
      'Self-attention mechanism eliminates need for recurrence',
      'Parallel processing enables faster training',
      'Multi-head attention captures different representation subspaces'
    ],
    tags: ['transformers', 'attention', 'nlp']
  }

  beforeAll(() => {
    // Setup mock implementations
    const { users, papers, collections, reviews, userSessions } = require('@/lib/database')
    const { hashPassword, verifyPassword, generateToken, verifyToken } = require('@/lib/auth')
    const { createPaperWithReview } = require('@/lib/transaction-wrapper')

    // Mock user creation and authentication
    userId = 'e2e-user-123'
    userToken = 'e2e-jwt-token-123'
    paperId = 'e2e-paper-123'
    collectionId = 'e2e-collection-123'

    const mockUser = {
      id: userId,
      email: testUser.email,
      displayName: testUser.displayName,
      role: 'user',
      emailVerified: true,
      passwordHash: 'hashed-password',
      createdAt: '2025-01-01T00:00:00.000Z',
      updatedAt: '2025-01-01T00:00:00.000Z'
    }

    const mockPaper = {
      id: paperId,
      ...testPaper,
      userId,
      starred: false,
      createdAt: '2025-01-01T00:00:00.000Z',
      updatedAt: '2025-01-01T00:00:00.000Z'
    }

    const mockCollection = {
      id: collectionId,
      name: 'Important Papers',
      description: 'Collection of important research papers',
      userId,
      createdAt: '2025-01-01T00:00:00.000Z',
      updatedAt: '2025-01-01T00:00:00.000Z'
    }

    // Setup mocks
    hashPassword.mockResolvedValue('hashed-password')
    verifyPassword.mockResolvedValue(true)
    generateToken.mockReturnValue(userToken)
    verifyToken.mockResolvedValue(mockUser)

    users.create.mockResolvedValue(mockUser)
    users.getByEmail.mockResolvedValue(mockUser)
    users.updateLastLogin.mockResolvedValue(undefined)

    userSessions.create.mockResolvedValue({
      id: 'session-123',
      userId,
      tokenHash: 'hashed-token',
      expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString()
    })

    createPaperWithReview.mockResolvedValue(mockPaper)
    papers.getByUserId.mockResolvedValue([mockPaper])

    collections.create.mockResolvedValue(mockCollection)
    collections.getByUserId.mockResolvedValue([mockCollection])

    reviews.getByUserId.mockResolvedValue([{
      paperId,
      ease: 2.5,
      nextDue: new Date().toISOString(),
      lastInterval: 1,
      lastReviewed: null,
      createdAt: '2025-01-01T00:00:00.000Z',
      updatedAt: '2025-01-01T00:00:00.000Z'
    }])

    reviews.getDue.mockResolvedValue([mockPaper])
  })

  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('Complete User Journey', () => {
    it('should complete the full user workflow', async () => {
      // Step 1: User Registration
      console.log('Step 1: User Registration')
      const registerRequest = new NextRequest('http://localhost:3000/api/auth/register', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(testUser)
      })

      const registerResponse = await registerPOST(registerRequest)
      const registerData = await registerResponse.json()

      expect(registerResponse.status).toBe(201)
      expect(registerData.data).toHaveProperty('user')
      expect(registerData.data.user.email).toBe(testUser.email)

      // Step 2: User Login
      console.log('Step 2: User Login')
      const loginRequest = new NextRequest('http://localhost:3000/api/auth/login', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          email: testUser.email,
          password: testUser.password
        })
      })

      const loginResponse = await loginPOST(loginRequest)
      const loginData = await loginResponse.json()

      expect(loginResponse.status).toBe(200)
      expect(loginData.data).toHaveProperty('token', userToken)
      expect(loginData.data).toHaveProperty('user')

      // Step 3: Verify Authentication
      console.log('Step 3: Verify Authentication')
      const meRequest = new NextRequest('http://localhost:3000/api/auth/me', {
        headers: { 'Authorization': `Bearer ${userToken}` }
      })

      const meResponse = await meGET(meRequest)
      const meData = await meResponse.json()

      expect(meResponse.status).toBe(200)
      expect(meData.data.user.id).toBe(userId)
      expect(meData.data.user.email).toBe(testUser.email)

      // Step 4: Create a Paper
      console.log('Step 4: Create a Paper')
      const createPaperRequest = new NextRequest('http://localhost:3000/api/papers', {
        method: 'POST',
        headers: { 
          'Authorization': `Bearer ${userToken}`,
          'Content-Type': 'application/json',
          'Idempotency-Key': 'e2e-test-paper-creation'
        },
        body: JSON.stringify(testPaper)
      })

      const createPaperResponse = await papersPOST(createPaperRequest)
      const createPaperData = await createPaperResponse.json()

      expect(createPaperResponse.status).toBe(201)
      expect(createPaperData.data).toHaveProperty('id', paperId)
      expect(createPaperData.data.title).toBe(testPaper.title)
      expect(createPaperData.data.keyIdeas).toEqual(testPaper.keyIdeas)

      // Step 5: List Papers
      console.log('Step 5: List Papers')
      const listPapersRequest = new NextRequest('http://localhost:3000/api/papers?page=1&limit=20', {
        headers: { 'Authorization': `Bearer ${userToken}` }
      })

      const listPapersResponse = await papersGET(listPapersRequest)
      const listPapersData = await listPapersResponse.json()

      expect(listPapersResponse.status).toBe(200)
      expect(listPapersData.data).toHaveLength(1)
      expect(listPapersData.data[0].id).toBe(paperId)
      expect(listPapersData).toHaveProperty('pagination')

      // Step 6: Create a Collection
      console.log('Step 6: Create a Collection')
      const createCollectionRequest = new NextRequest('http://localhost:3000/api/collections', {
        method: 'POST',
        headers: { 
          'Authorization': `Bearer ${userToken}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          name: 'Important Papers',
          description: 'Collection of important research papers'
        })
      })

      const createCollectionResponse = await collectionsPOST(createCollectionRequest)
      const createCollectionData = await createCollectionResponse.json()

      expect(createCollectionResponse.status).toBe(201)
      expect(createCollectionData.data).toHaveProperty('id', collectionId)
      expect(createCollectionData.data.name).toBe('Important Papers')

      // Step 7: List Collections
      console.log('Step 7: List Collections')
      const listCollectionsRequest = new NextRequest('http://localhost:3000/api/collections', {
        headers: { 'Authorization': `Bearer ${userToken}` }
      })

      const listCollectionsResponse = await collectionsGET(listCollectionsRequest)
      const listCollectionsData = await listCollectionsResponse.json()

      expect(listCollectionsResponse.status).toBe(200)
      expect(listCollectionsData.data).toHaveLength(1)
      expect(listCollectionsData.data[0].id).toBe(collectionId)

      // Step 8: Check Review Statistics
      console.log('Step 8: Check Review Statistics')
      const reviewStatsRequest = new NextRequest('http://localhost:3000/api/review/stats', {
        headers: { 'Authorization': `Bearer ${userToken}` }
      })

      const reviewStatsResponse = await reviewStatsGET(reviewStatsRequest)
      const reviewStatsData = await reviewStatsResponse.json()

      expect(reviewStatsResponse.status).toBe(200)
      expect(reviewStatsData.data).toHaveProperty('totalPapers')
      expect(reviewStatsData.data).toHaveProperty('dueToday')
      expect(reviewStatsData.data).toHaveProperty('overdue')

      // Step 9: Start Review Session
      console.log('Step 9: Start Review Session')
      const reviewSessionRequest = new NextRequest('http://localhost:3000/api/review/session', {
        method: 'POST',
        headers: { 'Authorization': `Bearer ${userToken}` }
      })

      const reviewSessionResponse = await reviewSessionPOST(reviewSessionRequest)
      const reviewSessionData = await reviewSessionResponse.json()

      expect(reviewSessionResponse.status).toBe(200)
      expect(reviewSessionData.data).toHaveProperty('sessionId')
      expect(reviewSessionData.data).toHaveProperty('papers')
      expect(reviewSessionData.data.papers).toHaveLength(1)

      console.log('✅ Complete user workflow test passed!')
    })

    it('should handle error scenarios gracefully', async () => {
      // Test authentication failure
      const unauthorizedRequest = new NextRequest('http://localhost:3000/api/papers', {
        headers: { 'Authorization': 'Bearer invalid-token' }
      })

      const { verifyToken } = require('@/lib/auth')
      verifyToken.mockRejectedValueOnce(new Error('Invalid token'))

      const unauthorizedResponse = await papersGET(unauthorizedRequest)
      const unauthorizedData = await unauthorizedResponse.json()

      expect(unauthorizedResponse.status).toBe(401)
      expect(unauthorizedData).toHaveProperty('error', 'Authentication required')
      expect(unauthorizedData).toHaveProperty('correlationId')

      console.log('✅ Error handling test passed!')
    })
  })
})
