openapi: 3.0.3
info:
  title: PaperNugget API
  description: |
    PaperNugget is a research paper management and spaced repetition review system.
    
    ## Authentication
    Most endpoints require authentication via <PERSON><PERSON><PERSON> token in the Authorization header:
    ```
    Authorization: Bearer <your-jwt-token>
    ```
    
    ## Rate Limiting
    API requests are rate limited. Check response headers for current limits:
    - `X-RateLimit-Limit`: Maximum requests per window
    - `X-RateLimit-Remaining`: Remaining requests in current window
    - `X-RateLimit-Reset`: Unix timestamp when window resets
    
    ## Error Handling
    All errors follow a consistent format:
    ```json
    {
      "error": "Error type",
      "message": "Human readable message",
      "correlationId": "unique-request-id",
      "timestamp": "2025-08-12T12:00:00.000Z",
      "path": "/api/endpoint"
    }
    ```
    
    ## Pagination
    List endpoints support pagination with query parameters:
    - `page`: Page number (default: 1)
    - `limit`: Items per page (default: 20, max: 100)
    - `sortBy`: Field to sort by
    - `sortOrder`: 'asc' or 'desc' (default: 'asc')
    
  version: 1.0.0
  contact:
    name: PaperNugget API Support
    email: <EMAIL>
  license:
    name: MIT
    url: https://opensource.org/licenses/MIT

servers:
  - url: http://localhost:3000/api
    description: Development server
  - url: https://papernugget.com/api
    description: Production server

security:
  - BearerAuth: []

components:
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
      description: JWT token obtained from /auth/login

  schemas:
    Error:
      type: object
      required:
        - error
        - message
        - correlationId
        - timestamp
      properties:
        error:
          type: string
          description: Error type identifier
        message:
          type: string
          description: Human-readable error message
        correlationId:
          type: string
          description: Unique request identifier for debugging
        timestamp:
          type: string
          format: date-time
          description: When the error occurred
        path:
          type: string
          description: API endpoint that generated the error
        details:
          type: object
          description: Additional error details

    PaginationMeta:
      type: object
      required:
        - page
        - limit
        - total
        - totalPages
        - hasNext
        - hasPrev
      properties:
        page:
          type: integer
          description: Current page number
        limit:
          type: integer
          description: Items per page
        total:
          type: integer
          description: Total number of items
        totalPages:
          type: integer
          description: Total number of pages
        hasNext:
          type: boolean
          description: Whether there is a next page
        hasPrev:
          type: boolean
          description: Whether there is a previous page
        nextPage:
          type: integer
          nullable: true
          description: Next page number if available
        prevPage:
          type: integer
          nullable: true
          description: Previous page number if available

    SortingMeta:
      type: object
      required:
        - field
        - order
      properties:
        field:
          type: string
          description: Field being sorted by
        order:
          type: string
          enum: [asc, desc]
          description: Sort order

    User:
      type: object
      required:
        - id
        - email
        - displayName
        - role
        - emailVerified
        - createdAt
      properties:
        id:
          type: string
          description: Unique user identifier
        email:
          type: string
          format: email
          description: User's email address
        displayName:
          type: string
          description: User's display name
        role:
          type: string
          enum: [user, admin]
          description: User's role
        emailVerified:
          type: boolean
          description: Whether email is verified
        lastLogin:
          type: string
          format: date-time
          nullable: true
          description: Last login timestamp
        createdAt:
          type: string
          format: date-time
          description: Account creation timestamp
        updatedAt:
          type: string
          format: date-time
          description: Last update timestamp

    Paper:
      type: object
      required:
        - id
        - title
        - authors
        - userId
        - createdAt
      properties:
        id:
          type: string
          description: Unique paper identifier
        title:
          type: string
          description: Paper title
        authors:
          type: array
          items:
            type: string
          description: List of author names
        venue:
          type: string
          nullable: true
          description: Publication venue
        year:
          type: integer
          nullable: true
          description: Publication year
        doi:
          type: string
          nullable: true
          description: Digital Object Identifier
        url:
          type: string
          format: uri
          nullable: true
          description: Paper URL
        abstract:
          type: string
          nullable: true
          description: Paper abstract
        citationCount:
          type: integer
          nullable: true
          description: Number of citations
        referenceCount:
          type: integer
          nullable: true
          description: Number of references
        publicationDate:
          type: string
          format: date
          nullable: true
          description: Publication date
        journal:
          type: string
          nullable: true
          description: Journal name
        volume:
          type: string
          nullable: true
          description: Journal volume
        issue:
          type: string
          nullable: true
          description: Journal issue
        pages:
          type: string
          nullable: true
          description: Page numbers
        tags:
          type: array
          items:
            type: string
          description: User-defined tags
        starred:
          type: boolean
          description: Whether paper is starred
        quickSummary:
          type: string
          nullable: true
          description: One-sentence summary
        keyIdeas:
          type: array
          items:
            type: string
          maxItems: 3
          description: Up to 3 key ideas
        userId:
          type: string
          description: Owner user ID
        createdAt:
          type: string
          format: date-time
          description: Creation timestamp
        updatedAt:
          type: string
          format: date-time
          description: Last update timestamp

    Collection:
      type: object
      required:
        - id
        - name
        - userId
        - createdAt
      properties:
        id:
          type: string
          description: Unique collection identifier
        name:
          type: string
          description: Collection name
        description:
          type: string
          nullable: true
          description: Collection description
        userId:
          type: string
          description: Owner user ID
        createdAt:
          type: string
          format: date-time
          description: Creation timestamp
        updatedAt:
          type: string
          format: date-time
          description: Last update timestamp

    Review:
      type: object
      required:
        - paperId
        - ease
        - nextDue
        - lastInterval
      properties:
        paperId:
          type: string
          description: Associated paper ID
        ease:
          type: number
          format: float
          minimum: 1.3
          maximum: 5.0
          description: Spaced repetition ease factor
        nextDue:
          type: string
          format: date-time
          description: When next review is due
        lastInterval:
          type: integer
          minimum: 1
          description: Last interval in days
        lastReviewed:
          type: string
          format: date-time
          nullable: true
          description: Last review timestamp
        createdAt:
          type: string
          format: date-time
          description: Creation timestamp
        updatedAt:
          type: string
          format: date-time
          description: Last update timestamp

    HealthStatus:
      type: object
      required:
        - status
        - timestamp
        - uptime
        - version
      properties:
        status:
          type: string
          enum: [healthy, degraded, unhealthy]
          description: Overall system status
        timestamp:
          type: string
          format: date-time
          description: Health check timestamp
        uptime:
          type: integer
          description: System uptime in seconds
        version:
          type: string
          description: Application version
        checks:
          type: object
          description: Individual health checks
          properties:
            database:
              type: object
              properties:
                status:
                  type: string
                  enum: [healthy, degraded, unhealthy]
                responseTime:
                  type: number
                  description: Database response time in ms
                error:
                  type: string
                  description: Error message if unhealthy
            memory:
              type: object
              properties:
                status:
                  type: string
                  enum: [healthy, degraded, unhealthy]
                usage:
                  type: number
                  description: Memory usage percentage
                limit:
                  type: number
                  description: Memory limit in MB

paths:
  /health:
    get:
      summary: Health check endpoint
      description: Check system health status
      tags: [System]
      security: []
      parameters:
        - name: detailed
          in: query
          description: Include detailed health checks
          required: false
          schema:
            type: boolean
            default: false
      responses:
        '200':
          description: System is healthy
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    $ref: '#/components/schemas/HealthStatus'
                  correlationId:
                    type: string
                  timestamp:
                    type: string
                    format: date-time
        '503':
          description: System is unhealthy
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /ready:
    get:
      summary: Readiness check endpoint
      description: Check if application is ready to serve traffic
      tags: [System]
      security: []
      responses:
        '200':
          description: Application is ready
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: object
                    properties:
                      status:
                        type: string
                        enum: [ready, not_ready]
                      timestamp:
                        type: string
                        format: date-time
                      checks:
                        type: array
                        items:
                          type: object
                          properties:
                            service:
                              type: string
                            status:
                              type: string
                              enum: [ready, not_ready]
                            message:
                              type: string
                            responseTime:
                              type: number
        '503':
          description: Application is not ready
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

tags:
  - name: System
    description: System health and monitoring endpoints
  - name: Authentication
    description: User authentication and authorization
  - name: Papers
    description: Research paper management
  - name: Collections
    description: Paper collection management
  - name: Reviews
    description: Spaced repetition review system
  - name: Users
    description: User management
