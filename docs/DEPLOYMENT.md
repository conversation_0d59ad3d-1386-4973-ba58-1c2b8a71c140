# PaperNugget Deployment Guide

This guide covers deploying PaperNugget to production environments.

## Prerequisites

- <PERSON>er and Docker Compose
- Domain name with DNS configured
- SSL certificate (recommended: Let's Encrypt)
- SMTP service for email delivery
- PostgreSQL database (can use Docker or external service)

## Quick Production Deployment

### 1. Server Setup

```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo usermod -aG docker $USER

# Install Docker Compose (if not included)
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# Reboot to apply group changes
sudo reboot
```

### 2. Application Deployment

```bash
# Clone repository
git clone <your-repository-url>
cd papernugget

# Create production environment file
cp .env.example .env.production

# Edit production configuration
nano .env.production
```

### 3. Production Environment Configuration

Edit `.env.production` with your production settings:

```bash
# Application
NODE_ENV=production
PORT=3000
APP_URL=https://yourdomain.com

# Database (use external PostgreSQL service recommended)
DATABASE_URL=************************************************/papernugget

# Security
JWT_SECRET=your-very-secure-jwt-secret-key-here

# Email (use production SMTP service)
EMAIL_FROM=<EMAIL>
SMTP_HOST=smtp.yourmailprovider.com
SMTP_PORT=587
SMTP_USER=your-smtp-username
SMTP_PASS=your-smtp-password
SMTP_TLS=true

# Database credentials (if using Docker PostgreSQL)
POSTGRES_DB=papernugget
POSTGRES_USER=papernugget
POSTGRES_PASSWORD=your-secure-database-password
```

### 4. Deploy Application

```bash
# Copy production environment
cp .env.production .env

# Deploy with production configuration
./bootstrap.sh

# Verify deployment
make health-detailed
```

## Production Considerations

### Security

1. **Environment Variables**:
   ```bash
   # Generate secure secrets
   openssl rand -base64 32  # For JWT_SECRET
   openssl rand -base64 32  # For database password
   
   # Set restrictive file permissions
   chmod 600 .env
   ```

2. **Database Security**:
   ```bash
   # Use external managed database service (recommended)
   # Or secure Docker PostgreSQL:
   
   # Change default passwords
   # Enable SSL connections
   # Restrict network access
   # Regular backups
   ```

3. **Network Security**:
   ```bash
   # Use reverse proxy (nginx/traefik)
   # Enable HTTPS with valid certificates
   # Configure firewall rules
   # Use private networks for internal communication
   ```

### SSL/HTTPS Setup

#### Option 1: Using Nginx Reverse Proxy

```nginx
# /etc/nginx/sites-available/papernugget
server {
    listen 80;
    server_name yourdomain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name yourdomain.com;

    ssl_certificate /path/to/your/certificate.crt;
    ssl_certificate_key /path/to/your/private.key;

    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
```

#### Option 2: Using Traefik (Docker)

```yaml
# docker-compose.prod.yml
version: '3.8'
services:
  traefik:
    image: traefik:v2.10
    command:
      - "--api.dashboard=true"
      - "--providers.docker=true"
      - "--entrypoints.web.address=:80"
      - "--entrypoints.websecure.address=:443"
      - "--certificatesresolvers.letsencrypt.acme.email=<EMAIL>"
      - "--certificatesresolvers.letsencrypt.acme.storage=/acme.json"
      - "--certificatesresolvers.letsencrypt.acme.httpchallenge.entrypoint=web"
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
      - ./acme.json:/acme.json
    labels:
      - "traefik.http.routers.api.rule=Host(`traefik.yourdomain.com`)"
      - "traefik.http.routers.api.tls.certresolver=letsencrypt"

  app:
    build: .
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.app.rule=Host(`yourdomain.com`)"
      - "traefik.http.routers.app.tls.certresolver=letsencrypt"
      - "traefik.http.services.app.loadbalancer.server.port=3000"
```

### Database Management

#### External Database (Recommended)

Use managed PostgreSQL services:
- AWS RDS
- Google Cloud SQL
- Azure Database for PostgreSQL
- DigitalOcean Managed Databases

```bash
# Example connection string
DATABASE_URL=postgresql://username:<EMAIL>:5432/papernugget
```

#### Self-Hosted Database

If using Docker PostgreSQL in production:

```yaml
# docker-compose.prod.yml
services:
  db:
    image: postgres:15
    environment:
      POSTGRES_DB: papernugget
      POSTGRES_USER: papernugget
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backups:/backups
    restart: unless-stopped
    # Don't expose port publicly
    # ports:
    #   - "5432:5432"

volumes:
  postgres_data:
    driver: local
```

### Monitoring and Logging

1. **Application Monitoring**:
   ```bash
   # Health check endpoint
   curl https://yourdomain.com/api/health
   
   # Detailed health check
   curl https://yourdomain.com/api/health?detailed=true
   ```

2. **Log Management**:
   ```bash
   # Configure log rotation
   # Use centralized logging (ELK stack, Fluentd, etc.)
   # Monitor error rates and performance metrics
   ```

3. **Uptime Monitoring**:
   ```bash
   # Use external monitoring services:
   # - UptimeRobot
   # - Pingdom
   # - StatusCake
   # - Custom monitoring scripts
   ```

### Backup Strategy

1. **Database Backups**:
   ```bash
   # Automated backup script
   #!/bin/bash
   BACKUP_DIR="/backups"
   DATE=$(date +%Y%m%d_%H%M%S)
   
   docker compose exec -T db pg_dump -U papernugget papernugget > "$BACKUP_DIR/papernugget_$DATE.sql"
   
   # Keep only last 7 days
   find $BACKUP_DIR -name "papernugget_*.sql" -mtime +7 -delete
   ```

2. **Application Backups**:
   ```bash
   # Backup configuration and uploads
   tar -czf app_backup_$(date +%Y%m%d).tar.gz .env docker-compose.yml uploads/
   ```

### Scaling Considerations

1. **Horizontal Scaling**:
   ```yaml
   # docker-compose.prod.yml
   services:
     app:
       deploy:
         replicas: 3
         update_config:
           parallelism: 1
           delay: 10s
         restart_policy:
           condition: on-failure
   ```

2. **Load Balancing**:
   ```bash
   # Use nginx, HAProxy, or cloud load balancers
   # Configure session affinity if needed
   # Health check endpoints for load balancer
   ```

3. **Database Scaling**:
   ```bash
   # Read replicas for read-heavy workloads
   # Connection pooling (PgBouncer)
   # Database sharding for very large datasets
   ```

## Deployment Automation

### CI/CD Pipeline

Use the provided GitHub Actions workflows:

1. **Continuous Integration**: `.github/workflows/ci.yml`
2. **Security Scanning**: `.github/workflows/security.yml`
3. **Manual Deployment**: `.github/workflows/manual-deploy.yml`

### Deployment Script

```bash
#!/bin/bash
# deploy.sh

set -e

echo "🚀 Deploying PaperNugget to production..."

# Pull latest changes
git pull origin main

# Backup current state
make backup

# Update application
docker compose pull
docker compose up -d --build

# Run health checks
sleep 30
make health-detailed

# Verify deployment
if curl -f https://yourdomain.com/api/health; then
    echo "✅ Deployment successful!"
else
    echo "❌ Deployment failed, rolling back..."
    # Rollback logic here
    exit 1
fi
```

## Maintenance

### Regular Tasks

1. **Weekly**:
   ```bash
   # Update dependencies
   docker compose pull
   
   # Clean up unused resources
   docker system prune
   
   # Check logs for errors
   docker compose logs --tail=100
   ```

2. **Monthly**:
   ```bash
   # Security updates
   sudo apt update && sudo apt upgrade
   
   # Certificate renewal (if using Let's Encrypt)
   certbot renew
   
   # Database maintenance
   docker compose exec db psql -U papernugget -d papernugget -c "VACUUM ANALYZE;"
   ```

### Emergency Procedures

1. **Application Down**:
   ```bash
   # Quick restart
   docker compose restart app
   
   # Full restart
   docker compose down && docker compose up -d
   
   # Check logs
   docker compose logs app
   ```

2. **Database Issues**:
   ```bash
   # Restore from backup
   docker compose exec -T db psql -U papernugget -d papernugget < backup.sql
   
   # Check database integrity
   docker compose exec db psql -U papernugget -d papernugget -c "SELECT 1;"
   ```

## Support

For production deployment support:
- Review logs: `docker compose logs`
- Check health: `make health-detailed`
- Consult troubleshooting guide: `docs/TROUBLESHOOTING.md`
- Review API documentation: `docs/API.md`
- Create GitHub issue with deployment details
