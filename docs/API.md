# PaperNugget API Documentation

## Overview

PaperNugget provides a RESTful API for managing research papers and implementing spaced repetition review systems. This documentation includes runnable curl examples for all endpoints.

## Base URL

- **Development**: `http://localhost:3000/api`
- **Production**: `https://papernugget.com/api`

## Authentication

Most endpoints require authentication via JWT token:

```bash
# Include in all authenticated requests
Authorization: Bearer <your-jwt-token>
```

## Rate Limiting

All endpoints are rate limited. Check these response headers:
- `X-RateLimit-Limit`: Maximum requests per window
- `X-RateLimit-Remaining`: Remaining requests
- `X-RateLimit-Reset`: Unix timestamp when window resets

## Error Responses

All errors follow this format:

```json
{
  "error": "Error type",
  "message": "Human readable message",
  "correlationId": "unique-request-id",
  "timestamp": "2025-08-12T12:00:00.000Z",
  "path": "/api/endpoint"
}
```

## Health Check Endpoints

### GET /api/health

Returns basic health status of the application.

**Response**:
```json
{
  "status": "healthy",
  "timestamp": "2024-01-15T10:30:00.000Z",
  "checks": [
    {
      "service": "database",
      "status": "healthy",
      "responseTime": 45
    },
    {
      "service": "smtp",
      "status": "healthy",
      "responseTime": 120
    }
  ],
  "summary": {
    "total": 4,
    "healthy": 4,
    "unhealthy": 0
  },
  "version": "1.0.0",
  "environment": "production"
}
```

### GET /api/health?detailed=true

Returns comprehensive health status with detailed validation results.

**Response**:
```json
{
  "status": "healthy",
  "timestamp": "2024-01-15T10:30:00.000Z",
  "summary": {
    "totalChecks": 15,
    "passed": 15,
    "warnings": 0,
    "errors": 0
  },
  "checks": [
    {
      "name": "Environment Variable: DATABASE_URL",
      "status": "pass",
      "message": "DATABASE_URL is configured",
      "details": "postgresql://..."
    }
  ],
  "errors": [],
  "warnings": []
}
```

## Authentication Endpoints

### POST /api/auth/register

Register a new user account.

**Request Body**:
```json
{
  "email": "<EMAIL>",
  "password": "SecurePassword123!",
  "displayName": "John Doe"
}
```

**Response** (201 Created):
```json
{
  "user": {
    "id": "uuid-here",
    "email": "<EMAIL>",
    "displayName": "John Doe",
    "emailVerified": false,
    "role": "user",
    "createdAt": "2024-01-15T10:30:00.000Z"
  },
  "verificationRequired": true,
  "message": "Registration successful. Please check your email to verify your account."
}
```

**Error Responses**:
- `400 Bad Request`: Invalid input data
- `409 Conflict`: Email already exists

### POST /api/auth/login

Authenticate user and receive JWT token.

**Request Body**:
```json
{
  "email": "<EMAIL>",
  "password": "SecurePassword123!"
}
```

**Response** (200 OK):
```json
{
  "user": {
    "id": "uuid-here",
    "email": "<EMAIL>",
    "displayName": "John Doe",
    "emailVerified": true,
    "role": "user"
  },
  "token": "jwt-token-here",
  "expiresAt": "2024-01-16T10:30:00.000Z"
}
```

**Error Responses**:
- `401 Unauthorized`: Invalid credentials
- `403 Forbidden`: Email not verified (code: `EMAIL_NOT_VERIFIED`)

### GET /api/auth/me

Get current user information (requires authentication).

**Headers**:
```
Authorization: Bearer <jwt-token>
```

**Response** (200 OK):
```json
{
  "user": {
    "id": "uuid-here",
    "email": "<EMAIL>",
    "displayName": "John Doe",
    "emailVerified": true,
    "role": "user",
    "createdAt": "2024-01-15T10:30:00.000Z",
    "lastLogin": "2024-01-15T10:30:00.000Z"
  }
}
```

### POST /api/auth/logout

Logout user and invalidate token (requires authentication).

**Headers**:
```
Authorization: Bearer <jwt-token>
```

**Response** (200 OK):
```json
{
  "message": "Logged out successfully"
}
```

### GET /api/auth/verify-email

Verify user email address with token.

**Query Parameters**:
- `token` (required): Email verification token

**Response**: Redirects to login page with status parameter

### POST /api/auth/resend-verification

Resend email verification token.

**Request Body**:
```json
{
  "email": "<EMAIL>"
}
```

**Response** (200 OK):
```json
{
  "message": "If an account with that email exists, a verification email has been sent."
}
```

### POST /api/auth/forgot-password

Initiate password reset process.

**Request Body**:
```json
{
  "email": "<EMAIL>"
}
```

**Response** (200 OK):
```json
{
  "message": "If an account with that email exists, a password reset email has been sent."
}
```

## Papers Endpoints

All paper endpoints require authentication.

### GET /api/papers

Get user's papers with optional filtering and pagination.

**Query Parameters**:
- `page` (optional): Page number (default: 1)
- `limit` (optional): Items per page (default: 20, max: 100)
- `search` (optional): Search in title, authors, venue
- `tags` (optional): Filter by tags (comma-separated)
- `starred` (optional): Filter by starred status (true/false)
- `year` (optional): Filter by publication year
- `sort` (optional): Sort field (title, year, created_at)
- `order` (optional): Sort order (asc, desc)

**Response** (200 OK):
```json
{
  "papers": [
    {
      "id": "paper-id",
      "title": "Attention Is All You Need",
      "authors": ["Ashish Vaswani", "Noam Shazeer"],
      "venue": "NeurIPS",
      "year": 2017,
      "doi": "10.48550/arXiv.1706.03762",
      "url": "https://arxiv.org/abs/1706.03762",
      "abstract": "The dominant sequence transduction models...",
      "tags": ["transformers", "attention", "nlp"],
      "starred": true,
      "userId": "user-uuid",
      "createdAt": "2024-01-15T10:30:00.000Z",
      "updatedAt": "2024-01-15T10:30:00.000Z"
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 150,
    "pages": 8
  }
}
```

### POST /api/papers

Create a new paper.

**Request Body**:
```json
{
  "title": "Paper Title",
  "authors": ["Author 1", "Author 2"],
  "venue": "Conference Name",
  "year": 2024,
  "doi": "10.1000/example",
  "url": "https://example.com/paper",
  "abstract": "Paper abstract...",
  "tags": ["tag1", "tag2"],
  "starred": false
}
```

**Response** (201 Created):
```json
{
  "id": "paper-id",
  "title": "Paper Title",
  "authors": ["Author 1", "Author 2"],
  "venue": "Conference Name",
  "year": 2024,
  "doi": "10.1000/example",
  "url": "https://example.com/paper",
  "abstract": "Paper abstract...",
  "tags": ["tag1", "tag2"],
  "starred": false,
  "userId": "user-uuid",
  "createdAt": "2024-01-15T10:30:00.000Z",
  "updatedAt": "2024-01-15T10:30:00.000Z"
}
```

### GET /api/papers/[id]

Get a specific paper by ID.

**Response** (200 OK):
```json
{
  "id": "paper-id",
  "title": "Paper Title",
  "authors": ["Author 1", "Author 2"],
  "venue": "Conference Name",
  "year": 2024,
  "doi": "10.1000/example",
  "url": "https://example.com/paper",
  "abstract": "Paper abstract...",
  "tags": ["tag1", "tag2"],
  "starred": false,
  "userId": "user-uuid",
  "createdAt": "2024-01-15T10:30:00.000Z",
  "updatedAt": "2024-01-15T10:30:00.000Z"
}
```

### PUT /api/papers/[id]

Update a paper.

**Request Body**: Same as POST /api/papers

**Response** (200 OK): Updated paper object

### DELETE /api/papers/[id]

Delete a paper.

**Response** (204 No Content)

## Collections Endpoints

All collection endpoints require authentication.

### GET /api/collections

Get user's collections.

**Response** (200 OK):
```json
{
  "collections": [
    {
      "id": "collection-id",
      "name": "Transformer Papers",
      "paperIds": ["paper-id-1", "paper-id-2"],
      "userId": "user-uuid",
      "createdAt": "2024-01-15T10:30:00.000Z",
      "updatedAt": "2024-01-15T10:30:00.000Z"
    }
  ]
}
```

### POST /api/collections

Create a new collection.

**Request Body**:
```json
{
  "name": "Collection Name",
  "paperIds": ["paper-id-1", "paper-id-2"]
}
```

**Response** (201 Created): Collection object

## Error Responses

All endpoints may return these error responses:

### 400 Bad Request
```json
{
  "error": "Invalid request data",
  "details": {
    "field": "email",
    "message": "Invalid email format"
  }
}
```

### 401 Unauthorized
```json
{
  "error": "Authentication required",
  "code": "MISSING_TOKEN"
}
```

### 403 Forbidden
```json
{
  "error": "Email not verified",
  "code": "EMAIL_NOT_VERIFIED"
}
```

### 404 Not Found
```json
{
  "error": "Resource not found"
}
```

### 429 Too Many Requests
```json
{
  "error": "Rate limit exceeded",
  "retryAfter": 60
}
```

### 500 Internal Server Error
```json
{
  "error": "Internal server error",
  "requestId": "req-uuid"
}
```

## Rate Limiting

API endpoints are rate limited to prevent abuse:
- Authentication endpoints: 5 requests per minute per IP
- General API endpoints: 100 requests per minute per user
- Health check endpoints: No rate limiting

## CORS

The API supports CORS for web applications. Allowed origins are configured via environment variables.

## Versioning

The current API version is v1. Future versions will be available at `/api/v2/`, etc.

## SDKs and Examples

### JavaScript/TypeScript Example

```typescript
const API_BASE = 'http://localhost:3000'

// Login
const loginResponse = await fetch(`${API_BASE}/api/auth/login`, {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    email: '<EMAIL>',
    password: 'password'
  })
})

const { token } = await loginResponse.json()

// Get papers
const papersResponse = await fetch(`${API_BASE}/api/papers`, {
  headers: { 'Authorization': `Bearer ${token}` }
})

const { papers } = await papersResponse.json()
```

### cURL Examples

```bash
# Login
curl -X POST http://localhost:3000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password"}'

# Get papers (replace TOKEN with actual token)
curl -X GET http://localhost:3000/api/papers \
  -H "Authorization: Bearer TOKEN"

# Health check
curl -X GET http://localhost:3000/api/health
```
