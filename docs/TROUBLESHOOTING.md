# PaperNugget Troubleshooting Guide

This guide helps you diagnose and fix common issues with PaperNugget.

## Quick Diagnostics

Before diving into specific issues, run these commands to get an overview:

```bash
# Check overall system health
make health-detailed

# Test email system
make email-test

# Check Docker status
docker compose ps
docker compose logs --tail=50
```

## Common Issues

### 1. Bootstrap Process Fails

**Symptoms**: `./bootstrap.sh` exits with errors

**Diagnosis**:
```bash
# Check Docker installation
docker --version
docker compose version

# Check if ports are available
netstat -tulpn | grep -E ':(3000|5432|8025|1025)'

# Check disk space
df -h
```

**Solutions**:

1. **Docker not installed or not running**:
   ```bash
   # Install Docker (Ubuntu/Debian)
   curl -fsSL https://get.docker.com -o get-docker.sh
   sudo sh get-docker.sh
   sudo systemctl start docker
   sudo usermod -aG docker $USER
   # Log out and back in
   ```

2. **Ports already in use**:
   ```bash
   # Find what's using the ports
   sudo lsof -i :3000
   sudo lsof -i :5432
   
   # Stop conflicting services or change ports in .env
   ```

3. **Insufficient disk space**:
   ```bash
   # Clean up Docker
   docker system prune -a
   docker volume prune
   ```

### 2. Application Won't Start

**Symptoms**: Services start but application is unreachable

**Diagnosis**:
```bash
# Check container status
docker compose ps

# Check application logs
docker compose logs app

# Check if app is responding
curl -f http://localhost:3000/api/health
```

**Solutions**:

1. **Database connection issues**:
   ```bash
   # Check database logs
   docker compose logs db
   
   # Verify database is ready
   docker compose exec db pg_isready -U papernugget
   
   # Reset database
   make clean
   make bootstrap
   ```

2. **Environment configuration issues**:
   ```bash
   # Check .env file exists and has correct values
   cat .env
   
   # Compare with example
   diff .env .env.example
   
   # Recreate from template
   cp .env.example .env
   ```

3. **Build issues**:
   ```bash
   # Rebuild containers
   docker compose build --no-cache
   docker compose up -d
   ```

### 3. Database Issues

**Symptoms**: Database connection errors, migration failures

**Diagnosis**:
```bash
# Check database container
docker compose logs db

# Test database connection
docker compose exec db psql -U papernugget -d papernugget -c "SELECT 1;"

# Check database schema
docker compose exec db psql -U papernugget -d papernugget -c "\dt"
```

**Solutions**:

1. **Database won't start**:
   ```bash
   # Check database logs for errors
   docker compose logs db
   
   # Remove corrupted volume
   docker compose down -v
   docker volume rm papernugget_postgres_data
   make bootstrap
   ```

2. **Migration failures**:
   ```bash
   # Check migration logs
   docker compose logs app | grep -i migration
   
   # Reset and re-run migrations
   make clean
   make bootstrap
   ```

3. **Schema inconsistencies**:
   ```bash
   # Run schema validation
   make health-detailed
   
   # Reset to clean state
   make reset
   ```

### 4. Email System Issues

**Symptoms**: Verification emails not sent, SMTP errors

**Diagnosis**:
```bash
# Test email system
make email-test

# Check email configuration
docker compose exec app npm run email:test

# Check Mailpit (development)
curl http://localhost:8025/api/v1/info
```

**Solutions**:

1. **SMTP connection failures**:
   ```bash
   # Check SMTP settings in .env
   grep SMTP .env
   
   # Test with different SMTP server
   # Update .env with working SMTP credentials
   ```

2. **Mailpit not accessible (development)**:
   ```bash
   # Check Mailpit container
   docker compose logs mailpit
   
   # Restart Mailpit
   docker compose restart mailpit
   ```

3. **Email verification not working**:
   ```bash
   # Send test email
   make email-test-send
   
   # Check Mailpit UI
   open http://localhost:8025
   
   # Check application logs
   docker compose logs app | grep -i email
   ```

### 5. Test Failures

**Symptoms**: Tests fail during development or CI

**Diagnosis**:
```bash
# Run tests with verbose output
npm run test:unit
npm run test:integration

# Check test environment
NODE_ENV=test make health-detailed
```

**Solutions**:

1. **Unit test failures**:
   ```bash
   # Run specific test file
   node --test tests/unit/specific-test.test.ts
   
   # Check for missing dependencies
   npm ci
   ```

2. **Integration test failures**:
   ```bash
   # Ensure database is ready
   make health-detailed
   
   # Reset test environment
   make clean
   make bootstrap
   npm run test:integration
   ```

3. **Database-related test failures**:
   ```bash
   # Check test database connection
   DATABASE_URL=postgresql://papernugget:password@localhost:5432/papernugget npm run migrate
   
   # Reset test data
   make seed
   ```

### 6. Performance Issues

**Symptoms**: Slow response times, high resource usage

**Diagnosis**:
```bash
# Check container resource usage
docker stats

# Check application logs for slow queries
docker compose logs app | grep -i slow

# Check database performance
docker compose exec db psql -U papernugget -d papernugget -c "SELECT * FROM pg_stat_activity;"
```

**Solutions**:

1. **High memory usage**:
   ```bash
   # Restart containers
   docker compose restart
   
   # Check for memory leaks in logs
   docker compose logs app | grep -i memory
   ```

2. **Slow database queries**:
   ```bash
   # Check database indexes
   make health-detailed
   
   # Analyze query performance
   docker compose exec db psql -U papernugget -d papernugget -c "EXPLAIN ANALYZE SELECT * FROM papers LIMIT 10;"
   ```

## Environment-Specific Issues

### Development Environment

1. **Hot reload not working**:
   ```bash
   # Check if volumes are mounted correctly
   docker compose config
   
   # Restart development server
   docker compose restart app
   ```

2. **Port conflicts**:
   ```bash
   # Change ports in .env
   PORT=3001
   
   # Update docker-compose.yml if needed
   ```

### Production Environment

1. **SSL/TLS issues**:
   ```bash
   # Check certificate validity
   openssl s_client -connect yourdomain.com:443
   
   # Update APP_URL to use https
   APP_URL=https://yourdomain.com
   ```

2. **Email delivery issues**:
   ```bash
   # Use production SMTP service
   SMTP_HOST=smtp.yourmailprovider.com
   SMTP_PORT=587
   SMTP_TLS=true
   ```

## Getting Help

If you're still experiencing issues:

1. **Check the logs**:
   ```bash
   # Application logs
   docker compose logs app --tail=100
   
   # Database logs
   docker compose logs db --tail=100
   
   # All services
   docker compose logs --tail=100
   ```

2. **Gather system information**:
   ```bash
   # System info
   uname -a
   docker --version
   docker compose version
   
   # Container status
   docker compose ps
   
   # Health check
   make health-detailed
   ```

3. **Create a minimal reproduction**:
   ```bash
   # Start fresh
   make clean
   ./bootstrap.sh
   
   # Document exact steps that cause the issue
   ```

4. **Check for known issues**:
   - Review GitHub issues
   - Check recent commits for related changes
   - Search documentation for similar problems

## Preventive Measures

1. **Regular maintenance**:
   ```bash
   # Weekly cleanup
   docker system prune
   
   # Update dependencies
   npm audit
   npm update
   ```

2. **Backup important data**:
   ```bash
   # Backup database
   make backup
   
   # Export environment configuration
   cp .env .env.backup
   ```

3. **Monitor system health**:
   ```bash
   # Regular health checks
   make health-detailed
   
   # Monitor logs for errors
   docker compose logs --follow
   ```
