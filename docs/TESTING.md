# PaperNugget Testing Guide

This guide covers testing strategies, running tests, and writing new tests for PaperNugget.

## Testing Overview

PaperNugget uses a comprehensive testing strategy with multiple layers:

- **Unit Tests**: Test individual functions and components
- **Integration Tests**: Test API endpoints and database operations
- **End-to-End Tests**: Test complete user workflows
- **Health Checks**: Validate system configuration and connectivity

## Running Tests

### Quick Test Commands

```bash
# Run all tests
make test

# Run specific test categories
npm run test:unit           # Fast unit tests
npm run test:integration    # Database and API tests
npm run test:ci            # All tests for CI environment

# Health and system tests
make health                # Basic health check
make health-detailed       # Comprehensive health check
make email-test           # Email system tests
```

### Test Environment Setup

Tests require a running database and email service:

```bash
# Start test environment
./bootstrap.sh

# Or manually start services
docker compose up -d

# Verify test environment
make health-detailed
```

### Running Individual Tests

```bash
# Run specific test file
node --test tests/unit/database-schema.test.ts

# Run with verbose output
node --test --reporter=spec tests/unit/database-schema.test.ts

# Run integration tests (requires running services)
node --test tests/integration/auth-flow.test.ts
```

## Test Categories

### Unit Tests (`tests/unit/`)

Test individual functions and modules in isolation.

**Examples**:
- `database-schema.test.ts`: Database schema validation
- `health-checks.test.ts`: Health check system
- `token-generation.test.ts`: Token generation utilities

**Characteristics**:
- Fast execution (< 1 second each)
- No external dependencies
- Mock external services
- Test pure functions and logic

### Integration Tests (`tests/integration/`)

Test API endpoints and database operations with real services.

**Examples**:
- `auth-flow.test.ts`: Complete authentication workflows
- `api-endpoints.test.ts`: API endpoint functionality
- `email-verification-e2e.test.ts`: Email verification system

**Characteristics**:
- Require running database and services
- Test real API endpoints
- Validate database operations
- Test service integrations

### Health Checks

Validate system configuration and connectivity.

```bash
# Basic health check
curl http://localhost:3000/api/health

# Detailed health check
curl http://localhost:3000/api/health?detailed=true

# Email system test
npm run email:test
```

## Writing Tests

### Unit Test Example

```typescript
// tests/unit/example.test.ts
import { test, describe } from 'node:test'
import assert from 'node:assert'
import { functionToTest } from '../../lib/example'

describe('Example Function Tests', () => {
  test('should return expected result', () => {
    const result = functionToTest('input')
    assert.strictEqual(result, 'expected-output')
  })

  test('should handle edge cases', () => {
    assert.throws(() => functionToTest(null), /Invalid input/)
  })
})
```

### Integration Test Example

```typescript
// tests/integration/example.test.ts
import { test, describe, before, after } from 'node:test'
import assert from 'node:assert'

const BASE_URL = 'http://localhost:3000'

describe('API Integration Tests', () => {
  before(async () => {
    // Setup test data
  })

  after(async () => {
    // Cleanup test data
  })

  test('should handle API request', async () => {
    const response = await fetch(`${BASE_URL}/api/endpoint`)
    assert.strictEqual(response.status, 200)
    
    const data = await response.json()
    assert.ok(data.result)
  })
})
```

### Database Test Example

```typescript
// tests/unit/database-example.test.ts
import { test, describe, before } from 'node:test'
import assert from 'node:assert'
import { query } from '../../lib/db'

describe('Database Tests', () => {
  before(async () => {
    // Ensure database connection
    await query('SELECT 1')
  })

  test('should validate table structure', async () => {
    const result = await query(`
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'users'
    `)
    
    const columns = result.rows.map(row => row.column_name)
    assert.ok(columns.includes('email'))
    assert.ok(columns.includes('password_hash'))
  })
})
```

## Test Data Management

### Test Users

The system includes test user creation utilities:

```bash
# Create test users
npm run seed:test-users

# Or manually in tests
const testUser = await users.create({
  email: '<EMAIL>',
  passwordHash: await hashPassword('password'),
  displayName: 'Test User',
  emailVerified: true
})
```

### Database Cleanup

Always clean up test data to avoid interference:

```typescript
describe('Test Suite', () => {
  let testUserId: string

  after(async () => {
    // Clean up test data
    if (testUserId) {
      await users.delete(testUserId)
    }
  })
})
```

### Email Testing

Use Mailpit for email testing in development:

```typescript
// Check if email was sent
const mailpitResponse = await fetch('http://localhost:8025/api/v1/messages')
const emails = await mailpitResponse.json()

const verificationEmail = emails.messages?.find(email => 
  email.To?.some(to => to.Address === testEmail)
)
```

## Continuous Integration

### GitHub Actions

Tests run automatically on:
- Push to main/develop branches
- Pull requests
- Manual workflow dispatch

**Workflows**:
- `.github/workflows/ci.yml`: Main CI pipeline
- `.github/workflows/bootstrap-test.yml`: Bootstrap process testing
- `.github/workflows/security.yml`: Security scanning

### CI Test Environment

CI uses Docker services for testing:

```yaml
services:
  postgres:
    image: postgres:15
    env:
      POSTGRES_DB: papernugget_test
      POSTGRES_USER: papernugget
      POSTGRES_PASSWORD: test_password

  mailpit:
    image: axllent/mailpit:latest
    ports:
      - 1025:1025
      - 8025:8025
```

## Test Coverage

### Measuring Coverage

```bash
# Run tests with coverage (if configured)
npm run test:coverage

# Check coverage report
open coverage/index.html
```

### Coverage Goals

- **Unit Tests**: > 80% line coverage
- **Integration Tests**: Cover all API endpoints
- **Critical Paths**: 100% coverage for auth and security

## Performance Testing

### Load Testing

```bash
# Install artillery for load testing
npm install -g artillery

# Run load test
artillery run tests/load/api-load-test.yml
```

### Database Performance

```bash
# Test database performance
docker compose exec db psql -U papernugget -d papernugget -c "
  EXPLAIN ANALYZE SELECT * FROM papers 
  WHERE user_id = 'test-user-id' 
  ORDER BY created_at DESC 
  LIMIT 20;
"
```

## Debugging Tests

### Common Issues

1. **Database Connection Failures**:
   ```bash
   # Check database is running
   docker compose ps
   
   # Check database logs
   docker compose logs db
   
   # Test connection
   docker compose exec db pg_isready -U papernugget
   ```

2. **Port Conflicts**:
   ```bash
   # Check what's using ports
   netstat -tulpn | grep -E ':(3000|5432|8025)'
   
   # Stop conflicting services
   docker compose down
   ```

3. **Test Data Conflicts**:
   ```bash
   # Reset test database
   make clean
   make bootstrap
   ```

### Debug Mode

```bash
# Run tests with debug output
DEBUG=* npm test

# Run specific test with debugging
node --inspect --test tests/unit/example.test.ts
```

## Best Practices

### Test Organization

1. **File Naming**: Use `.test.ts` suffix
2. **Directory Structure**: Mirror source code structure
3. **Test Grouping**: Use `describe` blocks for logical grouping

### Test Writing

1. **Descriptive Names**: Test names should describe expected behavior
2. **Single Responsibility**: One assertion per test when possible
3. **Setup/Teardown**: Use `before`/`after` hooks for setup
4. **Isolation**: Tests should not depend on each other

### Data Management

1. **Clean State**: Always start with clean test data
2. **Cleanup**: Remove test data after tests complete
3. **Realistic Data**: Use realistic test data that matches production

### Performance

1. **Fast Unit Tests**: Keep unit tests under 1 second
2. **Parallel Execution**: Design tests to run in parallel
3. **Resource Cleanup**: Clean up resources to prevent memory leaks

## Troubleshooting

### Test Failures

1. **Check System Health**:
   ```bash
   make health-detailed
   ```

2. **Verify Test Environment**:
   ```bash
   docker compose ps
   docker compose logs
   ```

3. **Reset Environment**:
   ```bash
   make clean
   make bootstrap
   ```

### Common Error Messages

- **"Database connection failed"**: Check database is running
- **"Port already in use"**: Stop conflicting services
- **"Email test failed"**: Check Mailpit is running
- **"Token validation failed"**: Check JWT secret configuration

For more troubleshooting help, see `docs/TROUBLESHOOTING.md`.
