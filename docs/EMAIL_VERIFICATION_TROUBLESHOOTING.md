# Email Verification Troubleshooting Guide

This guide helps diagnose and resolve common issues with the email verification system in PaperNugget.

## Quick Diagnostics

### 1. Health Check
First, check the system health:
```bash
curl http://localhost:3000/api/health
```

Expected response:
```json
{
  "status": "healthy",
  "timestamp": "2024-01-01T00:00:00.000Z",
  "checks": [
    {"service": "database", "status": "healthy", "responseTime": 5},
    {"service": "smtp", "status": "healthy", "responseTime": 100},
    {"service": "application", "status": "healthy"}
  ]
}
```

### 2. Environment Variables
Verify all required environment variables are set:
```bash
echo "APP_URL: $APP_URL"
echo "EMAIL_FROM: $EMAIL_FROM"
echo "SMTP_HOST: $SMTP_HOST"
echo "SMTP_PORT: $SMTP_PORT"
```

## Common Issues and Solutions

### Issue 1: SMTP Connection Failed

**Symptoms:**
- Health check shows SMTP as unhealthy
- Registration succeeds but no email is sent
- Console errors about SMTP connection

**Diagnosis:**
```bash
# Check if SMTP server is reachable
telnet $SMTP_HOST $SMTP_PORT
```

**Solutions:**

1. **For Local Development (Mailpit):**
   ```bash
   # Start Mailpit
   npm run dev:mail
   
   # Verify Mailpit is running
   curl http://localhost:8025
   ```

2. **For Production SMTP:**
   ```bash
   # Test SMTP credentials
   openssl s_client -connect smtp.gmail.com:587 -starttls smtp
   ```

3. **Firewall Issues:**
   - Check if port 587/465/25 is blocked
   - Verify network security groups allow SMTP traffic
   - Some ISPs block SMTP ports

4. **Authentication Issues:**
   - For Gmail: Use App Passwords, not account password
   - For Office 365: Enable SMTP AUTH
   - Verify username/password are correct

### Issue 2: Emails Not Being Delivered

**Symptoms:**
- SMTP connection is healthy
- No errors in logs
- Users don't receive emails

**Diagnosis:**
1. Check spam/junk folders
2. Verify EMAIL_FROM domain reputation
3. Check email server logs

**Solutions:**

1. **SPF/DKIM/DMARC Setup:**
   ```dns
   # Add to DNS records
   TXT "v=spf1 include:_spf.google.com ~all"
   TXT "v=DMARC1; p=quarantine; rua=mailto:<EMAIL>"
   ```

2. **Use Transactional Email Service:**
   - SendGrid, Mailgun, AWS SES
   - Better deliverability than generic SMTP

3. **Email Content Issues:**
   - Avoid spam trigger words
   - Include unsubscribe link
   - Use proper HTML structure

### Issue 3: Verification Links Not Working

**Symptoms:**
- Users receive emails
- Clicking link shows "invalid" or "expired"
- Links redirect to error page

**Diagnosis:**
```bash
# Check token in database
psql $DATABASE_URL -c "SELECT token, expires_at, used_at FROM email_verification_tokens ORDER BY created_at DESC LIMIT 5;"

# Check APP_URL configuration
echo "APP_URL: $APP_URL"
```

**Solutions:**

1. **APP_URL Mismatch:**
   ```bash
   # Ensure APP_URL matches your domain
   APP_URL=https://yourdomain.com  # Production
   APP_URL=http://localhost:3000   # Development
   ```

2. **Token Expiry:**
   - Tokens expire after 24 hours
   - User needs to request new verification email
   - Check system clock synchronization

3. **Database Issues:**
   ```sql
   -- Check for orphaned tokens
   SELECT COUNT(*) FROM email_verification_tokens WHERE used_at IS NULL AND expires_at < NOW();
   
   -- Clean up expired tokens
   DELETE FROM email_verification_tokens WHERE expires_at < NOW();
   ```

### Issue 4: Rate Limiting Problems

**Symptoms:**
- Users get "Too many requests" errors
- Legitimate users can't resend verification
- Rate limits too restrictive

**Diagnosis:**
```bash
# Check rate limit logs
docker logs papernugget-app | grep "rate_limit"
```

**Solutions:**

1. **Adjust Rate Limits:**
   ```typescript
   // In lib/rate-limiter.ts
   export const verificationResendLimiter = new RateLimiter({
     maxRequests: 3,  // Increase from 1
     windowMs: 60 * 1000, // 1 minute
   })
   ```

2. **IP Address Issues:**
   - Behind proxy: Check X-Forwarded-For headers
   - Shared IP: Consider user-based rate limiting
   - VPN users: May share IP addresses

3. **Clear Rate Limits:**
   ```typescript
   // Reset rate limits for specific IP
   rateLimitStore.delete('resend:***********:<EMAIL>')
   ```

### Issue 5: Database Connection Problems

**Symptoms:**
- Health check shows database as unhealthy
- Registration fails completely
- Connection timeout errors

**Diagnosis:**
```bash
# Test database connection
psql $DATABASE_URL -c "SELECT 1;"

# Check database logs
docker logs papernugget-db
```

**Solutions:**

1. **Connection String Issues:**
   ```bash
   # Verify DATABASE_URL format
   DATABASE_URL=postgresql://user:password@host:port/database
   ```

2. **Database Not Ready:**
   ```bash
   # Wait for database to be ready
   docker-compose up db
   # Wait for "database system is ready to accept connections"
   ```

3. **Migration Issues:**
   ```bash
   # Check if email_verification_tokens table exists
   psql $DATABASE_URL -c "\dt email_verification_tokens"
   
   # Run migrations manually if needed
   npm run migrate
   ```

## Performance Optimization

### Email Queue (Production)

For high-volume applications, implement email queuing:

```typescript
// lib/email-queue.ts
import Queue from 'bull'

const emailQueue = new Queue('email processing', process.env.REDIS_URL)

emailQueue.process(async (job) => {
  const { to, token } = job.data
  await sendVerificationEmail(to, token)
})

// Queue email instead of sending immediately
export function queueVerificationEmail(to: string, token: string) {
  return emailQueue.add('verification', { to, token })
}
```

### Database Optimization

```sql
-- Add indexes for better performance
CREATE INDEX CONCURRENTLY idx_email_verification_tokens_expires_at 
ON email_verification_tokens(expires_at) 
WHERE used_at IS NULL;

-- Clean up old tokens regularly
DELETE FROM email_verification_tokens 
WHERE expires_at < NOW() - INTERVAL '7 days';
```

## Monitoring and Alerting

### Key Metrics to Monitor

1. **Email Delivery Rate**
   ```sql
   SELECT 
     DATE(created_at) as date,
     COUNT(*) as total_sent,
     COUNT(CASE WHEN used_at IS NOT NULL THEN 1 END) as verified
   FROM email_verification_tokens 
   WHERE created_at > NOW() - INTERVAL '7 days'
   GROUP BY DATE(created_at);
   ```

2. **SMTP Health**
   ```bash
   # Monitor SMTP response times
   curl -w "%{time_total}" http://localhost:3000/api/health
   ```

3. **Rate Limit Hits**
   ```bash
   # Count rate limit violations
   grep "rate_limit_exceeded" /var/log/app.log | wc -l
   ```

### Alerting Setup

```yaml
# Example Prometheus alert
- alert: EmailVerificationDown
  expr: up{job="papernugget-health"} == 0
  for: 5m
  annotations:
    summary: "Email verification system is down"
    
- alert: HighEmailFailureRate
  expr: rate(email_send_failures_total[5m]) > 0.1
  for: 2m
  annotations:
    summary: "High email failure rate detected"
```

## Testing Email Flows

### Manual Testing

1. **Registration Flow:**
   ```bash
   curl -X POST http://localhost:3000/api/auth/register \
     -H "Content-Type: application/json" \
     -d '{"email":"<EMAIL>","password":"TestPass123!"}'
   ```

2. **Resend Verification:**
   ```bash
   curl -X POST http://localhost:3000/api/auth/resend-verification \
     -H "Content-Type: application/json" \
     -d '{"email":"<EMAIL>"}'
   ```

3. **Verify Email:**
   ```bash
   # Get token from Mailpit UI or database
   curl "http://localhost:3000/api/auth/verify-email?token=YOUR_TOKEN"
   ```

### Automated Testing

```bash
# Run all email verification tests
npm test

# Run specific test suites
npm run test:unit
npm run test:integration
```

## Getting Help

If you're still experiencing issues:

1. **Check Logs:**
   ```bash
   docker logs papernugget-app --tail 100
   ```

2. **Enable Debug Mode:**
   ```bash
   NODE_ENV=development npm start
   ```

3. **Community Support:**
   - Create an issue on GitHub
   - Include health check output
   - Provide relevant log excerpts
   - Describe steps to reproduce
