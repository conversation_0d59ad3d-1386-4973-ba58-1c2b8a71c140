# User Management System Architecture

## Overview
This document outlines the comprehensive User Management system for PaperNugget, including authentication, authorization, security, and privacy features.

## Core Components

### 1. Database Schema

#### Users Table
```sql
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    display_name VA<PERSON>HA<PERSON>(100),
    role VARCHAR(50) DEFAULT 'user',
    email_verified BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_login TIMESTAMP WITH TIME ZONE,
    is_active BOOLEAN DEFAULT TRUE,
    privacy_settings JSONB DEFAULT '{}',
    preferences JSONB DEFAULT '{}'
);
```

#### Sessions Table
```sql
CREATE TABLE user_sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    token_hash VARCHAR(255) NOT NULL,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    ip_address INET,
    user_agent TEXT
);
```

#### Password Reset Tokens Table
```sql
CREATE TABLE password_reset_tokens (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    token_hash VARCHAR(255) NOT NULL,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    used BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

#### Email Verification Tokens Table
```sql
CREATE TABLE email_verification_tokens (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    token_hash VARCHAR(255) NOT NULL,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    used BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### 2. Data Model Updates

All existing tables need user_id foreign keys:
- papers.user_id
- collections.user_id
- notes.user_id (inherited from paper)
- reviews.user_id (inherited from paper)

### 3. Authentication Flow

#### Registration
1. User submits email/password
2. Validate email format and password strength
3. Hash password with bcrypt (12 rounds)
4. Create user record
5. Generate email verification token
6. Send verification email
7. Return success (no sensitive data)

#### Login
1. User submits email/password
2. Find user by email
3. Verify password hash
4. Check if account is active and verified
5. Generate JWT session token
6. Store session in database
7. Return JWT token and user info

#### Session Management
- JWT tokens with 24-hour expiry
- Refresh token mechanism
- Session storage in database for revocation
- Automatic cleanup of expired sessions

### 4. Role-Based Access Control (RBAC)

#### Roles
- **admin**: Full system access, user management
- **user**: Standard user access to own data
- **readonly**: Read-only access (future use)

#### Permissions
- **papers**: create, read, update, delete (own)
- **collections**: create, read, update, delete (own)
- **notes**: create, read, update, delete (own)
- **admin**: user management, system settings

### 5. Security Features

#### Password Security
- Minimum 8 characters
- bcrypt hashing with salt rounds: 12
- Password strength validation
- Password history (prevent reuse of last 5)

#### Session Security
- Secure HTTP-only cookies
- CSRF protection
- Rate limiting on auth endpoints
- Session timeout and cleanup

#### Data Encryption
- Passwords: bcrypt hashing
- Tokens: SHA-256 hashing
- Sensitive data: AES-256 encryption for PII

### 6. Privacy Controls

#### User Data Management
- Data export (JSON format)
- Account deletion (hard delete with confirmation)
- Privacy settings (profile visibility, data sharing)
- Consent management for data processing

#### Compliance Features
- GDPR compliance ready
- Data retention policies
- Audit logging for sensitive operations
- User consent tracking

### 7. API Endpoints

#### Authentication
- POST /api/auth/register
- POST /api/auth/login
- POST /api/auth/logout
- POST /api/auth/refresh
- GET /api/auth/me

#### Password Management
- POST /api/auth/forgot-password
- POST /api/auth/reset-password
- POST /api/auth/change-password

#### Email Verification
- POST /api/auth/verify-email
- POST /api/auth/resend-verification

#### User Management
- GET /api/users/profile
- PUT /api/users/profile
- DELETE /api/users/account
- GET /api/users/export-data

#### Admin Endpoints
- GET /api/admin/users
- PUT /api/admin/users/:id
- DELETE /api/admin/users/:id

### 8. Frontend Components

#### Authentication Components
- LoginForm
- RegisterForm
- ForgotPasswordForm
- ResetPasswordForm
- EmailVerificationForm

#### User Management Components
- UserProfile
- UserSettings
- PrivacySettings
- AccountDeletion
- DataExport

#### Navigation Updates
- User menu in sidebar
- Authentication guards
- Role-based component rendering

### 9. Middleware and Guards

#### Authentication Middleware
- JWT token validation
- Session verification
- User context injection

#### Authorization Guards
- Role-based route protection
- Resource ownership validation
- Permission checking

### 10. Environment Variables

```env
# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key
JWT_EXPIRES_IN=24h
REFRESH_TOKEN_EXPIRES_IN=7d

# Email Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
FROM_EMAIL=<EMAIL>

# Security
BCRYPT_ROUNDS=12
SESSION_SECRET=your-session-secret
CSRF_SECRET=your-csrf-secret

# Rate Limiting
RATE_LIMIT_WINDOW=15
RATE_LIMIT_MAX_REQUESTS=100
```

### 11. Migration Strategy

#### Phase 1: Core Authentication
1. Create user tables
2. Implement basic auth endpoints
3. Add authentication middleware

#### Phase 2: Data Association
1. Add user_id to existing tables
2. Migrate existing data to default user
3. Update all CRUD operations

#### Phase 3: Advanced Features
1. Implement RBAC
2. Add password reset
3. Email verification

#### Phase 4: Privacy & Security
1. Privacy controls
2. Data export/deletion
3. Security enhancements

## Implementation Notes

### Backward Compatibility
- Existing data will be associated with a default "system" user
- Gradual migration approach to avoid breaking changes
- Feature flags for enabling/disabling auth features

### Performance Considerations
- Database indexes on user_id columns
- Session cleanup background job
- Efficient permission checking

### Security Best Practices
- Input validation and sanitization
- SQL injection prevention
- XSS protection
- Rate limiting
- Secure headers
- HTTPS enforcement