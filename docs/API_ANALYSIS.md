# API Analysis and Hardening Plan

## Current API Structure

### Route Inventory (32 routes total)

#### Authentication Routes (11)
- `POST /api/auth/register` - User registration with rate limiting ✅
- `POST /api/auth/login` - User login with audit logging ✅
- `POST /api/auth/logout` - User logout
- `GET /api/auth/me` - Get current user info
- `POST /api/auth/change-password` - Change password with validation ✅
- `POST /api/auth/forgot-password` - Password reset request ✅
- `POST /api/auth/reset-password` - Password reset confirmation
- `POST /api/auth/resend-verification` - Resend email verification ✅
- `POST /api/auth/verify-email` - Email verification

#### User Management Routes (3)
- `GET /api/user/profile` - Get user profile (protected) ✅
- `PUT /api/user/profile` - Update user profile (protected)
- `GET /api/admin/users` - Admin: List users (protected)
- `GET /api/admin/users/[id]` - Admin: Get user details (protected)

#### Paper Management Routes (8)
- `GET /api/papers` - List user papers (protected) ✅
- `POST /api/papers` - Create paper (protected) ✅
- `GET /api/papers/[id]` - Get paper details (protected) ✅
- `PUT /api/papers/[id]` - Update paper (protected) ✅
- `DELETE /api/papers/[id]` - Delete paper (protected)
- `POST /api/papers/[id]/star` - Star/unstar paper (protected)
- `GET /api/papers/doi/[doi]` - Get paper by DOI
- `POST /api/papers/enrich` - Enrich paper metadata

#### Collection Management Routes (6)
- `GET /api/collections` - List user collections (protected) ✅
- `POST /api/collections` - Create collection (protected)
- `GET /api/collections/[id]` - Get collection details (protected)
- `PUT /api/collections/[id]` - Update collection (protected)
- `DELETE /api/collections/[id]` - Delete collection (protected)
- `GET /api/collections/[id]/papers` - Get collection papers (protected) ✅
- `POST /api/collections/[id]/papers` - Add papers to collection (protected)
- `DELETE /api/collections/[id]/papers/[paperId]` - Remove paper from collection (protected)

#### Review System Routes (5)
- `GET /api/review/stats` - Get review statistics (protected)
- `GET /api/review/due` - Get papers due for review (protected)
- `POST /api/review/session` - Start review session (protected)
- `POST /api/papers/[id]/review` - Add paper to review (protected) ✅
- `POST /api/review/[paperId]` - Submit review (protected)

#### Notes Routes (1)
- `GET /api/notes/[paperId]` - Get paper notes (protected)
- `POST /api/notes/[paperId]` - Create/update paper notes (protected)

#### System Routes (2)
- `GET /api/health` - Health check ✅
- `GET /api/debug/reviews` - Debug reviews (should be admin-only)

## Current State Analysis

### ✅ Strengths
1. **Authentication**: Robust JWT-based auth with session management
2. **Authorization**: Resource-level access control with `canAccessResource`
3. **Rate Limiting**: Implemented for registration and verification endpoints
4. **Validation**: Email/password validation in auth endpoints
5. **Audit Logging**: Security events tracked
6. **Error Handling**: Consistent error responses in most routes

### ❌ Issues Identified

#### Validation Issues
- **Inconsistent validation**: Some routes lack input validation
- **No request size limits**: Missing body size validation
- **No correlation IDs**: Error tracking difficult
- **Inconsistent error shapes**: Different error response formats

#### Security Issues
- **Missing rate limiting**: Most routes lack rate limiting
- **No security headers**: Missing CORS, CSP, etc.
- **Sensitive data logging**: Potential PII in logs
- **No idempotency**: Duplicate requests possible

#### Data Issues
- **No pagination**: List endpoints return all data
- **No sorting**: No standardized sorting options
- **No transaction safety**: Multi-step operations not atomic
- **Missing constraints**: Database lacks proper indexes/constraints

#### Observability Issues
- **Basic logging**: No structured logging with request IDs
- **No metrics**: Missing performance/usage metrics
- **Limited health checks**: Basic health endpoint only

## Implementation Priority

### Phase 1: Foundation (Critical)
1. Centralized validation system with correlation IDs
2. Unified error response format
3. Request logging middleware with request IDs
4. Basic security headers

### Phase 2: Security (High)
1. Rate limiting for all endpoints
2. Request size limits
3. Sensitive data redaction
4. Enhanced authentication checks

### Phase 3: Data Integrity (High)
1. Database transactions for mutations
2. Pagination and sorting standardization
3. Input sanitization and SQL injection prevention
4. Database constraints and indexes

### Phase 4: Observability (Medium)
1. Structured logging
2. Health and readiness endpoints
3. Basic metrics collection
4. Performance monitoring

### Phase 5: Documentation (Medium)
1. OpenAPI specification
2. API documentation updates
3. Example requests/responses
4. Testing documentation

## Breaking Change Risk Assessment

### Low Risk Changes
- Adding validation (with backward-compatible error messages)
- Adding optional query parameters (pagination, sorting)
- Adding response headers
- Adding logging and metrics

### Medium Risk Changes
- Changing error response format (need careful migration)
- Adding required authentication to currently open endpoints
- Adding rate limiting (may affect high-volume clients)

### High Risk Changes
- Changing request/response schemas
- Removing endpoints
- Changing authentication mechanisms
- Modifying database constraints that could fail existing data

## Success Metrics

1. **Security**: Zero critical security vulnerabilities
2. **Reliability**: 99.9% uptime with proper error handling
3. **Performance**: <200ms average response time
4. **Observability**: Full request tracing and error correlation
5. **Documentation**: 100% API coverage in OpenAPI spec
6. **Testing**: 90%+ code coverage with integration tests
