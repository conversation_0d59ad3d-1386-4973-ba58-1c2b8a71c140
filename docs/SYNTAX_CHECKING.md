# Syntax Checking System

This document describes the comprehensive syntax checking system implemented to prevent build errors in PaperNugget.

## Overview

The syntax checking system helps catch common syntax errors in route files before they cause build failures. It performs both basic syntax validation and pattern-based checks to ensure code quality.

## Quick Start

### Run Syntax Check (Development)
```bash
# Using npm script (local development)
npm run check-syntax

# Or directly (local development)
./scripts/check-syntax.sh
```

### Automatic Pre-build Check
The syntax checker runs automatically before every build:
```bash
npm run build  # Runs check-syntax first during build phase
docker compose up -d --build  # Syntax check runs during Docker build
```

**Note**: The syntax checker only runs during the build phase. It's not available in the production container since only the compiled application is deployed.

## What It Checks

### 1. **Brace Matching**
- Ensures all `{` have matching `}`
- Detects unbalanced braces that cause syntax errors

### 2. **Parentheses Matching**
- Ensures all `(` have matching `)`
- Catches missing parentheses in function calls

### 3. **Try-Catch Blocks**
- Verifies `catch` blocks have corresponding `try` blocks
- Prevents orphaned catch statements

### 4. **Export Statements**
- Ensures route files have proper export statements
- Route files must export HTTP handlers (GET, POST, etc.)

### 5. **withAuth Usage**
- Validates proper closing of `withAuth` wrapper functions
- Checks for correct parameter structure

### 6. **Async/Await Patterns**
- Warns about async functions without await statements
- Helps identify unnecessary async declarations

## Files Checked

The system automatically finds and checks all route files:
- `app/api/**/route.ts`
- `app/api/**/route.js`

Currently checking **32 route files** across the application.

## Output Format

### Success Output
```
🎉 All route files passed basic syntax checks!

💡 Next steps:
  1. Run 'docker compose build app' to test full compilation
  2. Run 'docker compose up -d' to start the application
  3. Test the application functionality
```

### Error Output
```
❌ Failed: 2
📁 Total:  32

🚨 Some files failed syntax checks!

💡 Fix these issues before building:
  1. Review the errors above
  2. Fix syntax issues in the failing files
  3. Run this script again to verify fixes
  4. Then run 'docker compose build app'
```

## Common Issues and Fixes

### 1. **Unmatched Braces**
```typescript
// ❌ Wrong - missing closing brace
export const GET = withAuth(async (request, { user, userId }) => {
  try {
    return NextResponse.json({ data: "test" })
  } catch (error) {
    return NextResponse.json({ error: "Failed" }, { status: 500 })
  }
// Missing: }, { allowUnverified: true })

// ✅ Correct
export const GET = withAuth(async (request, { user, userId }) => {
  try {
    return NextResponse.json({ data: "test" })
  } catch (error) {
    return NextResponse.json({ error: "Failed" }, { status: 500 })
  }
}, { allowUnverified: true })
```

### 2. **Duplicate Catch Blocks**
```typescript
// ❌ Wrong - duplicate catch blocks
try {
  // some code
  return NextResponse.json({ success: true })
} catch (error) {
  return NextResponse.json({ error: "Failed" }, { status: 500 })
} catch (error) {  // ❌ Duplicate!
  return NextResponse.json({ error: "Failed" }, { status: 500 })
}

// ✅ Correct - single catch block
try {
  // some code
  return NextResponse.json({ success: true })
} catch (error) {
  return NextResponse.json({ error: "Failed" }, { status: 500 })
}
```

### 3. **Missing Export Statements**
```typescript
// ❌ Wrong - no exports
async function handleGet() {
  return NextResponse.json({ data: "test" })
}

// ✅ Correct - proper export
export async function GET() {
  return NextResponse.json({ data: "test" })
}

// ✅ Also correct - withAuth export
export const GET = withAuth(async (request, { user, userId }) => {
  return NextResponse.json({ data: "test" })
}, { allowUnverified: true })
```

## Integration with CI/CD

The syntax checker is integrated into the build process:

1. **Pre-build Hook**: Runs automatically before `npm run build`
2. **Manual Check**: Can be run independently with `npm run check-syntax`
3. **Docker Build**: Prevents builds with syntax errors

## Extending the Checker

To add new syntax checks, edit `scripts/check-syntax.sh`:

```bash
# Add new check in the check_basic_syntax function
check_basic_syntax() {
    local file="$1"
    local errors=()
    
    # Your new check here
    if grep -q "your_pattern" "$file"; then
        if ! grep -q "expected_pattern" "$file"; then
            errors+=("Your error message")
        fi
    fi
    
    # Return errors
    if [ ${#errors[@]} -eq 0 ]; then
        return 0
    else
        printf '%s\n' "${errors[@]}"
        return 1
    fi
}
```

## Troubleshooting

### Script Not Executable
```bash
chmod +x scripts/check-syntax.sh
```

### False Positives
If the checker reports false positives, you can:
1. Review the specific check causing the issue
2. Modify the pattern in `check_basic_syntax()`
3. Add exceptions for specific cases

### Performance
The checker is designed to be fast:
- Basic pattern matching (no compilation)
- Parallel processing where possible
- Minimal dependencies

## Best Practices

1. **Run Before Committing**: Always run syntax check before committing code
2. **Fix Issues Immediately**: Don't ignore syntax warnings
3. **Keep It Updated**: Update patterns as new coding patterns emerge
4. **Document Changes**: Update this file when adding new checks

## Related Tools

- **ESLint**: For more comprehensive code quality checks
- **TypeScript Compiler**: For type checking and advanced syntax validation
- **Prettier**: For code formatting consistency

The syntax checker complements these tools by providing fast, build-specific validation.
