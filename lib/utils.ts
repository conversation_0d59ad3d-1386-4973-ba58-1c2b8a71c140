import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

/**
 * Network request logging utility for Docker container logs
 */
interface RequestLogContext {
  component?: string
  action?: string
  userId?: string
  correlationId?: string
}

interface RequestLogData {
  url: string
  method: string
  component: string
  action: string
  timestamp: string
  correlationId: string
  userId?: string
  requestId: string
}

interface ResponseLogData extends RequestLogData {
  status: number
  statusText: string
  responseTime: number
  responseSize?: number
  success: boolean
}

interface ErrorLogData extends RequestLogData {
  errorType: string
  errorMessage: string
  errorStack?: string
  responseTime: number
  status?: number
  networkError: boolean
}

/**
 * Generate a unique request ID for tracking
 */
function generateRequestId(): string {
  return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
}

/**
 * Get the current component name from the call stack
 */
function getComponentFromStack(): string {
  try {
    const stack = new Error().stack
    if (!stack) return 'unknown'

    const lines = stack.split('\n')
    // Look for React component patterns in the stack
    for (const line of lines) {
      if (line.includes('.tsx') || line.includes('.jsx')) {
        const match = line.match(/\/([^\/]+\.(tsx|jsx))/)
        if (match) {
          return match[1].replace(/\.(tsx|jsx)$/, '')
        }
      }
    }
    return 'unknown'
  } catch {
    return 'unknown'
  }
}

/**
 * Send log to server for Docker container logging
 */
async function sendLogToServer(logEntry: any): Promise<void> {
  try {
    // Only send logs to server in production or when explicitly enabled
    if (typeof window !== 'undefined' && (process.env.NODE_ENV === 'production' || process.env.NEXT_PUBLIC_ENABLE_CLIENT_LOGGING === 'true')) {
      await fetch('/api/logs/client', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(logEntry),
        // Don't wait for response to avoid blocking
      }).catch(() => {
        // Silently fail to avoid infinite loops
      })
    }
  } catch {
    // Silently fail to avoid infinite loops
  }
}

/**
 * Enhanced error logging utility for UI components
 * Automatically sends errors to server logs while maintaining console logging for development
 */
export interface UIErrorLogData {
  component: string
  action: string
  errorMessage: string
  errorStack?: string
  errorType?: string
  userId?: string
  additionalContext?: Record<string, any>
}

/**
 * Log UI component errors to both console and server logs
 */
export function logUIError(data: UIErrorLogData): void {
  const logEntry = {
    type: 'UI_COMPONENT_ERROR',
    level: 'error',
    timestamp: new Date().toISOString(),
    component: data.component,
    action: data.action,
    errorType: data.errorType || 'UNKNOWN_ERROR',
    errorMessage: data.errorMessage,
    errorStack: data.errorStack,
    userId: data.userId,
    additionalContext: data.additionalContext,
    userAgent: typeof window !== 'undefined' ? window.navigator.userAgent : undefined,
    url: typeof window !== 'undefined' ? window.location.href : undefined
  }

  // Log to browser console for development
  console.error(`[UI_COMPONENT_ERROR] ${data.component}:${data.action} - ${data.errorMessage}`, {
    ...logEntry,
    originalError: data.errorStack
  })

  // Send to server for Docker container logs
  sendLogToServer(logEntry)
}

/**
 * Enhanced console.error replacement for UI components
 * Use this instead of console.error to ensure errors are logged to server
 */
export function logError(component: string, action: string, error: any, additionalContext?: Record<string, any>): void {
  const errorMessage = error?.message || String(error) || 'Unknown error occurred'
  const errorStack = error?.stack
  const errorType = error?.name || (error?.constructor?.name !== 'Object' ? error?.constructor?.name : undefined)

  logUIError({
    component,
    action,
    errorMessage,
    errorStack,
    errorType,
    additionalContext
  })
}

/**
 * React hook for component error logging
 * Returns a logging function bound to the component name
 */
export function useErrorLogger(componentName: string) {
  return {
    logError: (action: string, error: any, additionalContext?: Record<string, any>) => {
      logError(componentName, action, error, additionalContext)
    },
    logUIError: (action: string, data: Omit<UIErrorLogData, 'component' | 'action'>) => {
      logUIError({
        component: componentName,
        action,
        ...data
      })
    }
  }
}

/**
 * Log network request initiation to Docker container logs
 */
function logRequestStart(data: RequestLogData): void {
  const logEntry = {
    type: 'NETWORK_REQUEST_START',
    level: 'info',
    timestamp: data.timestamp,
    requestId: data.requestId,
    correlationId: data.correlationId,
    component: data.component,
    action: data.action,
    method: data.method,
    url: data.url,
    userId: data.userId
  }

  // Log to browser console for development
  console.log(`[FRONTEND_NETWORK_REQUEST_START] ${JSON.stringify(logEntry)}`)

  // Send to server for Docker container logs
  sendLogToServer(logEntry)
}

/**
 * Log successful network response to Docker container logs
 */
function logRequestSuccess(data: ResponseLogData): void {
  const logEntry = {
    type: 'NETWORK_REQUEST_SUCCESS',
    level: 'info',
    timestamp: new Date().toISOString(),
    requestId: data.requestId,
    correlationId: data.correlationId,
    component: data.component,
    action: data.action,
    method: data.method,
    url: data.url,
    status: data.status,
    statusText: data.statusText,
    responseTime: data.responseTime,
    responseSize: data.responseSize,
    userId: data.userId
  }

  // Log to browser console for development
  console.log(`[FRONTEND_NETWORK_REQUEST_SUCCESS] ${JSON.stringify(logEntry)}`)

  // Send to server for Docker container logs
  sendLogToServer(logEntry)
}

/**
 * Log network request error to Docker container logs
 */
function logRequestError(data: ErrorLogData): void {
  const logEntry = {
    type: 'NETWORK_REQUEST_ERROR',
    level: 'error',
    timestamp: new Date().toISOString(),
    requestId: data.requestId,
    correlationId: data.correlationId,
    component: data.component,
    action: data.action,
    method: data.method,
    url: data.url,
    errorType: data.errorType,
    errorMessage: data.errorMessage,
    errorStack: data.errorStack,
    status: data.status,
    networkError: data.networkError,
    responseTime: data.responseTime,
    userId: data.userId
  }

  // Log to browser console for development
  console.error(`[FRONTEND_NETWORK_REQUEST_ERROR] ${JSON.stringify(logEntry)}`)

  // Send to server for Docker container logs
  sendLogToServer(logEntry)
}

/**
 * Enhanced fetch utility with comprehensive logging for Docker containers
 */
export async function loggedFetch(
  url: string | URL,
  options: RequestInit = {},
  context: RequestLogContext = {}
): Promise<Response> {
  const startTime = Date.now()
  const requestId = generateRequestId()
  const correlationId = context.correlationId || `corr_${Date.now()}_${Math.random().toString(36).substr(2, 6)}`
  const component = context.component || getComponentFromStack()
  const action = context.action || 'fetch'
  const method = (options.method || 'GET').toUpperCase()
  const urlString = url.toString()

  const baseLogData: RequestLogData = {
    url: urlString,
    method,
    component,
    action,
    timestamp: new Date().toISOString(),
    correlationId,
    userId: context.userId,
    requestId
  }

  // Log request initiation
  logRequestStart(baseLogData)

  try {
    const response = await fetch(url, options)
    const responseTime = Date.now() - startTime

    // Get response size if available
    const contentLength = response.headers.get('content-length')
    const responseSize = contentLength ? parseInt(contentLength, 10) : undefined

    if (response.ok) {
      // Log successful response
      logRequestSuccess({
        ...baseLogData,
        status: response.status,
        statusText: response.statusText,
        responseTime,
        responseSize,
        success: true
      })
    } else {
      // Log HTTP error response
      logRequestError({
        ...baseLogData,
        errorType: 'HTTP_ERROR',
        errorMessage: `HTTP ${response.status}: ${response.statusText}`,
        status: response.status,
        networkError: false,
        responseTime
      })
    }

    return response
  } catch (error: any) {
    const responseTime = Date.now() - startTime

    // Determine error type
    let errorType = 'UNKNOWN_ERROR'
    let networkError = false

    if (error.name === 'TypeError' && error.message.includes('fetch')) {
      errorType = 'NETWORK_ERROR'
      networkError = true
    } else if (error.name === 'AbortError') {
      errorType = 'REQUEST_ABORTED'
    } else if (error.message.includes('timeout')) {
      errorType = 'TIMEOUT_ERROR'
      networkError = true
    } else if (error.message.includes('JSON')) {
      errorType = 'JSON_PARSE_ERROR'
    }

    // Log error with full context
    logRequestError({
      ...baseLogData,
      errorType,
      errorMessage: error.message || 'Unknown error occurred',
      errorStack: error.stack,
      networkError,
      responseTime
    })

    // Re-throw the error to maintain existing error handling
    throw error
  }
}

/**
 * Authenticated fetch utility with comprehensive logging
 */
export async function authenticatedFetch(
  url: string | URL,
  options: RequestInit = {},
  context: RequestLogContext = {}
): Promise<Response> {
  // Get token from localStorage (client-side only)
  const token = typeof window !== 'undefined' ? localStorage.getItem('auth-token') : null

  // Merge headers with authentication
  const headers = new Headers(options.headers)

  // Add auth header if token exists
  if (token) {
    headers.set('Authorization', `Bearer ${token}`)
  }

  // Add content-type for POST/PUT requests if not already set
  if ((options.method === 'POST' || options.method === 'PUT') && !headers.has('Content-Type')) {
    headers.set('Content-Type', 'application/json')
  }

  // Always include credentials for cookie-based auth as fallback
  const fetchOptions: RequestInit = {
    ...options,
    headers,
    credentials: 'include'
  }

  // Add authentication context
  const enhancedContext: RequestLogContext = {
    ...context,
    action: context.action || 'authenticated_fetch',
    userId: context.userId || (typeof window !== 'undefined' ? localStorage.getItem('user-id') : undefined)
  }

  return loggedFetch(url, fetchOptions, enhancedContext)
}
