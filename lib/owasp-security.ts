import { NextRequest, NextResponse } from 'next/server'
import { createErrorResponse } from './validation'
import { SecurityValidator, InputSanitizer } from './sql-security'
import { logSecurityEvent } from './logging'

/**
 * OWASP API Security Top 10 protection middleware
 */

interface OWASPSecurityOptions {
  // API1: Broken Object Level Authorization
  enableObjectLevelAuth?: boolean
  
  // API2: Broken User Authentication
  enableAuthValidation?: boolean
  
  // API3: Excessive Data Exposure
  enableDataFiltering?: boolean
  
  // API4: Lack of Resources & Rate Limiting
  enableRateLimiting?: boolean
  
  // API5: Broken Function Level Authorization
  enableFunctionLevelAuth?: boolean
  
  // API6: Mass Assignment
  enableMassAssignmentProtection?: boolean
  
  // API7: Security Misconfiguration
  enableSecurityHeaders?: boolean
  
  // API8: Injection
  enableInjectionProtection?: boolean
  
  // API9: Improper Assets Management
  enableAssetValidation?: boolean
  
  // API10: Insufficient Logging & Monitoring
  enableSecurityLogging?: boolean
}

const DEFAULT_OWASP_OPTIONS: OWASPSecurityOptions = {
  enableObjectLevelAuth: true,
  enableAuthValidation: true,
  enableDataFiltering: true,
  enableRateLimiting: true,
  enableFunctionLevelAuth: true,
  enableMassAssignmentProtection: true,
  enableSecurityHeaders: true,
  enableInjectionProtection: true,
  enableAssetValidation: true,
  enableSecurityLogging: true
}

/**
 * API3: Excessive Data Exposure - Data filtering utilities
 */
export class DataFilter {
  /**
   * Remove sensitive fields from response data
   */
  static filterSensitiveData(data: any, sensitiveFields: string[] = []): any {
    const defaultSensitiveFields = [
      'password',
      'passwordHash',
      'token',
      'sessionToken',
      'apiKey',
      'secret',
      'privateKey',
      'creditCard',
      'ssn',
      'bankAccount'
    ]
    
    const allSensitiveFields = [...defaultSensitiveFields, ...sensitiveFields]
    
    if (Array.isArray(data)) {
      return data.map(item => this.filterObject(item, allSensitiveFields))
    }
    
    return this.filterObject(data, allSensitiveFields)
  }
  
  private static filterObject(obj: any, sensitiveFields: string[]): any {
    if (!obj || typeof obj !== 'object') {
      return obj
    }
    
    const filtered: any = {}
    
    for (const [key, value] of Object.entries(obj)) {
      const lowerKey = key.toLowerCase()
      
      // Check if field is sensitive
      if (sensitiveFields.some(field => lowerKey.includes(field.toLowerCase()))) {
        filtered[key] = '[FILTERED]'
        continue
      }
      
      // Recursively filter nested objects
      if (typeof value === 'object' && value !== null) {
        if (Array.isArray(value)) {
          filtered[key] = value.map(item => this.filterObject(item, sensitiveFields))
        } else {
          filtered[key] = this.filterObject(value, sensitiveFields)
        }
      } else {
        filtered[key] = value
      }
    }
    
    return filtered
  }
}

/**
 * API6: Mass Assignment Protection
 */
export class MassAssignmentProtector {
  /**
   * Filter request body to only allow specified fields
   */
  static filterAllowedFields(body: any, allowedFields: string[]): any {
    if (!body || typeof body !== 'object') {
      return body
    }
    
    const filtered: any = {}
    
    for (const field of allowedFields) {
      if (body.hasOwnProperty(field)) {
        filtered[field] = body[field]
      }
    }
    
    return filtered
  }
  
  /**
   * Remove dangerous fields from request body
   */
  static removeDangerousFields(body: any): any {
    const dangerousFields = [
      'id',
      'userId',
      'createdAt',
      'updatedAt',
      'role',
      'permissions',
      'isAdmin',
      'isActive',
      'emailVerified',
      '__proto__',
      'constructor',
      'prototype'
    ]
    
    if (!body || typeof body !== 'object') {
      return body
    }
    
    const filtered = { ...body }
    
    for (const field of dangerousFields) {
      delete filtered[field]
    }
    
    return filtered
  }
}

/**
 * API8: Injection Protection
 */
export class InjectionProtector {
  /**
   * Validate and sanitize all string inputs
   */
  static sanitizeInputs(data: any): any {
    if (typeof data === 'string') {
      const validation = SecurityValidator.validateInput(data)
      if (!validation.safe) {
        throw new Error(`Security threat detected: ${validation.threats.join(', ')}`)
      }
      return InputSanitizer.sanitizeString(data)
    }
    
    if (Array.isArray(data)) {
      return data.map(item => this.sanitizeInputs(item))
    }
    
    if (data && typeof data === 'object') {
      const sanitized: any = {}
      for (const [key, value] of Object.entries(data)) {
        sanitized[key] = this.sanitizeInputs(value)
      }
      return sanitized
    }
    
    return data
  }
}

/**
 * API7: Security Misconfiguration - Enhanced security headers
 */
export function addEnhancedSecurityHeaders(response: NextResponse): NextResponse {
  // Prevent clickjacking
  response.headers.set('X-Frame-Options', 'DENY')
  
  // Prevent MIME type sniffing
  response.headers.set('X-Content-Type-Options', 'nosniff')
  
  // XSS protection
  response.headers.set('X-XSS-Protection', '1; mode=block')
  
  // Referrer policy
  response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin')
  
  // Content Security Policy
  response.headers.set('Content-Security-Policy', 
    "default-src 'self'; " +
    "script-src 'self' 'unsafe-inline' 'unsafe-eval'; " +
    "style-src 'self' 'unsafe-inline'; " +
    "img-src 'self' data: https:; " +
    "font-src 'self' data:; " +
    "connect-src 'self'; " +
    "frame-ancestors 'none'; " +
    "base-uri 'self'; " +
    "form-action 'self'"
  )
  
  // Permissions policy
  response.headers.set('Permissions-Policy', 
    'camera=(), microphone=(), geolocation=(), payment=(), usb=()'
  )
  
  // HSTS (if HTTPS)
  if (process.env.NODE_ENV === 'production') {
    response.headers.set('Strict-Transport-Security', 'max-age=31536000; includeSubDomains; preload')
  }
  
  // Prevent caching of sensitive data
  response.headers.set('Cache-Control', 'no-store, no-cache, must-revalidate, proxy-revalidate')
  response.headers.set('Pragma', 'no-cache')
  response.headers.set('Expires', 'Thu, 01 Jan 1970 00:00:00 GMT')
  
  // Remove server information
  response.headers.delete('Server')
  response.headers.delete('X-Powered-By')
  
  return response
}

/**
 * Comprehensive OWASP API Security middleware
 */
export function withOWASPSecurity(
  handler: (request: NextRequest, context: any, routeParams?: any) => Promise<NextResponse>,
  options: OWASPSecurityOptions = {},
  allowedFields?: string[]
) {
  const config = { ...DEFAULT_OWASP_OPTIONS, ...options }
  
  return async (request: NextRequest, context: any, routeParams?: any) => {
    const { correlationId } = context
    const url = new URL(request.url)
    
    try {
      // API8: Injection Protection
      if (config.enableInjectionProtection) {
        // Check URL for injection attempts
        const urlValidation = SecurityValidator.validateInput(url.pathname + url.search)
        if (!urlValidation.safe) {
          logSecurityEvent('injection_attempt_url', {
            threats: urlValidation.threats,
            url: url.pathname,
            query: url.search
          }, request, correlationId)
          
          return createErrorResponse(
            'Security violation detected',
            400,
            correlationId,
            undefined,
            'Request contains potentially malicious content',
            url.pathname
          )
        }
        
        // Check headers for injection attempts (skip user-agent and referer as they often contain false positives)
        for (const [name, value] of request.headers.entries()) {
          // Only check custom headers and authorization headers for injection
          if (['authorization', 'x-api-key', 'x-custom-header'].includes(name.toLowerCase())) {
            const headerValidation = SecurityValidator.validateInput(value)
            if (!headerValidation.safe) {
              logSecurityEvent('injection_attempt_header', {
                header: name,
                threats: headerValidation.threats
              }, request, correlationId)

              return createErrorResponse(
                'Security violation detected',
                400,
                correlationId,
                undefined,
                'Request headers contain potentially malicious content',
                url.pathname
              )
            }
          }
        }
      }
      
      // API6: Mass Assignment Protection
      if (config.enableMassAssignmentProtection && ['POST', 'PUT', 'PATCH'].includes(request.method)) {
        try {
          const body = await request.json()
          let filteredBody = body
          
          // Remove dangerous fields
          filteredBody = MassAssignmentProtector.removeDangerousFields(filteredBody)
          
          // Filter to allowed fields if specified
          if (allowedFields) {
            filteredBody = MassAssignmentProtector.filterAllowedFields(filteredBody, allowedFields)
          }
          
          // Sanitize inputs
          if (config.enableInjectionProtection) {
            filteredBody = InjectionProtector.sanitizeInputs(filteredBody)
          }
          
          // Replace request body with filtered version
          const modifiedRequest = new Request(request.url, {
            method: request.method,
            headers: request.headers,
            body: JSON.stringify(filteredBody)
          })
          
          // Call handler with modified request
          const response = await handler(modifiedRequest as NextRequest, context, routeParams)
          
          // API7: Security Headers
          if (config.enableSecurityHeaders) {
            addEnhancedSecurityHeaders(response)
          }
          
          // API3: Data Filtering
          if (config.enableDataFiltering) {
            try {
              const responseClone = response.clone()
              const responseData = await responseClone.json()
              const filteredData = DataFilter.filterSensitiveData(responseData)
              
              return NextResponse.json(filteredData, {
                status: response.status,
                headers: response.headers
              })
            } catch {
              // If response is not JSON, return as-is
              return response
            }
          }
          
          return response
          
        } catch (error) {
          // If body parsing fails, continue with original request
          const response = await handler(request, context, routeParams)
          
          if (config.enableSecurityHeaders) {
            addEnhancedSecurityHeaders(response)
          }
          
          return response
        }
      } else {
        // For GET requests or when mass assignment protection is disabled
        const response = await handler(request, context, routeParams)
        
        if (config.enableSecurityHeaders) {
          addEnhancedSecurityHeaders(response)
        }
        
        if (config.enableDataFiltering) {
          try {
            const responseClone = response.clone()
            const responseData = await responseClone.json()
            const filteredData = DataFilter.filterSensitiveData(responseData)
            
            return NextResponse.json(filteredData, {
              status: response.status,
              headers: response.headers
            })
          } catch {
            return response
          }
        }
        
        return response
      }
      
    } catch (error) {
      if (config.enableSecurityLogging) {
        logSecurityEvent('owasp_middleware_error', {
          error: error.message,
          path: url.pathname
        }, request, correlationId)
      }
      
      return createErrorResponse(
        'Internal server error',
        500,
        correlationId,
        undefined,
        'An error occurred while processing the request',
        url.pathname
      )
    }
  }
}
