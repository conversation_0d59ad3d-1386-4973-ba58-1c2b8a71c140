import { NextRequest } from 'next/server'

// Simple metrics recording (avoiding circular import)
let requestMetrics = {
  total: 0,
  errors: 0,
  totalResponseTime: 0,
  lastReset: Date.now()
}

export function recordRequestMetrics(responseTime: number, isError: boolean = false) {
  requestMetrics.total++
  requestMetrics.totalResponseTime += responseTime
  if (isError) {
    requestMetrics.errors++
  }

  // Reset metrics every hour
  if (Date.now() - requestMetrics.lastReset > 3600000) {
    requestMetrics = {
      total: 0,
      errors: 0,
      totalResponseTime: 0,
      lastReset: Date.now()
    }
  }
}

export function getRequestMetrics() {
  return { ...requestMetrics }
}

// Sensitive fields that should be redacted in logs
const SENSITIVE_FIELDS = [
  'password',
  'passwordHash',
  'currentPassword',
  'newPassword',
  'token',
  'tokenHash',
  'sessionToken',
  'authToken',
  'authorization',
  'cookie',
  'cookies',
  'creditCard',
  'ssn',
  'socialSecurityNumber',
  'bankAccount',
  'routingNumber'
]

// Sensitive headers that should be redacted
const SENSITIVE_HEADERS = [
  'authorization',
  'cookie',
  'x-api-key',
  'x-auth-token',
  'x-session-token'
]

// PII fields that should be partially redacted
const PII_FIELDS = [
  'email',
  'phone',
  'phoneNumber',
  'ipAddress',
  'ip'
]

/**
 * Redact sensitive information from an object
 */
export function redactSensitiveData(obj: any, options: {
  redactPII?: boolean
  customSensitiveFields?: string[]
} = {}): any {
  if (!obj || typeof obj !== 'object') {
    return obj
  }

  const { redactPII = false, customSensitiveFields = [] } = options
  const sensitiveFields = [...SENSITIVE_FIELDS, ...customSensitiveFields]

  if (Array.isArray(obj)) {
    return obj.map(item => redactSensitiveData(item, options))
  }

  const redacted: any = {}

  for (const [key, value] of Object.entries(obj)) {
    const lowerKey = key.toLowerCase()

    // Completely redact sensitive fields
    if (sensitiveFields.some(field => lowerKey.includes(field.toLowerCase()))) {
      redacted[key] = '[REDACTED]'
      continue
    }

    // Partially redact PII if requested
    if (redactPII && PII_FIELDS.some(field => lowerKey.includes(field.toLowerCase()))) {
      if (typeof value === 'string') {
        if (lowerKey.includes('email')) {
          // Redact email: <EMAIL> -> u***@d***.com
          const emailMatch = value.match(/^([^@])([^@]*)@([^.])([^.]*)\.(.+)$/)
          if (emailMatch) {
            redacted[key] = `${emailMatch[1]}***@${emailMatch[3]}***.${emailMatch[5]}`
          } else {
            redacted[key] = '[REDACTED_EMAIL]'
          }
        } else if (lowerKey.includes('ip')) {
          // Redact IP: *********** -> 192.168.*.*
          const ipParts = value.split('.')
          if (ipParts.length === 4) {
            redacted[key] = `${ipParts[0]}.${ipParts[1]}.*.*`
          } else {
            redacted[key] = '[REDACTED_IP]'
          }
        } else {
          // Generic PII redaction
          redacted[key] = value.length > 3 ? `${value.substring(0, 2)}***` : '[REDACTED]'
        }
      } else {
        redacted[key] = '[REDACTED_PII]'
      }
      continue
    }

    // Recursively redact nested objects
    if (typeof value === 'object' && value !== null) {
      redacted[key] = redactSensitiveData(value, options)
    } else {
      redacted[key] = value
    }
  }

  return redacted
}

/**
 * Redact sensitive headers from request
 */
export function redactRequestHeaders(headers: Headers): Record<string, string> {
  const redactedHeaders: Record<string, string> = {}

  headers.forEach((value, key) => {
    const lowerKey = key.toLowerCase()
    
    if (SENSITIVE_HEADERS.includes(lowerKey)) {
      redactedHeaders[key] = '[REDACTED]'
    } else {
      redactedHeaders[key] = value
    }
  })

  return redactedHeaders
}

/**
 * Create a safe log context from a request
 */
export function createRequestLogContext(
  request: NextRequest,
  options: {
    includeBody?: boolean
    redactPII?: boolean
    correlationId?: string
  } = {}
): any {
  const { includeBody = false, redactPII = true, correlationId } = options

  const context: any = {
    method: request.method,
    url: request.url,
    userAgent: request.headers.get('user-agent'),
    correlationId,
    timestamp: new Date().toISOString(),
    headers: redactRequestHeaders(request.headers)
  }

  // Add IP address with redaction
  const ip = request.headers.get('x-forwarded-for') || 
             request.headers.get('x-real-ip') || 
             'unknown'
  
  if (redactPII && ip !== 'unknown') {
    context.clientIp = redactSensitiveData({ ip }, { redactPII: true }).ip
  } else {
    context.clientIp = ip
  }

  return context
}

/**
 * Log levels
 */
export enum LogLevel {
  ERROR = 'error',
  WARN = 'warn',
  INFO = 'info',
  DEBUG = 'debug'
}

/**
 * Structured logger with automatic sensitive data redaction
 */
export class Logger {
  private context: any = {}

  constructor(context: any = {}) {
    this.context = redactSensitiveData(context, { redactPII: true })
  }

  private log(level: LogLevel, message: string, data: any = {}) {
    const logEntry = {
      level,
      message,
      timestamp: new Date().toISOString(),
      ...this.context,
      ...redactSensitiveData(data, { redactPII: true })
    }

    // In production, you might want to send this to a logging service
    console.log(JSON.stringify(logEntry))
  }

  error(message: string, data?: any) {
    this.log(LogLevel.ERROR, message, data)
  }

  warn(message: string, data?: any) {
    this.log(LogLevel.WARN, message, data)
  }

  info(message: string, data?: any) {
    this.log(LogLevel.INFO, message, data)
  }

  debug(message: string, data?: any) {
    this.log(LogLevel.DEBUG, message, data)
  }

  /**
   * Create a child logger with additional context
   */
  child(additionalContext: any): Logger {
    return new Logger({
      ...this.context,
      ...additionalContext
    })
  }
}

/**
 * Create a logger instance with request context
 */
export function createRequestLogger(
  request: NextRequest,
  correlationId: string,
  additionalContext: any = {}
): Logger {
  const requestContext = createRequestLogContext(request, {
    redactPII: true,
    correlationId
  })

  const logger = new Logger({
    ...requestContext,
    ...additionalContext
  })

  // Record request start time for metrics
  const startTime = Date.now()

  // Enhance logger with metrics recording
  const originalError = logger.error.bind(logger)

  logger.error = (message: string, data?: any) => {
    const responseTime = Date.now() - startTime
    recordRequestMetrics(responseTime, true)
    originalError(message, { ...data, responseTime })
  }

  // Add a method to record successful completion
  ;(logger as any).recordSuccess = (data?: any) => {
    const responseTime = Date.now() - startTime
    recordRequestMetrics(responseTime, false)
    logger.info('Request completed successfully', { ...data, responseTime })
  }

  return logger
}

/**
 * Security event logging
 */
export function logSecurityEvent(
  event: string,
  details: any,
  request?: NextRequest,
  correlationId?: string
) {
  const logger = request 
    ? createRequestLogger(request, correlationId || 'unknown')
    : new Logger({ correlationId })

  logger.warn(`Security Event: ${event}`, {
    securityEvent: event,
    details: redactSensitiveData(details, { redactPII: true })
  })
}

/**
 * Audit logging for sensitive operations
 */
export async function logAuditEvent(
  action: string,
  userId: string,
  resourceType: string,
  resourceId: string,
  details: any = {},
  request?: NextRequest,
  correlationId?: string
) {
  const logger = request
    ? createRequestLogger(request, correlationId || 'unknown')
    : new Logger({ correlationId })

  // Log to console for immediate visibility
  logger.info(`Audit: ${action}`, {
    audit: {
      action,
      userId,
      resourceType,
      resourceId,
      details: redactSensitiveData(details, { redactPII: true })
    }
  })

  // Store in database for persistence and admin interface
  try {
    // Use dynamic import to avoid circular dependencies
    const { auditLogs } = require('./database')

    const ipAddress = request?.headers.get('x-forwarded-for') ||
                     request?.headers.get('x-real-ip') ||
                     null
    const userAgent = request?.headers.get('user-agent') || null

    await auditLogs.create({
      userId,
      action,
      resourceType,
      resourceId,
      ipAddress,
      userAgent,
      details: redactSensitiveData(details, { redactPII: true })
    })
  } catch (error) {
    // Don't fail the request if audit logging fails, but log the error
    console.error('Failed to store audit log in database:', error)
  }
}
