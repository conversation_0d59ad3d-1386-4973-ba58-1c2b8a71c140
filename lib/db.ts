let Pool: any
try {
  Pool = require('pg').Pool
} catch (error) {
  console.warn('PostgreSQL driver not found. Please install pg: npm install pg @types/pg')
  Pool = null
}

// Database connection pool
let pool: Pool | null = null

export function getPool(): any {
  if (!Pool) {
    throw new Error('PostgreSQL driver not available. Please install: npm install pg @types/pg')
  }

  if (!pool) {
    pool = new Pool({
      connectionString: process.env.DATABASE_URL || 'postgresql://papernugget:password@localhost:5432/papernugget',
      max: 20,
      idleTimeoutMillis: 30000,
      connectionTimeoutMillis: 2000,
    })
  }
  return pool
}

export async function query(text: string, params?: any[]) {
  try {
    const pool = getPool()
    const client = await pool.connect()
    try {
      const result = await client.query(text, params)
      return result
    } finally {
      client.release()
    }
  } catch (error) {
    console.error('Database query failed:', error)
    throw error
  }
}

// Close the pool when the process exits
process.on('SIGINT', () => {
  if (pool) {
    pool.end()
  }
})

process.on('SIGTERM', () => {
  if (pool) {
    pool.end()
  }
})
