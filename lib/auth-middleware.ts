import { NextRequest, NextResponse } from 'next/server'
import { users, userSessions } from './database'
import { generateSessionTokenHash, getUserIdFromToken, isTokenExpired } from './auth'
import { createErrorResponse, generateCorrelationId } from './validation'
import type { User } from './types'

export interface AuthenticatedRequest extends NextRequest {
  user?: User
  userId?: string
}

/**
 * Middleware to authenticate requests
 */
export async function authenticateRequest(request: NextRequest): Promise<{
  authenticated: boolean
  user?: User
  userId?: string
  error?: string
}> {
  try {
    // Get token from Authorization header or cookie
    const authHeader = request.headers.get('authorization')
    const cookieToken = request.cookies.get('auth-token')?.value

    const token = authHeader?.replace('Bearer ', '') || cookieToken

    if (!token) {
      return { authenticated: false, error: 'No token provided' }
    }

    // Get user ID from token
    const userId = getUserIdFromToken(token)
    if (!userId) {
      return { authenticated: false, error: 'Invalid token' }
    }

    // Verify session exists in database
    const tokenHash = generateSessionTokenHash(token)
    const session = await userSessions.getByTokenHash(tokenHash)

    if (!session) {
      return { authenticated: false, error: 'Session not found' }
    }

    // Check if session has expired
    if (isTokenExpired(session.expiresAt)) {
      // Clean up expired session
      await userSessions.deleteByTokenHash(tokenHash)
      return { authenticated: false, error: 'Session expired' }
    }

    // Get user data
    const user = await users.getById(userId)
    if (!user) {
      return { authenticated: false, error: 'User not found' }
    }

    // Check if user is still active
    if (!user.isActive) {
      return { authenticated: false, error: 'Account is deactivated' }
    }

    return { authenticated: true, user, userId }

  } catch (error) {
    console.error('Authentication error:', error)
    return { authenticated: false, error: 'Authentication failed' }
  }
}

/**
 * Middleware to check if user has required role
 */
export function hasRole(userRole: string, requiredRoles: string[]): boolean {
  return requiredRoles.includes(userRole)
}

/**
 * Middleware to check if user can access resource (owns it or is admin)
 */
export function canAccessResource(user: User, resourceUserId: string): boolean {
  return user.role === 'admin' || user.id === resourceUserId
}

/**
 * Higher-order function to create protected API routes
 */
export function withAuth(
  handler: (request: NextRequest, context: { user: User; userId: string; correlationId: string }, routeParams?: any) => Promise<NextResponse>,
  options: {
    requiredRoles?: string[]
    allowUnverified?: boolean
  } = {}
) {
  return async (request: NextRequest, routeParams?: any) => {
    const correlationId = generateCorrelationId()
    const url = new URL(request.url)
    const auth = await authenticateRequest(request)

    if (!auth.authenticated || !auth.user || !auth.userId) {
      return createErrorResponse(
        'Authentication required',
        401,
        correlationId,
        undefined,
        auth.error || 'Valid authentication token required',
        url.pathname
      )
    }

    // Check email verification if required
    if (!options.allowUnverified && !auth.user.emailVerified) {
      return createErrorResponse(
        'Email verification required',
        403,
        correlationId,
        undefined,
        'Please verify your email address before accessing this resource',
        url.pathname
      )
    }

    // Check role requirements
    if (options.requiredRoles && !hasRole(auth.user.role, options.requiredRoles)) {
      return createErrorResponse(
        'Insufficient permissions',
        403,
        correlationId,
        { requiredRoles: options.requiredRoles, userRole: auth.user.role },
        'You do not have the required permissions to access this resource',
        url.pathname
      )
    }

    // Call the actual handler with authenticated context
    return handler(request, { user: auth.user, userId: auth.userId, correlationId }, routeParams)
  }
}

/**
 * Utility to get client IP address
 */
export function getClientIP(request: NextRequest): string {
  return request.headers.get('x-forwarded-for') ||
         request.headers.get('x-real-ip') ||
         'unknown'
}

/**
 * Utility to get user agent
 */
export function getUserAgent(request: NextRequest): string {
  return request.headers.get('user-agent') || 'unknown'
}