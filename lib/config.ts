/**
 * Configuration validation and management
 * Validates required environment variables at boot time
 */

interface EmailConfig {
  appUrl: string
  emailFrom: string
  smtpHost: string
  smtpPort: number
  smtpUser: string
  smtpPass: string
  smtpTls: boolean
}

interface AppConfig {
  nodeEnv: string
  port: number
  databaseUrl: string
  email: EmailConfig
  kofiUrl?: string
}

class ConfigError extends Error {
  constructor(message: string) {
    super(message)
    this.name = 'ConfigError'
  }
}

function validateRequired(value: string | undefined, name: string): string {
  if (!value || value.trim() === '') {
    throw new ConfigError(`Missing required environment variable: ${name}`)
  }
  return value.trim()
}

function validateOptional(value: string | undefined, defaultValue: string): string {
  return value?.trim() || defaultValue
}

function validateOptionalUrl(value: string | undefined, name: string): string | undefined {
  if (!value || value.trim() === '') {
    return undefined
  }

  try {
    new URL(value.trim())
    return value.trim()
  } catch {
    throw new ConfigError(`Invalid URL for environment variable: ${name}`)
  }
}

function validateNumber(value: string | undefined, name: string, defaultValue?: number): number {
  if (!value && defaultValue !== undefined) {
    return defaultValue
  }
  
  const num = Number(value)
  if (isNaN(num)) {
    throw new ConfigError(`Invalid number for environment variable: ${name}`)
  }
  return num
}

function validateBoolean(value: string | undefined, defaultValue: boolean): boolean {
  if (!value) return defaultValue
  return value.toLowerCase() === 'true'
}

function validateUrl(value: string, name: string): string {
  try {
    new URL(value)
    return value
  } catch {
    throw new ConfigError(`Invalid URL for environment variable: ${name}`)
  }
}

function validateEmail(value: string, name: string): string {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  if (!emailRegex.test(value)) {
    throw new ConfigError(`Invalid email format for environment variable: ${name}`)
  }
  return value
}

function loadConfig(): AppConfig {
  try {
    // Basic app configuration
    const nodeEnv = validateOptional(process.env.NODE_ENV, 'development')
    const port = validateNumber(process.env.PORT, 'PORT', 3000)
    const databaseUrl = validateRequired(process.env.DATABASE_URL, 'DATABASE_URL')
    
    // Email configuration
    const appUrl = validateUrl(
      validateRequired(process.env.APP_URL, 'APP_URL'),
      'APP_URL'
    )
    
    const emailFrom = validateEmail(
      validateRequired(process.env.EMAIL_FROM, 'EMAIL_FROM'),
      'EMAIL_FROM'
    )
    
    const smtpHost = validateRequired(process.env.SMTP_HOST, 'SMTP_HOST')
    const smtpPort = validateNumber(process.env.SMTP_PORT, 'SMTP_PORT')
    const smtpUser = validateOptional(process.env.SMTP_USER, '')
    const smtpPass = validateOptional(process.env.SMTP_PASS, '')
    const smtpTls = validateBoolean(process.env.SMTP_TLS, false)

    // Optional Ko-fi support URL
    const kofiUrl = validateOptionalUrl(process.env.KO_FI_URL, 'KO_FI_URL')

    return {
      nodeEnv,
      port,
      databaseUrl,
      email: {
        appUrl,
        emailFrom,
        smtpHost,
        smtpPort,
        smtpUser,
        smtpPass,
        smtpTls,
      },
      kofiUrl,
    }
  } catch (error) {
    if (error instanceof ConfigError) {
      console.error('❌ Configuration Error:', error.message)
      console.error('Please check your environment variables and try again.')
      process.exit(1)
    }
    throw error
  }
}

// Load and validate configuration (lazy loading to avoid build-time issues)
let _config: AppConfig | null = null

export const config = new Proxy({} as AppConfig, {
  get(target, prop) {
    if (!_config) {
      _config = loadConfig()

      // Log configuration (without sensitive data) in development
      if (_config.nodeEnv === 'development' && typeof window === 'undefined') {
        console.log('📧 Email Configuration:')
        console.log(`  App URL: ${_config.email.appUrl}`)
        console.log(`  From: ${_config.email.emailFrom}`)
        console.log(`  SMTP Host: ${_config.email.smtpHost}:${_config.email.smtpPort}`)
        console.log(`  SMTP TLS: ${_config.email.smtpTls}`)
        console.log(`  SMTP Auth: ${_config.email.smtpUser ? 'Yes' : 'No'}`)
      }
    }
    return _config[prop as keyof AppConfig]
  }
})

/**
 * Extract base URL from request headers for environment-agnostic email links
 * Supports local development, Cloudflare Tunnel, Traefik, Nginx, and production domains
 */
export function getBaseUrlFromRequest(request: Request): string {
  const headers = request.headers

  // Check for forwarded protocol and host (reverse proxy scenarios)
  const forwardedProto = headers.get('x-forwarded-proto')
  const forwardedHost = headers.get('x-forwarded-host')

  if (forwardedProto && forwardedHost) {
    return `${forwardedProto}://${forwardedHost}`
  }

  // Check for standard forwarded headers
  const proto = headers.get('x-forwarded-proto') ||
                (headers.get('x-forwarded-ssl') === 'on' ? 'https' : 'http')
  const host = headers.get('x-forwarded-host') ||
               headers.get('host')

  if (host) {
    return `${proto}://${host}`
  }

  // Fallback to request URL origin
  try {
    const url = new URL(request.url)
    return url.origin
  } catch {
    // Final fallback to config (for backwards compatibility)
    return _config?.email.appUrl || 'http://localhost:3000'
  }
}

export { ConfigError }
