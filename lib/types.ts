export type PaperType =
  | 'journalArticle'
  | 'conferencePaper'
  | 'book'
  | 'bookSection'
  | 'thesis'
  | 'report'
  | 'preprint'
  | 'manuscript'
  | 'webpage'
  | 'blogPost'
  | 'presentation'
  | 'patent'
  | 'dataset'
  | 'software'
  | 'artwork'
  | 'audioRecording'
  | 'videoRecording'
  | 'interview'
  | 'letter'
  | 'email'
  | 'forumPost'
  | 'encyclopediaArticle'
  | 'dictionaryEntry'
  | 'magazineArticle'
  | 'newspaperArticle'
  | 'radioBroadcast'
  | 'tvBroadcast'
  | 'podcast'
  | 'case'
  | 'statute'
  | 'bill'
  | 'hearing'
  | 'standard'
  | 'map'
  | 'film'
  | 'document'

export const PAPER_TYPE_LABELS: Record<PaperType, string> = {
  journalArticle: 'Journal Article',
  conferencePaper: 'Conference Paper',
  book: 'Book',
  bookSection: 'Book Section',
  thesis: 'Thesis',
  report: 'Report',
  preprint: 'Preprint',
  manuscript: 'Manuscript',
  webpage: 'Web Page',
  blogPost: 'Blog Post',
  presentation: 'Presentation',
  patent: 'Patent',
  dataset: 'Dataset',
  software: 'Software',
  artwork: 'Artwork',
  audioRecording: 'Audio Recording',
  videoRecording: 'Video Recording',
  interview: 'Interview',
  letter: 'Letter',
  email: 'Email',
  forumPost: 'Forum Post',
  encyclopediaArticle: 'Encyclopedia Article',
  dictionaryEntry: 'Dictionary Entry',
  magazineArticle: 'Magazine Article',
  newspaperArticle: 'Newspaper Article',
  radioBroadcast: 'Radio Broadcast',
  tvBroadcast: 'TV Broadcast',
  podcast: 'Podcast',
  case: 'Legal Case',
  statute: 'Statute',
  bill: 'Bill',
  hearing: 'Hearing',
  standard: 'Standard',
  map: 'Map',
  film: 'Film',
  document: 'Document'
}

// Common paper types for easier selection
export const COMMON_PAPER_TYPES: PaperType[] = [
  'journalArticle',
  'conferencePaper',
  'book',
  'bookSection',
  'thesis',
  'report',
  'preprint',
  'manuscript',
  'webpage',
  'presentation'
]

export interface Paper {
  id: string
  title: string
  authors: string[]
  venue?: string
  year?: number
  doi?: string
  url?: string
  abstract?: string
  citationCount?: number
  referenceCount?: number
  publicationDate?: string
  journal?: string
  volume?: string
  issue?: string
  pages?: string
  tags: string[]
  starred: boolean
  userId: string
  createdAt: string
  updatedAt: string
  paperType: PaperType
  // Zotero sync fields
  zoteroItemKey?: string
  zoteroNoteKey?: string
  zoteroLastSynced?: string
  zoteroSyncStatus?: 'not_synced' | 'synced' | 'error' | 'pending'
}

export interface Note {
  id: string
  paperId: string
  quickSummary?: string
  keyIdeas: string[]
  createdAt?: string
  updatedAt?: string
}

export interface Collection {
  id: string
  name: string
  paperIds: string[]
  userId: string
  createdAt?: string
  updatedAt?: string
  // Zotero sync destination (optional - falls back to user's global settings if not set)
  zoteroLibraryType?: 'user' | 'group'
  zoteroLibraryId?: string // null/undefined for user library, group ID for group libraries
}

export interface Review {
  paperId: string
  ease: number
  nextDue: string
  lastInterval?: number
}

// User Management Types
export interface User {
  id: string
  email: string
  displayName?: string
  role: 'admin' | 'user' | 'readonly'
  emailVerified: boolean
  isActive: boolean
  privacySettings: Record<string, any>
  preferences: Record<string, any>
  createdAt: string
  updatedAt: string
  lastLogin?: string
}

export interface UserSession {
  id: string
  userId: string
  tokenHash: string
  expiresAt: string
  createdAt: string
  ipAddress?: string
  userAgent?: string
}

export interface PasswordResetToken {
  id: string
  userId: string
  tokenHash: string
  expiresAt: string
  used: boolean
  createdAt: string
}

export interface EmailVerificationToken {
  id: string
  userId: string
  tokenHash: string
  expiresAt: string
  used: boolean
  createdAt: string
}

export interface AuditLog {
  id: string
  userId?: string
  action: string
  resourceType?: string
  resourceId?: string
  ipAddress?: string
  userAgent?: string
  details: Record<string, any>
  createdAt: string
}

// Authentication Types
export interface LoginRequest {
  email: string
  password: string
}

export interface RegisterRequest {
  email: string
  password: string
  displayName?: string
}

export interface AuthResponse {
  user: User
  token: string
  expiresAt: string
}

export interface PasswordResetRequest {
  email: string
}

export interface PasswordResetConfirm {
  token: string
  newPassword: string
}

export interface ChangePasswordRequest {
  currentPassword: string
  newPassword: string
}

export interface UpdateProfileRequest {
  displayName?: string
  privacySettings?: Record<string, any>
  preferences?: Record<string, any>
}

export interface ResendVerificationRequest {
  email: string
}

export interface VerifyEmailRequest {
  token: string
}

// Zotero Integration Types
export interface ZoteroSettings {
  apiKey?: string
  libraryType: 'user' | 'group'
  libraryId?: string
  enabled: boolean
}

export interface ZoteroItem {
  key?: string
  version?: number
  itemType: string
  title?: string
  creators?: Array<{
    creatorType: string
    firstName?: string
    lastName?: string
    name?: string
  }>
  abstractNote?: string
  publicationTitle?: string
  proceedingsTitle?: string
  bookTitle?: string
  publisher?: string
  volume?: string
  issue?: string
  pages?: string
  date?: string
  DOI?: string
  url?: string
  tags?: Array<{
    tag: string
    type?: number
  }>
}

export interface ZoteroNote {
  key?: string
  version?: number
  itemType: 'note'
  parentItem: string
  note: string
  tags?: Array<{
    tag: string
    type?: number
  }>
  relations?: Record<string, any>
  collections?: string[]
  dateAdded?: string
  dateModified?: string
}

export interface ZoteroSyncResult {
  success: boolean
  itemKey?: string
  noteKey?: string
  error?: string
  message?: string
}

export interface ZoteroLibrary {
  id: string
  name: string
  type: 'user' | 'group'
  description?: string
}

// Review Session Types
export interface ReviewStatus {
  inReview: boolean
  isDue: boolean
  daysUntilDue: number | null
  nextDue: string | null
}

export interface ReviewSession {
  papers: (Paper & { note?: Note | null; review?: Review })[]
  sessionId: string
  startedAt: string
  totalCount: number
}

export interface ReviewSessionResult {
  paperId: string
  success: boolean
  nextDue?: string
  interval?: number
  reason?: string
}

export interface ReviewSessionCompletion {
  sessionId: string
  completedAt: string
  results: ReviewSessionResult[]
  summary: {
    total: number
    successful: number
    failed: number
  }
}

export interface PasswordResetConfirmRequest {
  token: string
  newPassword: string
}
