/**
 * Simple transaction wrapper for database operations
 */

import { getPool } from './db'

/**
 * Execute multiple database operations within a transaction
 */
export async function withTransaction<T>(
  operations: (client: any) => Promise<T>
): Promise<T> {
  const pool = getPool()
  const client = await pool.connect()
  
  try {
    await client.query('BEGIN')
    const result = await operations(client)
    await client.query('COMMIT')
    return result
  } catch (error) {
    await client.query('ROLLBACK')
    throw error
  } finally {
    client.release()
  }
}

/**
 * Transaction-safe paper creation with review initialization
 */
export async function createPaperWithReview(paperData: any, reviewData: any): Promise<any> {
  return withTransaction(async (client) => {
    // Create paper
    const paperResult = await client.query(`
      INSERT INTO papers (
        id, title, authors, venue, year, doi, url, abstract,
        citation_count, reference_count, publication_date, journal,
        volume, issue, pages, tags, starred, user_id, created_at, updated_at
      ) VALUES (
        $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19, $20
      ) RETURNING *
    `, [
      paperData.id, paperData.title, paperData.authors,
      paperData.venue, paperData.year, paperData.doi, paperData.url,
      paperData.abstract, paperData.citationCount, paperData.referenceCount,
      paperData.publicationDate, paperData.journal, paperData.volume,
      paperData.issue, paperData.pages, paperData.tags,
      paperData.starred, paperData.userId, paperData.createdAt, paperData.updatedAt
    ])
    
    const paper = paperResult.rows[0]
    
    // Create initial review
    await client.query(`
      INSERT INTO reviews (paper_id, ease, next_due, last_interval, created_at, updated_at)
      VALUES ($1, $2, $3, $4, $5, $6)
    `, [
      paper.id, reviewData.ease, reviewData.nextDue, reviewData.lastInterval,
      new Date().toISOString(), new Date().toISOString()
    ])
    
    return paper
  })
}

/**
 * Transaction-safe collection creation with papers
 */
export async function createCollectionWithPapers(collectionData: any, paperIds: string[]): Promise<any> {
  return withTransaction(async (client) => {
    // Create collection
    const collectionResult = await client.query(`
      INSERT INTO collections (id, name, user_id, created_at, updated_at)
      VALUES ($1, $2, $3, $4, $5) RETURNING *
    `, [
      collectionData.id, collectionData.name, collectionData.userId,
      collectionData.createdAt, collectionData.updatedAt
    ])
    
    const collection = collectionResult.rows[0]
    
    // Add papers to collection
    for (const paperId of paperIds) {
      await client.query(`
        INSERT INTO collection_papers (collection_id, paper_id, created_at)
        VALUES ($1, $2, $3)
        ON CONFLICT (collection_id, paper_id) DO NOTHING
      `, [collection.id, paperId, new Date().toISOString()])
    }
    
    return collection
  })
}

/**
 * Transaction-safe user deletion with cleanup
 */
export async function deleteUserWithCleanup(userId: string): Promise<void> {
  return withTransaction(async (client) => {
    // Delete user sessions
    await client.query('DELETE FROM user_sessions WHERE user_id = $1', [userId])
    
    // Delete email verification tokens
    await client.query('DELETE FROM email_verification_tokens WHERE user_id = $1', [userId])
    
    // Delete password reset tokens
    await client.query('DELETE FROM password_reset_tokens WHERE user_id = $1', [userId])
    
    // Delete notes for user's papers
    await client.query(`
      DELETE FROM notes WHERE paper_id IN (
        SELECT id FROM papers WHERE user_id = $1
      )
    `, [userId])
    
    // Delete reviews for user's papers
    await client.query(`
      DELETE FROM reviews WHERE paper_id IN (
        SELECT id FROM papers WHERE user_id = $1
      )
    `, [userId])
    
    // Delete collection papers for user's collections
    await client.query(`
      DELETE FROM collection_papers WHERE collection_id IN (
        SELECT id FROM collections WHERE user_id = $1
      )
    `, [userId])
    
    // Delete user's collections
    await client.query('DELETE FROM collections WHERE user_id = $1', [userId])
    
    // Delete user's papers
    await client.query('DELETE FROM papers WHERE user_id = $1', [userId])
    
    // Update audit logs to remove user reference
    await client.query('UPDATE audit_logs SET user_id = NULL WHERE user_id = $1', [userId])
    
    // Finally delete the user
    await client.query('DELETE FROM users WHERE id = $1', [userId])
  })
}

/**
 * Transaction-safe paper deletion with cleanup
 */
export async function deletePaperWithCleanup(paperId: string): Promise<void> {
  return withTransaction(async (client) => {
    // Delete notes
    await client.query('DELETE FROM notes WHERE paper_id = $1', [paperId])
    
    // Delete reviews
    await client.query('DELETE FROM reviews WHERE paper_id = $1', [paperId])
    
    // Remove from collections
    await client.query('DELETE FROM collection_papers WHERE paper_id = $1', [paperId])
    
    // Delete the paper
    await client.query('DELETE FROM papers WHERE id = $1', [paperId])
  })
}

/**
 * Transaction-safe collection deletion with cleanup
 */
export async function deleteCollectionWithCleanup(collectionId: string): Promise<void> {
  return withTransaction(async (client) => {
    // Remove all papers from collection
    await client.query('DELETE FROM collection_papers WHERE collection_id = $1', [collectionId])
    
    // Delete the collection
    await client.query('DELETE FROM collections WHERE id = $1', [collectionId])
  })
}

/**
 * Batch operations with transaction safety
 */
export async function batchUpdatePapers(updates: Array<{ id: string; data: any }>): Promise<void> {
  return withTransaction(async (client) => {
    for (const update of updates) {
      const { id, data } = update
      const fields = Object.keys(data)
      const values = Object.values(data)
      const setClause = fields.map((field, index) => `${field} = $${index + 2}`).join(', ')
      
      await client.query(
        `UPDATE papers SET ${setClause}, updated_at = $1 WHERE id = $${fields.length + 2}`,
        [new Date().toISOString(), ...values, id]
      )
    }
  })
}

/**
 * Database maintenance operations
 */
export async function performDatabaseMaintenance(): Promise<{
  cleaned: number
  errors: string[]
}> {
  let cleaned = 0
  const errors: string[] = []
  
  try {
    await withTransaction(async (client) => {
      // Clean up expired sessions
      const expiredSessions = await client.query(`
        DELETE FROM user_sessions WHERE expires_at < NOW()
      `)
      cleaned += expiredSessions.rowCount || 0
      
      // Clean up expired email verification tokens
      const expiredEmailTokens = await client.query(`
        DELETE FROM email_verification_tokens WHERE expires_at < NOW()
      `)
      cleaned += expiredEmailTokens.rowCount || 0
      
      // Clean up expired password reset tokens
      const expiredPasswordTokens = await client.query(`
        DELETE FROM password_reset_tokens WHERE expires_at < NOW()
      `)
      cleaned += expiredPasswordTokens.rowCount || 0
      
      // Clean up old audit logs (older than 1 year)
      const oldAuditLogs = await client.query(`
        DELETE FROM audit_logs WHERE created_at < NOW() - INTERVAL '1 year'
      `)
      cleaned += oldAuditLogs.rowCount || 0
    })
  } catch (error) {
    errors.push(`Database maintenance error: ${error.message}`)
  }
  
  return { cleaned, errors }
}

/**
 * Add database constraints and indexes
 */
export async function addDatabaseConstraints(): Promise<void> {
  const constraints = [
    // Users table
    'CREATE UNIQUE INDEX IF NOT EXISTS idx_users_email ON users(email)',
    'CREATE INDEX IF NOT EXISTS idx_users_created_at ON users(created_at)',
    'CREATE INDEX IF NOT EXISTS idx_users_last_login ON users(last_login)',
    'CREATE INDEX IF NOT EXISTS idx_users_role ON users(role)',
    
    // Papers table
    'CREATE INDEX IF NOT EXISTS idx_papers_user_id ON papers(user_id)',
    'CREATE INDEX IF NOT EXISTS idx_papers_created_at ON papers(created_at)',
    'CREATE INDEX IF NOT EXISTS idx_papers_year ON papers(year)',
    'CREATE INDEX IF NOT EXISTS idx_papers_starred ON papers(starred)',
    'CREATE INDEX IF NOT EXISTS idx_papers_doi ON papers(doi)',
    'CREATE INDEX IF NOT EXISTS idx_papers_title ON papers(title)',
    
    // Collections table
    'CREATE INDEX IF NOT EXISTS idx_collections_user_id ON collections(user_id)',
    'CREATE INDEX IF NOT EXISTS idx_collections_created_at ON collections(created_at)',
    'CREATE INDEX IF NOT EXISTS idx_collections_name ON collections(name)',
    
    // Collection papers table
    'CREATE INDEX IF NOT EXISTS idx_collection_papers_collection_id ON collection_papers(collection_id)',
    'CREATE INDEX IF NOT EXISTS idx_collection_papers_paper_id ON collection_papers(paper_id)',
    'CREATE UNIQUE INDEX IF NOT EXISTS idx_collection_papers_unique ON collection_papers(collection_id, paper_id)',
    
    // Reviews table
    'CREATE INDEX IF NOT EXISTS idx_reviews_paper_id ON reviews(paper_id)',
    'CREATE INDEX IF NOT EXISTS idx_reviews_next_due ON reviews(next_due)',
    'CREATE INDEX IF NOT EXISTS idx_reviews_created_at ON reviews(created_at)',
    'CREATE UNIQUE INDEX IF NOT EXISTS idx_reviews_paper_unique ON reviews(paper_id)',
    
    // Notes table
    'CREATE INDEX IF NOT EXISTS idx_notes_paper_id ON notes(paper_id)',
    'CREATE INDEX IF NOT EXISTS idx_notes_created_at ON notes(created_at)',
    'CREATE UNIQUE INDEX IF NOT EXISTS idx_notes_paper_unique ON notes(paper_id)',
    
    // User sessions table
    'CREATE INDEX IF NOT EXISTS idx_user_sessions_user_id ON user_sessions(user_id)',
    'CREATE INDEX IF NOT EXISTS idx_user_sessions_token_hash ON user_sessions(token_hash)',
    'CREATE INDEX IF NOT EXISTS idx_user_sessions_expires_at ON user_sessions(expires_at)',
    
    // Audit logs table
    'CREATE INDEX IF NOT EXISTS idx_audit_logs_user_id ON audit_logs(user_id)',
    'CREATE INDEX IF NOT EXISTS idx_audit_logs_action ON audit_logs(action)',
    'CREATE INDEX IF NOT EXISTS idx_audit_logs_created_at ON audit_logs(created_at)'
  ]
  
  const { query } = await import('./db')
  
  for (const constraint of constraints) {
    try {
      await query(constraint)
    } catch (error) {
      console.warn(`Failed to add constraint: ${constraint}`, error)
    }
  }
}
