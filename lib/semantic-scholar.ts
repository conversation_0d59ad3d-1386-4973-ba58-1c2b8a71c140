/**
 * Semantic Scholar API Integration
 * Fetches paper metadata including citations, references, and DOI
 */

interface SemanticScholarPaper {
  paperId: string
  title: string
  abstract?: string
  venue?: string
  year?: number
  referenceCount: number
  citationCount: number
  influentialCitationCount: number
  isOpenAccess: boolean
  openAccessPdf?: {
    url: string
    status: string
  }
  fieldsOfStudy?: string[]
  s2FieldsOfStudy?: Array<{
    category: string
    source: string
  }>
  publicationTypes?: string[]
  publicationDate?: string
  journal?: {
    name: string
    pages?: string
    volume?: string
  }
  authors: Array<{
    authorId: string
    name: string
  }>
  externalIds?: {
    DOI?: string
    ArXiv?: string
    MAG?: string
    DBLP?: string
    PubMed?: string
    CorpusId?: string
  }
  url?: string
  tldr?: {
    model: string
    text: string
  }
}

interface SemanticScholarSearchResult {
  total: number
  offset: number
  next?: number
  data: SemanticScholarPaper[]
}

const SEMANTIC_SCHOLAR_BASE_URL = 'https://api.semanticscholar.org/graph/v1'

// Fields to request from Semantic Scholar API
const PAPER_FIELDS = [
  'paperId',
  'title',
  'abstract',
  'venue',
  'year',
  'referenceCount',
  'citationCount',
  'influentialCitationCount',
  'isOpenAccess',
  'openAccessPdf',
  'fieldsOfStudy',
  's2FieldsOfStudy',
  'publicationTypes',
  'publicationDate',
  'journal',
  'authors',
  'externalIds',
  'url',
  'tldr'
].join(',')

/**
 * Search for papers by title
 */
export async function searchPapersByTitle(title: string, limit: number = 5): Promise<SemanticScholarPaper[]> {
  try {
    const encodedTitle = encodeURIComponent(title)
    const url = `${SEMANTIC_SCHOLAR_BASE_URL}/paper/search?query=${encodedTitle}&limit=${limit}&fields=${PAPER_FIELDS}`
    
    console.log('Searching Semantic Scholar for:', title)
    
    const response = await fetch(url, {
      headers: {
        'User-Agent': 'PaperNugget/1.0 (https://papernugget.com)'
      }
    })
    
    if (!response.ok) {
      console.error('Semantic Scholar API error:', response.status, response.statusText)
      return []
    }
    
    const data: SemanticScholarSearchResult = await response.json()
    console.log(`Found ${data.data.length} papers for "${title}"`)
    
    return data.data
  } catch (error) {
    console.error('Error searching Semantic Scholar:', error)
    return []
  }
}

/**
 * Get paper details by DOI
 */
export async function getPaperByDOI(doi: string): Promise<SemanticScholarPaper | null> {
  try {
    const url = `${SEMANTIC_SCHOLAR_BASE_URL}/paper/DOI:${doi}?fields=${PAPER_FIELDS}`
    
    console.log('Fetching paper by DOI:', doi)
    
    const response = await fetch(url, {
      headers: {
        'User-Agent': 'PaperNugget/1.0 (https://papernugget.com)'
      }
    })
    
    if (!response.ok) {
      if (response.status === 404) {
        console.log('Paper not found in Semantic Scholar for DOI:', doi)
        return null
      }
      console.error('Semantic Scholar API error:', response.status, response.statusText)
      return null
    }
    
    const paper: SemanticScholarPaper = await response.json()
    console.log('Found paper by DOI:', paper.title)
    
    return paper
  } catch (error) {
    console.error('Error fetching paper by DOI:', error)
    return null
  }
}

/**
 * Get paper details by Semantic Scholar paper ID
 */
export async function getPaperById(paperId: string): Promise<SemanticScholarPaper | null> {
  try {
    const url = `${SEMANTIC_SCHOLAR_BASE_URL}/paper/${paperId}?fields=${PAPER_FIELDS}`
    
    const response = await fetch(url, {
      headers: {
        'User-Agent': 'PaperNugget/1.0 (https://papernugget.com)'
      }
    })
    
    if (!response.ok) {
      if (response.status === 404) {
        return null
      }
      console.error('Semantic Scholar API error:', response.status, response.statusText)
      return null
    }
    
    return await response.json()
  } catch (error) {
    console.error('Error fetching paper by ID:', error)
    return null
  }
}

/**
 * Find the best matching paper for a given title and authors
 */
export async function findBestMatch(title: string, authors?: string[]): Promise<SemanticScholarPaper | null> {
  const searchResults = await searchPapersByTitle(title)
  
  if (searchResults.length === 0) {
    return null
  }
  
  // If no authors provided, return the first result
  if (!authors || authors.length === 0) {
    return searchResults[0]
  }
  
  // Find the best match based on title similarity and author overlap
  let bestMatch = searchResults[0]
  let bestScore = 0
  
  for (const paper of searchResults) {
    let score = 0
    
    // Title similarity (simple check)
    if (paper.title.toLowerCase().includes(title.toLowerCase()) || 
        title.toLowerCase().includes(paper.title.toLowerCase())) {
      score += 2
    }
    
    // Author overlap
    const paperAuthors = paper.authors.map(a => a.name.toLowerCase())
    const inputAuthors = authors.map(a => a.toLowerCase())
    
    for (const inputAuthor of inputAuthors) {
      for (const paperAuthor of paperAuthors) {
        if (paperAuthor.includes(inputAuthor) || inputAuthor.includes(paperAuthor)) {
          score += 1
          break
        }
      }
    }
    
    if (score > bestScore) {
      bestScore = score
      bestMatch = paper
    }
  }
  
  return bestScore > 0 ? bestMatch : searchResults[0]
}

/**
 * Convert Semantic Scholar paper to our Paper format
 */
export function convertSemanticScholarPaper(s2Paper: SemanticScholarPaper) {
  return {
    title: s2Paper.title,
    authors: s2Paper.authors.map(a => a.name),
    venue: s2Paper.venue,
    year: s2Paper.year,
    doi: s2Paper.externalIds?.DOI,
    url: s2Paper.url || (s2Paper.openAccessPdf?.url),
    abstract: s2Paper.abstract,
    citationCount: s2Paper.citationCount,
    referenceCount: s2Paper.referenceCount,
    publicationDate: s2Paper.publicationDate,
    journal: s2Paper.journal?.name,
    volume: s2Paper.journal?.volume,
    pages: s2Paper.journal?.pages,
  }
}
