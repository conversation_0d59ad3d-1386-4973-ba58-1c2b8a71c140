import { NextRequest } from 'next/server'
import { createErrorResponse } from './validation'
import { logSecurityEvent } from './logging'

/**
 * Enhanced rate limiting utility for API endpoints
 * In production, this should use Redis or a database for persistence
 */

interface RateLimitRecord {
  count: number
  resetTime: number
  firstRequest: number
}

interface RateLimitConfig {
  maxRequests: number
  windowMs: number
  keyGenerator?: (request: NextRequest) => string
  skipSuccessfulRequests?: boolean
  skipFailedRequests?: boolean
  message?: string
}

interface RateLimitResult {
  allowed: boolean
  remaining: number
  resetTime: number
  totalHits: number
}

// In-memory store (use Redis in production)
const rateLimitStore = new Map<string, RateLimitRecord>()

// Cleanup expired entries periodically
setInterval(() => {
  const now = Date.now()
  for (const [key, record] of rateLimitStore.entries()) {
    if (now > record.resetTime) {
      rateLimitStore.delete(key)
    }
  }
}, 60000) // Cleanup every minute

export class RateLimiter {
  private config: RateLimitConfig

  constructor(config: RateLimitConfig) {
    this.config = config
  }

  check(key: string): { allowed: boolean; remaining: number; resetTime: number } {
    const now = Date.now()
    const record = rateLimitStore.get(key)

    if (!record || now > record.resetTime) {
      // Create new record
      const newRecord: RateLimitRecord = {
        count: 1,
        resetTime: now + this.config.windowMs
      }
      rateLimitStore.set(key, newRecord)
      
      return {
        allowed: true,
        remaining: this.config.maxRequests - 1,
        resetTime: newRecord.resetTime
      }
    }

    if (record.count >= this.config.maxRequests) {
      return {
        allowed: false,
        remaining: 0,
        resetTime: record.resetTime
      }
    }

    record.count++
    return {
      allowed: true,
      remaining: this.config.maxRequests - record.count,
      resetTime: record.resetTime
    }
  }

  reset(key: string): void {
    rateLimitStore.delete(key)
  }
}

// Pre-configured rate limiters for different operations
export const registrationLimiter = new RateLimiter({
  maxRequests: 5,
  windowMs: 60 * 60 * 1000, // 1 hour
})

export const verificationResendLimiter = new RateLimiter({
  maxRequests: 1,
  windowMs: 60 * 1000, // 1 minute
})

export const verificationResendDailyLimiter = new RateLimiter({
  maxRequests: 5,
  windowMs: 24 * 60 * 60 * 1000, // 24 hours
})

export const loginLimiter = new RateLimiter({
  maxRequests: 10,
  windowMs: 15 * 60 * 1000, // 15 minutes
})

// Helper function to generate rate limit keys
export function generateRateLimitKey(prefix: string, identifier: string, timeWindow?: number): string {
  const timestamp = timeWindow ? Math.floor(Date.now() / timeWindow) : ''
  return `${prefix}:${identifier}${timestamp ? `:${timestamp}` : ''}`
}

// Helper function to get client identifier for rate limiting
export function getClientIdentifier(request: Request): string {
  // Try to get real IP from headers (for reverse proxy setups)
  const forwardedFor = request.headers.get('x-forwarded-for')
  const realIp = request.headers.get('x-real-ip')
  const remoteAddr = request.headers.get('remote-addr')
  
  // Use the first IP from x-forwarded-for if available
  if (forwardedFor) {
    return forwardedFor.split(',')[0].trim()
  }
  
  return realIp || remoteAddr || 'unknown'
}

// Middleware-style rate limiter
export function withRateLimit(
  limiter: RateLimiter,
  keyGenerator: (request: Request) => string,
  errorMessage?: string
) {
  return function(handler: (request: Request, ...args: any[]) => Promise<Response>) {
    return async function(request: Request, ...args: any[]): Promise<Response> {
      const key = keyGenerator(request)
      const result = limiter.check(key)
      
      if (!result.allowed) {
        const resetDate = new Date(result.resetTime)
        const isValidDate = !isNaN(resetDate.getTime())

        return new Response(
          JSON.stringify({
            error: errorMessage || 'Too many requests. Please try again later.',
            retryAfter: Math.ceil((result.resetTime - Date.now()) / 1000),
            ...(isValidDate && { resetTime: resetDate.toISOString() })
          }),
          {
            status: 429,
            headers: {
              'Content-Type': 'application/json',
              ...(typeof result.resetTime === 'number' && !isNaN(result.resetTime) && {
                'Retry-After': Math.ceil((result.resetTime - Date.now()) / 1000).toString(),
                'X-RateLimit-Reset': result.resetTime.toString(),
              }),
              'X-RateLimit-Limit': limiter.config.maxRequests.toString(),
              ...(typeof result.remaining === 'number' && !isNaN(result.remaining) && {
                'X-RateLimit-Remaining': result.remaining.toString(),
              }),
            }
          }
        )
      }

      // Add rate limit headers to successful responses
      const response = await handler(request, ...args)
      
      // Clone response to add headers
      const newResponse = new Response(response.body, {
        status: response.status,
        statusText: response.statusText,
        headers: new Headers(response.headers)
      })
      
      newResponse.headers.set('X-RateLimit-Limit', limiter.config.maxRequests.toString())
      if (typeof result.remaining === 'number' && !isNaN(result.remaining)) {
        newResponse.headers.set('X-RateLimit-Remaining', result.remaining.toString())
      }
      if (typeof result.resetTime === 'number' && !isNaN(result.resetTime)) {
        newResponse.headers.set('X-RateLimit-Reset', result.resetTime.toString())
      }
      
      return newResponse
    }
  }
}

// Security logging function
export function logSecurityEvent(
  event: string,
  details: {
    ip?: string
    userAgent?: string
    userId?: string
    email?: string
    [key: string]: any
  }
): void {
  const logEntry = {
    timestamp: new Date().toISOString(),
    event,
    ip: details.ip,
    userAgent: details.userAgent,
    userId: details.userId,
    email: details.email,
    details: { ...details, ip: undefined, userAgent: undefined, userId: undefined, email: undefined }
  }
  
  // Remove undefined values
  Object.keys(logEntry).forEach(key => {
    if (logEntry[key as keyof typeof logEntry] === undefined) {
      delete logEntry[key as keyof typeof logEntry]
    }
  })
  
  console.log('🔒 Security Event:', JSON.stringify(logEntry))
}
