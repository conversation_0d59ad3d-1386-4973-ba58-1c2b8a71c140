import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import { v4 as uuidv4 } from 'uuid'

// Correlation ID for request tracking
export function generateCorrelationId(): string {
  return uuidv4()
}

// Standard API error response shape
export interface ApiError {
  error: string
  message?: string
  details?: any
  correlationId: string
  timestamp: string
  path?: string
}

// Standard API success response envelope
export interface ApiResponse<T = any> {
  data: T
  correlationId: string
  timestamp: string
  meta?: {
    pagination?: {
      page: number
      limit: number
      total: number
      totalPages: number
    }
    sorting?: {
      field: string
      order: 'asc' | 'desc'
    }
  }
}

// Create standardized error response
export function createErrorResponse(
  error: string,
  status: number,
  correlationId: string,
  details?: any,
  message?: string,
  path?: string
): NextResponse {
  const errorResponse: ApiError = {
    error,
    message,
    details,
    correlationId,
    timestamp: new Date().toISOString(),
    path
  }

  return NextResponse.json(errorResponse, { 
    status,
    headers: {
      'X-Correlation-ID': correlationId
    }
  })
}

// Create standardized success response
export function createSuccessResponse<T>(
  data: T,
  correlationId: string,
  meta?: ApiResponse<T>['meta']
): NextResponse {
  const response: ApiResponse<T> = {
    data,
    correlationId,
    timestamp: new Date().toISOString(),
    meta
  }

  return NextResponse.json(response, {
    headers: {
      'X-Correlation-ID': correlationId
    }
  })
}

// Create standardized list response with pagination
export function createListResponse<T>(
  data: T[],
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
  },
  total?: number,
  sorting?: {
    sortBy: string
    sortOrder: 'asc' | 'desc'
  }
) {
  return {
    data: {
      data,
      pagination,
      sorting
    },
    pagination,
    sorting
  }
}

// Validation middleware options
export interface ValidationOptions {
  body?: z.ZodSchema
  query?: z.ZodSchema
  params?: z.ZodSchema
  maxBodySize?: number // in bytes
}

// Request validation middleware
export function withValidation(
  handler: (
    request: NextRequest,
    context: {
      correlationId: string
      validatedData: {
        body?: any
        query?: any
        params?: any
      }
    },
    routeParams?: any
  ) => Promise<NextResponse>,
  options: ValidationOptions = {}
) {
  return async (request: NextRequest, routeParams?: any) => {
    const correlationId = generateCorrelationId()
    const url = new URL(request.url)
    
    try {
      const validatedData: any = {}

      // Validate request body size
      if (options.maxBodySize) {
        const contentLength = request.headers.get('content-length')
        if (contentLength && parseInt(contentLength) > options.maxBodySize) {
          return createErrorResponse(
            'Request body too large',
            413,
            correlationId,
            { maxSize: options.maxBodySize, receivedSize: parseInt(contentLength) },
            `Request body must be smaller than ${options.maxBodySize} bytes`,
            url.pathname
          )
        }
      }

      // Validate request body
      if (options.body && ['POST', 'PUT', 'PATCH'].includes(request.method)) {
        try {
          const body = await request.json()
          validatedData.body = options.body.parse(body)
        } catch (error) {
          if (error instanceof z.ZodError) {
            return createErrorResponse(
              'Validation failed',
              400,
              correlationId,
              {
                field: 'body',
                issues: error.issues.map(issue => ({
                  path: issue.path.join('.'),
                  message: issue.message,
                  code: issue.code
                }))
              },
              'Request body validation failed',
              url.pathname
            )
          }

          return createErrorResponse(
            'Invalid JSON',
            400,
            correlationId,
            undefined,
            'Request body must be valid JSON',
            url.pathname
          )
        }
      }

      // Validate query parameters
      if (options.query) {
        try {
          const query = Object.fromEntries(url.searchParams.entries())
          validatedData.query = options.query.parse(query)
        } catch (error) {
          if (error instanceof z.ZodError) {
            return createErrorResponse(
              'Validation failed',
              400,
              correlationId,
              {
                field: 'query',
                issues: error.issues.map(issue => ({
                  path: issue.path.join('.'),
                  message: issue.message,
                  code: issue.code
                }))
              },
              'Query parameter validation failed',
              url.pathname
            )
          }
        }
      }

      // Validate route parameters
      if (options.params && routeParams?.params) {
        try {
          // In Next.js 15, params is a Promise that needs to be awaited
          const params = await routeParams.params
          validatedData.params = options.params.parse(params)
        } catch (error) {
          if (error instanceof z.ZodError) {
            return createErrorResponse(
              'Validation failed',
              400,
              correlationId,
              {
                field: 'params',
                issues: error.issues.map(issue => ({
                  path: issue.path.join('.'),
                  message: issue.message,
                  code: issue.code
                }))
              },
              'Route parameter validation failed',
              url.pathname
            )
          }
        }
      }

      // Call the handler with validated data
      return await handler(request, { correlationId, validatedData }, routeParams)

    } catch (error) {
      console.error('Validation middleware error:', error)
      return createErrorResponse(
        'Internal server error',
        500,
        correlationId,
        undefined,
        'An unexpected error occurred',
        url.pathname
      )
    }
  }
}

// Common validation schemas
export const commonSchemas = {
  // UUID validation
  uuid: z.string().uuid('Invalid UUID format'),
  
  // Email validation
  email: z.string().email('Invalid email format').toLowerCase(),
  
  // Password validation
  password: z.string()
    .min(8, 'Password must be at least 8 characters')
    .regex(/[a-z]/, 'Password must contain at least one lowercase letter')
    .regex(/[A-Z]/, 'Password must contain at least one uppercase letter')
    .regex(/\d/, 'Password must contain at least one number')
    .regex(/[!@#$%^&*(),.?":{}|<>]/, 'Password must contain at least one special character'),
  
  // Pagination
  pagination: z.object({
    page: z.string().optional().transform(val => val ? parseInt(val) : 1).pipe(z.number().min(1)),
    limit: z.string().optional().transform(val => val ? parseInt(val) : 20).pipe(z.number().min(1).max(100))
  }),
  
  // Sorting
  sorting: z.object({
    sortBy: z.string().optional(),
    sortOrder: z.enum(['asc', 'desc']).default('asc')
  }),
  
  // Common query parameters
  listQuery: z.object({
    page: z.string().optional(),
    limit: z.string().optional(),
    sortBy: z.string().optional(),
    sortOrder: z.enum(['asc', 'desc']).optional(),
    search: z.string().optional()
  })
}

// Combine validation with authentication
export function withValidatedAuth(
  handler: (
    request: NextRequest,
    context: {
      correlationId: string
      validatedData: any
      user: any
      userId: string
    },
    routeParams?: any
  ) => Promise<NextResponse>,
  validationOptions: ValidationOptions = {},
  authOptions: {
    requiredRoles?: string[]
    allowUnverified?: boolean
  } = {}
) {
  return withValidation(async (request, validationContext, routeParams) => {
    // Import auth functions here to avoid circular dependency
    const { authenticateRequest, hasRole } = await import('./auth-middleware')

    const auth = await authenticateRequest(request)
    const url = new URL(request.url)

    if (!auth.authenticated || !auth.user || !auth.userId) {
      return createErrorResponse(
        'Authentication required',
        401,
        validationContext.correlationId,
        undefined,
        auth.error || 'Valid authentication token required',
        url.pathname
      )
    }

    // Check email verification if required
    if (!authOptions.allowUnverified && !auth.user.emailVerified) {
      return createErrorResponse(
        'Email verification required',
        403,
        validationContext.correlationId,
        undefined,
        'Please verify your email address before accessing this resource',
        url.pathname
      )
    }

    // Check role requirements
    if (authOptions.requiredRoles && !hasRole(auth.user.role, authOptions.requiredRoles)) {
      return createErrorResponse(
        'Insufficient permissions',
        403,
        validationContext.correlationId,
        { requiredRoles: authOptions.requiredRoles, userRole: auth.user.role },
        'You do not have the required permissions to access this resource',
        url.pathname
      )
    }

    // Call the handler with both validation and auth context
    return handler(request, {
      ...validationContext,
      user: auth.user,
      userId: auth.userId
    }, routeParams)
  }, validationOptions)
}
