/**
 * SQL Security utilities to prevent injection attacks
 */

/**
 * Escape SQL identifiers (table names, column names)
 */
export function escapeIdentifier(identifier: string): string {
  // Remove any non-alphanumeric characters except underscores
  const cleaned = identifier.replace(/[^a-zA-Z0-9_]/g, '')
  
  // Ensure it doesn't start with a number
  if (/^\d/.test(cleaned)) {
    throw new Error('SQL identifier cannot start with a number')
  }
  
  // Ensure it's not empty
  if (!cleaned) {
    throw new Error('SQL identifier cannot be empty')
  }
  
  return cleaned
}

/**
 * Validate and sanitize ORDER BY clauses
 */
export function sanitizeOrderBy(
  field: string, 
  direction: 'ASC' | 'DESC' | 'asc' | 'desc',
  allowedFields: string[]
): { field: string; direction: 'ASC' | 'DESC' } {
  // Validate field is in allowed list
  if (!allowedFields.includes(field)) {
    throw new Error(`Invalid sort field: ${field}`)
  }
  
  // Escape the field name
  const escapedField = escapeIdentifier(field)
  
  // Validate direction
  const upperDirection = direction.toUpperCase()
  if (!['ASC', 'DESC'].includes(upperDirection)) {
    throw new Error(`Invalid sort direction: ${direction}`)
  }
  
  return {
    field: escapedField,
    direction: upperDirection as 'ASC' | 'DESC'
  }
}

/**
 * Validate LIMIT and OFFSET values
 */
export function sanitizePagination(limit: number, offset: number): { limit: number; offset: number } {
  // Validate limit
  if (!Number.isInteger(limit) || limit < 1 || limit > 1000) {
    throw new Error('Invalid limit value')
  }
  
  // Validate offset
  if (!Number.isInteger(offset) || offset < 0) {
    throw new Error('Invalid offset value')
  }
  
  return { limit, offset }
}

/**
 * Parameterized query builder for common operations
 */
export class QueryBuilder {
  private query: string = ''
  private params: any[] = []
  private paramIndex: number = 1
  
  select(fields: string[]): this {
    const escapedFields = fields.map(field => escapeIdentifier(field)).join(', ')
    this.query += `SELECT ${escapedFields}`
    return this
  }
  
  from(table: string): this {
    const escapedTable = escapeIdentifier(table)
    this.query += ` FROM ${escapedTable}`
    return this
  }
  
  where(condition: string, value?: any): this {
    if (this.query.includes('WHERE')) {
      this.query += ` AND ${condition}`
    } else {
      this.query += ` WHERE ${condition}`
    }
    
    if (value !== undefined) {
      this.params.push(value)
    }
    
    return this
  }
  
  orderBy(field: string, direction: 'ASC' | 'DESC', allowedFields: string[]): this {
    const sanitized = sanitizeOrderBy(field, direction, allowedFields)
    this.query += ` ORDER BY ${sanitized.field} ${sanitized.direction}`
    return this
  }
  
  limit(limit: number, offset: number = 0): this {
    const sanitized = sanitizePagination(limit, offset)
    this.query += ` LIMIT ${sanitized.limit} OFFSET ${sanitized.offset}`
    return this
  }
  
  build(): { query: string; params: any[] } {
    return {
      query: this.query,
      params: this.params
    }
  }
}

/**
 * Input sanitization utilities
 */
export class InputSanitizer {
  /**
   * Sanitize string input to prevent XSS
   */
  static sanitizeString(input: string, maxLength: number = 1000): string {
    if (typeof input !== 'string') {
      throw new Error('Input must be a string')
    }
    
    // Trim whitespace
    let sanitized = input.trim()
    
    // Limit length
    if (sanitized.length > maxLength) {
      sanitized = sanitized.substring(0, maxLength)
    }
    
    // Remove null bytes
    sanitized = sanitized.replace(/\0/g, '')
    
    // Basic HTML entity encoding for dangerous characters
    sanitized = sanitized
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#x27;')
      .replace(/\//g, '&#x2F;')
    
    return sanitized
  }
  
  /**
   * Sanitize email input
   */
  static sanitizeEmail(email: string): string {
    const sanitized = this.sanitizeString(email, 254).toLowerCase()
    
    // Basic email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(sanitized)) {
      throw new Error('Invalid email format')
    }
    
    return sanitized
  }
  
  /**
   * Sanitize numeric input
   */
  static sanitizeNumber(input: any, min?: number, max?: number): number {
    const num = Number(input)
    
    if (!Number.isFinite(num)) {
      throw new Error('Invalid number')
    }
    
    if (min !== undefined && num < min) {
      throw new Error(`Number must be at least ${min}`)
    }
    
    if (max !== undefined && num > max) {
      throw new Error(`Number must be at most ${max}`)
    }
    
    return num
  }
  
  /**
   * Sanitize boolean input
   */
  static sanitizeBoolean(input: any): boolean {
    if (typeof input === 'boolean') {
      return input
    }
    
    if (typeof input === 'string') {
      const lower = input.toLowerCase()
      if (lower === 'true' || lower === '1') return true
      if (lower === 'false' || lower === '0') return false
    }
    
    if (typeof input === 'number') {
      return input !== 0
    }
    
    throw new Error('Invalid boolean value')
  }
  
  /**
   * Sanitize array input
   */
  static sanitizeArray(input: any, maxLength: number = 100): any[] {
    if (!Array.isArray(input)) {
      throw new Error('Input must be an array')
    }
    
    if (input.length > maxLength) {
      throw new Error(`Array too long (max ${maxLength} items)`)
    }
    
    return input
  }
}

/**
 * Security validation utilities
 */
export class SecurityValidator {
  /**
   * Check for common SQL injection patterns
   */
  static checkSQLInjection(input: string): boolean {
    const sqlPatterns = [
      /(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION|SCRIPT)\b)/i,
      /(--|\/\*|\*\/|;|'|"|`)/,
      /(\bOR\b|\bAND\b).*[=<>]/i,
      /\b(WAITFOR|DELAY)\b/i,
      /\b(XP_|SP_)/i
    ]
    
    return sqlPatterns.some(pattern => pattern.test(input))
  }
  
  /**
   * Check for XSS patterns
   */
  static checkXSS(input: string): boolean {
    const xssPatterns = [
      /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
      /<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi,
      /javascript:/i,
      /on\w+\s*=/i,
      /<img[^>]+src[^>]*>/gi
    ]
    
    return xssPatterns.some(pattern => pattern.test(input))
  }
  
  /**
   * Check for path traversal attempts
   */
  static checkPathTraversal(input: string): boolean {
    const pathPatterns = [
      /\.\./,
      /\/\.\./,
      /\.\.\\/,
      /\.\.%2f/i,
      /\.\.%5c/i,
      /%2e%2e/i
    ]
    
    return pathPatterns.some(pattern => pattern.test(input))
  }
  
  /**
   * Comprehensive security check
   */
  static validateInput(input: string): { safe: boolean; threats: string[] } {
    const threats: string[] = []
    
    if (this.checkSQLInjection(input)) {
      threats.push('SQL Injection')
    }
    
    if (this.checkXSS(input)) {
      threats.push('XSS')
    }
    
    if (this.checkPathTraversal(input)) {
      threats.push('Path Traversal')
    }
    
    return {
      safe: threats.length === 0,
      threats
    }
  }
}
