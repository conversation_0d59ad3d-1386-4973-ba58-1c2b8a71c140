import { z } from 'zod'
import { PaperType } from './types'

// Paper type validation
const paperTypeSchema = z.enum([
  'journalArticle',
  'conferencePaper',
  'book',
  'bookSection',
  'thesis',
  'report',
  'preprint',
  'manuscript',
  'webpage',
  'blogPost',
  'presentation',
  'patent',
  'dataset',
  'software',
  'artwork',
  'audioRecording',
  'videoRecording',
  'interview',
  'letter',
  'email',
  'forumPost',
  'encyclopediaArticle',
  'dictionaryEntry',
  'magazineArticle',
  'newspaperArticle',
  'radioBroadcast',
  'tvBroadcast',
  'podcast',
  'case',
  'statute',
  'bill',
  'hearing',
  'standard',
  'map',
  'film',
  'document'
] as const)
import { commonSchemas } from './validation'

// Auth schemas
export const authSchemas = {
  register: z.object({
    email: commonSchemas.email,
    password: commonSchemas.password,
    displayName: z.string().min(1, 'Display name is required').max(100, 'Display name too long').optional()
  }),

  login: z.object({
    email: commonSchemas.email,
    password: z.string().min(1, 'Password is required')
  }),

  changePassword: z.object({
    currentPassword: z.string().min(1, 'Current password is required'),
    newPassword: commonSchemas.password
  }),

  forgotPassword: z.object({
    email: commonSchemas.email
  }),

  resetPassword: z.object({
    token: z.string().min(1, 'Reset token is required'),
    password: commonSchemas.password
  }),

  resendVerification: z.object({
    email: commonSchemas.email
  }),

  verifyEmail: z.object({
    token: z.string().min(1, 'Verification token is required')
  })
}

// Paper schemas
export const paperSchemas = {
  create: z.object({
    title: z.string().min(1, 'Title is required').max(500, 'Title too long'),
    authors: z.array(z.string()).default([]),
    venue: z.preprocess(val => val === '' ? undefined : val, z.string().max(200, 'Venue too long').optional()),
    year: z.number().int().min(1900).max(new Date().getFullYear() + 5).optional(),
    doi: z.preprocess(val => val === '' ? undefined : val, z.string().max(100, 'DOI too long').optional()),
    url: z.preprocess(val => val === '' ? undefined : val, z.string().url('Invalid URL').optional()),
    abstract: z.preprocess(val => val === '' ? undefined : val, z.string().max(5000, 'Abstract too long').optional()),
    citationCount: z.number().int().min(0).optional(),
    referenceCount: z.number().int().min(0).optional(),
    publicationDate: z.preprocess(val => val === '' ? undefined : val, z.string().optional()),
    journal: z.preprocess(val => val === '' ? undefined : val, z.string().max(200, 'Journal name too long').optional()),
    volume: z.preprocess(val => val === '' ? undefined : val, z.string().max(50, 'Volume too long').optional()),
    issue: z.preprocess(val => val === '' ? undefined : val, z.string().max(50, 'Issue too long').optional()),
    pages: z.preprocess(val => val === '' ? undefined : val, z.string().max(50, 'Pages too long').optional()),
    tags: z.array(z.string().max(50, 'Tag too long')).default([]),
    paperType: paperTypeSchema.default('journalArticle')
  }),

  update: z.object({
    title: z.string().min(1, 'Title is required').max(500, 'Title too long').optional(),
    authors: z.array(z.string()).optional(),
    venue: z.string().max(200, 'Venue too long').optional(),
    year: z.number().int().min(1900).max(new Date().getFullYear() + 5).optional(),
    doi: z.string().max(100, 'DOI too long').optional(),
    url: z.string().url('Invalid URL').optional(),
    abstract: z.string().max(5000, 'Abstract too long').optional(),
    citationCount: z.number().int().min(0).optional(),
    referenceCount: z.number().int().min(0).optional(),
    publicationDate: z.string().datetime().optional(),
    journal: z.string().max(200, 'Journal name too long').optional(),
    volume: z.string().max(50, 'Volume too long').optional(),
    issue: z.string().max(50, 'Issue too long').optional(),
    pages: z.string().max(50, 'Pages too long').optional(),
    tags: z.array(z.string().max(50, 'Tag too long')).optional(),
    starred: z.boolean().optional(),
    paperType: paperTypeSchema.optional()
  }),

  list: commonSchemas.listQuery.extend({
    starred: z.string().transform(val => val === 'true').optional(),
    tag: z.string().optional(),
    year: z.string().transform(val => parseInt(val)).pipe(z.number().int()).optional()
  }),

  params: z.object({
    id: commonSchemas.uuid
  }),

  doiParams: z.object({
    doi: z.string().min(1, 'DOI is required')
  }),

  enrich: z.object({
    doi: z.string().min(1, 'DOI is required').optional(),
    title: z.string().min(1, 'Title is required').optional(),
    authors: z.array(z.string()).optional(),
    collectionId: z.string().uuid('Invalid collection ID').optional()
  }).refine(data => data.doi || data.title, {
    message: 'Either DOI or title must be provided'
  }),

  passwordResetConfirm: z.object({
    token: z.string().min(1, 'Reset token is required'),
    newPassword: z.string().min(8, 'Password must be at least 8 characters long'),
  })
}

// Collection schemas
export const collectionSchemas = {
  create: z.object({
    name: z.string().min(1, 'Collection name is required').max(100, 'Collection name too long'),
    zoteroLibraryType: z.enum(['user', 'group']).optional(),
    zoteroLibraryId: z.string().optional()
  }),

  update: z.object({
    name: z.string().min(1, 'Collection name is required').max(100, 'Collection name too long').optional(),
    zoteroLibraryType: z.enum(['user', 'group']).optional(),
    zoteroLibraryId: z.string().optional()
  }),

  list: commonSchemas.listQuery,

  params: z.object({
    id: commonSchemas.uuid
  }),

  addPapers: z.object({
    paperIds: z.array(commonSchemas.uuid).min(1, 'At least one paper ID is required')
  }),

  paperParams: z.object({
    id: commonSchemas.uuid,
    paperId: commonSchemas.uuid
  })
}

// User profile schemas
export const userSchemas = {
  updateProfile: z.object({
    displayName: z.string().min(1, 'Display name is required').max(100, 'Display name too long').optional(),
    preferences: z.record(z.any()).optional(),
    privacySettings: z.record(z.any()).optional()
  }),

  adminUserParams: z.object({
    id: commonSchemas.uuid
  }),

  adminUserList: commonSchemas.listQuery.extend({
    role: z.enum(['admin', 'user', 'readonly']).optional(),
    isActive: z.string().transform(val => val === 'true').optional(),
    emailVerified: z.string().transform(val => val === 'true').optional()
  })
}

// Review schemas
export const reviewSchemas = {
  submit: z.object({
    ease: z.number().int().min(1).max(5),
    quality: z.number().int().min(1).max(5).optional(),
    notes: z.string().max(1000, 'Notes too long').optional()
  }),

  stats: z.object({
    period: z.enum(['day', 'week', 'month', 'year']).default('week')
  }),

  sessionStart: z.object({
    limit: z.number().int().min(1).max(50).default(10)
  }),

  params: z.object({
    paperId: commonSchemas.uuid
  })
}

// Notes schemas
export const noteSchemas = {
  create: z.object({
    quickSummary: z.string().max(500, 'Quick summary too long').optional(),
    keyIdeas: z.array(z.string().max(200, 'Key idea too long')).max(10, 'Too many key ideas')
  }),

  update: z.object({
    quickSummary: z.string().max(500, 'Quick summary too long').optional(),
    keyIdeas: z.array(z.string().max(200, 'Key idea too long')).max(10, 'Too many key ideas').optional()
  }),

  params: z.object({
    paperId: commonSchemas.uuid
  })
}

// Health check schemas
export const healthSchemas = {
  query: z.object({
    detailed: z.string().transform(val => val === 'true').default(false)
  })
}

// Admin schemas
export const adminSchemas = {
  userUpdate: z.object({
    displayName: z.string().min(1).max(100).optional(),
    role: z.enum(['admin', 'user', 'readonly']).optional(),
    isActive: z.boolean().optional(),
    emailVerified: z.boolean().optional()
  })
}

// Export all schemas grouped by domain
export const validationSchemas = {
  auth: authSchemas,
  paper: paperSchemas,
  collection: collectionSchemas,
  user: userSchemas,
  review: reviewSchemas,
  note: noteSchemas,
  health: healthSchemas,
  admin: adminSchemas
}
