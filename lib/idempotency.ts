import { NextRequest, NextResponse } from 'next/server'
import { createErrorResponse } from './validation'

/**
 * Idempotency middleware for preventing duplicate operations
 */

interface IdempotencyRecord {
  response: any
  timestamp: number
  status: number
  headers: Record<string, string>
}

// In-memory store (use Redis in production)
const idempotencyStore = new Map<string, IdempotencyRecord>()

// Cleanup expired entries periodically (24 hours)
const IDEMPOTENCY_TTL = 24 * 60 * 60 * 1000 // 24 hours
setInterval(() => {
  const now = Date.now()
  for (const [key, record] of idempotencyStore.entries()) {
    if (now - record.timestamp > IDEMPOTENCY_TTL) {
      idempotencyStore.delete(key)
    }
  }
}, 60 * 60 * 1000) // Cleanup every hour

/**
 * Generate idempotency key from request
 */
function generateIdempotencyKey(
  request: NextRequest,
  customKey?: string,
  includeBody: boolean = true
): string {
  const url = new URL(request.url)
  const method = request.method
  const userId = request.headers.get('x-user-id') || 'anonymous'
  
  let key = `${method}:${url.pathname}:${userId}`
  
  if (customKey) {
    key += `:${customKey}`
  }
  
  // For POST/PUT/PATCH requests, include body hash for uniqueness
  if (includeBody && ['POST', 'PUT', 'PATCH'].includes(method)) {
    // Note: In a real implementation, you'd hash the request body
    // For now, we'll use the idempotency header if provided
    const idempotencyHeader = request.headers.get('idempotency-key')
    if (idempotencyHeader) {
      key += `:${idempotencyHeader}`
    }
  }
  
  return key
}

/**
 * Check if request is idempotent and return cached response if exists
 */
function checkIdempotency(
  key: string
): { isIdempotent: boolean; cachedResponse?: NextResponse } {
  const record = idempotencyStore.get(key)
  
  if (!record) {
    return { isIdempotent: false }
  }
  
  // Check if record is still valid (within TTL)
  if (Date.now() - record.timestamp > IDEMPOTENCY_TTL) {
    idempotencyStore.delete(key)
    return { isIdempotent: false }
  }
  
  // Return cached response
  const response = NextResponse.json(record.response, { status: record.status })
  
  // Restore headers
  Object.entries(record.headers).forEach(([name, value]) => {
    response.headers.set(name, value)
  })
  
  // Add idempotency header
  response.headers.set('X-Idempotent-Replay', 'true')
  
  return { isIdempotent: true, cachedResponse: response }
}

/**
 * Store response for future idempotency checks
 */
function storeIdempotentResponse(
  key: string,
  response: NextResponse,
  responseBody: any
): void {
  const headers: Record<string, string> = {}
  
  // Store important headers
  response.headers.forEach((value, name) => {
    // Don't store certain headers that shouldn't be replayed
    if (!['set-cookie', 'date', 'server'].includes(name.toLowerCase())) {
      headers[name] = value
    }
  })
  
  const record: IdempotencyRecord = {
    response: responseBody,
    timestamp: Date.now(),
    status: response.status,
    headers
  }
  
  idempotencyStore.set(key, record)
}

/**
 * Idempotency middleware options
 */
interface IdempotencyOptions {
  // Custom key generator
  keyGenerator?: (request: NextRequest) => string
  
  // TTL for idempotency records (default: 24 hours)
  ttl?: number
  
  // Whether to include request body in key generation
  includeBody?: boolean
  
  // HTTP methods to apply idempotency to
  methods?: string[]
  
  // Whether idempotency is required (via header)
  required?: boolean
}

/**
 * Idempotency middleware
 */
export function withIdempotency(
  handler: (request: NextRequest, context: any, routeParams?: any) => Promise<NextResponse>,
  options: IdempotencyOptions = {}
) {
  const {
    keyGenerator,
    includeBody = true,
    methods = ['POST', 'PUT', 'PATCH'],
    required = false
  } = options
  
  return async (request: NextRequest, context: any, routeParams?: any) => {
    const { correlationId } = context
    
    // Only apply idempotency to specified methods
    if (!methods.includes(request.method)) {
      return handler(request, context, routeParams)
    }
    
    // Check if idempotency key is required
    const idempotencyHeader = request.headers.get('idempotency-key')
    if (required && !idempotencyHeader) {
      return createErrorResponse(
        'Idempotency key required',
        400,
        correlationId,
        undefined,
        'This endpoint requires an Idempotency-Key header to prevent duplicate operations',
        new URL(request.url).pathname
      )
    }
    
    // Generate idempotency key
    const key = keyGenerator 
      ? keyGenerator(request)
      : generateIdempotencyKey(request, idempotencyHeader || undefined, includeBody)
    
    // Check for existing idempotent response
    const { isIdempotent, cachedResponse } = checkIdempotency(key)
    
    if (isIdempotent && cachedResponse) {
      // Add correlation ID to cached response
      cachedResponse.headers.set('X-Correlation-ID', correlationId)
      return cachedResponse
    }
    
    // Execute handler
    const response = await handler(request, context, routeParams)
    
    // Store response for future idempotency (only for successful operations)
    if (response.status >= 200 && response.status < 300) {
      try {
        // Clone response to read body
        const responseClone = response.clone()
        const responseBody = await responseClone.json()
        
        storeIdempotentResponse(key, response, responseBody)
      } catch (error) {
        // If we can't parse the response body, don't store it
        console.warn('Failed to store idempotent response:', error)
      }
    }
    
    return response
  }
}

/**
 * Convenience function for endpoints that should be idempotent
 */
export function withRequiredIdempotency(
  handler: any,
  options: Omit<IdempotencyOptions, 'required'> = {}
) {
  return withIdempotency(handler, { ...options, required: true })
}

/**
 * Clear idempotency cache (useful for testing)
 */
export function clearIdempotencyCache(): void {
  idempotencyStore.clear()
}

/**
 * Get idempotency cache stats (useful for monitoring)
 */
export function getIdempotencyCacheStats(): {
  size: number
  oldestEntry: number | null
  newestEntry: number | null
} {
  const entries = Array.from(idempotencyStore.values())
  
  return {
    size: idempotencyStore.size,
    oldestEntry: entries.length > 0 ? Math.min(...entries.map(e => e.timestamp)) : null,
    newestEntry: entries.length > 0 ? Math.max(...entries.map(e => e.timestamp)) : null
  }
}
