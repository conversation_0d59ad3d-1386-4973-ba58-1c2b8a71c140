import { NextRequest } from 'next/server'

/**
 * Standardized pagination and sorting utilities
 */

export interface PaginationParams {
  page: number
  limit: number
  offset: number
}

export interface SortingParams {
  sortBy?: string
  sortOrder: 'asc' | 'desc'
}

export interface PaginationMeta {
  page: number
  limit: number
  total: number
  totalPages: number
  hasNext: boolean
  hasPrev: boolean
  nextPage?: number
  prevPage?: number
}

export interface SortingMeta {
  field: string
  order: 'asc' | 'desc'
}

export interface ListResponse<T> {
  data: T[]
  pagination: PaginationMeta
  sorting?: SortingMeta
}

/**
 * Default pagination limits
 */
export const PAGINATION_LIMITS = {
  DEFAULT_PAGE: 1,
  DEFAULT_LIMIT: 20,
  MIN_LIMIT: 1,
  MAX_LIMIT: 100,
  DEFAULT_SORT_ORDER: 'asc' as const
}

/**
 * Parse pagination parameters from request
 */
export function parsePaginationParams(
  request: NextRequest,
  defaultLimit: number = PAGINATION_LIMITS.DEFAULT_LIMIT
): PaginationParams {
  const url = new URL(request.url)
  
  const page = Math.max(
    parseInt(url.searchParams.get('page') || '1', 10),
    PAGINATION_LIMITS.DEFAULT_PAGE
  )
  
  const limit = Math.min(
    Math.max(
      parseInt(url.searchParams.get('limit') || defaultLimit.toString(), 10),
      PAGINATION_LIMITS.MIN_LIMIT
    ),
    PAGINATION_LIMITS.MAX_LIMIT
  )
  
  const offset = (page - 1) * limit
  
  return { page, limit, offset }
}

/**
 * Parse sorting parameters from request
 */
export function parseSortingParams(
  request: NextRequest,
  allowedFields: string[] = [],
  defaultField?: string
): SortingParams {
  const url = new URL(request.url)
  
  const sortBy = url.searchParams.get('sortBy') || url.searchParams.get('sort')
  const sortOrder = (url.searchParams.get('sortOrder') || url.searchParams.get('order') || PAGINATION_LIMITS.DEFAULT_SORT_ORDER) as 'asc' | 'desc'
  
  // Validate sort field
  let validatedSortBy: string | undefined
  if (sortBy) {
    if (allowedFields.length === 0 || allowedFields.includes(sortBy)) {
      validatedSortBy = sortBy
    } else if (defaultField) {
      validatedSortBy = defaultField
    }
  } else if (defaultField) {
    validatedSortBy = defaultField
  }
  
  // Validate sort order
  const validatedSortOrder: 'asc' | 'desc' = ['asc', 'desc'].includes(sortOrder) ? sortOrder : 'asc'
  
  return {
    sortBy: validatedSortBy,
    sortOrder: validatedSortOrder
  }
}

/**
 * Create pagination metadata
 */
export function createPaginationMeta(
  page: number,
  limit: number,
  total: number
): PaginationMeta {
  const totalPages = Math.ceil(total / limit)
  const hasNext = page < totalPages
  const hasPrev = page > 1
  
  return {
    page,
    limit,
    total,
    totalPages,
    hasNext,
    hasPrev,
    nextPage: hasNext ? page + 1 : undefined,
    prevPage: hasPrev ? page - 1 : undefined
  }
}

/**
 * Create sorting metadata
 */
export function createSortingMeta(
  field: string,
  order: 'asc' | 'desc'
): SortingMeta {
  return { field, order }
}

/**
 * Apply pagination to an array
 */
export function applyPagination<T>(
  items: T[],
  pagination: PaginationParams
): T[] {
  const { offset, limit } = pagination
  return items.slice(offset, offset + limit)
}

/**
 * Apply sorting to an array
 */
export function applySorting<T>(
  items: T[],
  sorting: SortingParams,
  customSortFn?: (a: T, b: T, field: string, order: 'asc' | 'desc') => number
): T[] {
  if (!sorting.sortBy) return items
  
  return [...items].sort((a, b) => {
    if (customSortFn) {
      return customSortFn(a, b, sorting.sortBy!, sorting.sortOrder)
    }
    
    const aVal = (a as any)[sorting.sortBy!]
    const bVal = (b as any)[sorting.sortBy!]
    
    // Handle null/undefined values
    if (aVal == null && bVal == null) return 0
    if (aVal == null) return 1
    if (bVal == null) return -1
    
    // Handle different data types
    let comparison = 0
    if (typeof aVal === 'string' && typeof bVal === 'string') {
      comparison = aVal.localeCompare(bVal)
    } else if (typeof aVal === 'number' && typeof bVal === 'number') {
      comparison = aVal - bVal
    } else if (aVal instanceof Date && bVal instanceof Date) {
      comparison = aVal.getTime() - bVal.getTime()
    } else {
      // Fallback to string comparison
      comparison = String(aVal).localeCompare(String(bVal))
    }
    
    return sorting.sortOrder === 'desc' ? -comparison : comparison
  })
}

/**
 * Create a standardized list response
 */
export function createListResponse<T>(
  items: T[],
  pagination: PaginationParams,
  total: number,
  sorting?: SortingParams
): ListResponse<T> {
  const paginationMeta = createPaginationMeta(pagination.page, pagination.limit, total)
  const sortingMeta = sorting?.sortBy ? createSortingMeta(sorting.sortBy, sorting.sortOrder) : undefined
  
  return {
    data: items,
    pagination: paginationMeta,
    sorting: sortingMeta
  }
}

/**
 * Middleware for standardized pagination and sorting
 */
export function withPaginationAndSorting<T>(
  handler: (
    request: NextRequest,
    context: any,
    routeParams?: any
  ) => Promise<{ items: T[]; total: number }>,
  options: {
    defaultLimit?: number
    allowedSortFields?: string[]
    defaultSortField?: string
    customSortFn?: (a: T, b: T, field: string, order: 'asc' | 'desc') => number
  } = {}
) {
  return async (request: NextRequest, context: any, routeParams?: any) => {
    const pagination = parsePaginationParams(request, options.defaultLimit)
    const sorting = parseSortingParams(request, options.allowedSortFields, options.defaultSortField)

    // Add pagination and sorting to context
    const enhancedContext = {
      ...context,
      pagination,
      sorting
    }

    try {
      const { items, total } = await handler(request, enhancedContext, routeParams)

      // Apply sorting and pagination
      const sortedItems = applySorting(items, sorting, options.customSortFn)
      const paginatedItems = applyPagination(sortedItems, pagination)

      const response = createListResponse(paginatedItems, pagination, total, sorting)

      // Import createSuccessResponse dynamically to avoid circular dependency
      const { createSuccessResponse } = await import('./validation')
      return createSuccessResponse(response.data, context.correlationId, {
        pagination: response.pagination,
        sorting: response.sorting
      })
    } catch (error) {
      console.error('Pagination middleware error:', error)
      // Import createErrorResponse dynamically to avoid circular dependency
      const { createErrorResponse } = await import('./validation')
      return createErrorResponse(
        'Internal server error',
        500,
        context.correlationId,
        undefined,
        'Failed to fetch data'
      )
    }
  }
}

/**
 * Database-style pagination helpers for when you want to paginate at the DB level
 */
export interface DatabasePaginationParams {
  limit: number
  offset: number
  orderBy?: string
  orderDirection?: 'ASC' | 'DESC'
}

export function createDatabasePaginationParams(
  pagination: PaginationParams,
  sorting: SortingParams
): DatabasePaginationParams {
  return {
    limit: pagination.limit,
    offset: pagination.offset,
    orderBy: sorting.sortBy,
    orderDirection: sorting.sortOrder.toUpperCase() as 'ASC' | 'DESC'
  }
}

/**
 * Common sort field mappings for different entities
 */
export const SORT_FIELD_MAPPINGS = {
  papers: {
    allowed: ['title', 'authors', 'year', 'venue', 'citationCount', 'createdAt', 'updatedAt'],
    default: 'createdAt'
  },
  collections: {
    allowed: ['name', 'createdAt', 'updatedAt'],
    default: 'createdAt'
  },
  users: {
    allowed: ['email', 'displayName', 'createdAt', 'lastLogin'],
    default: 'createdAt'
  },
  reviews: {
    allowed: ['nextDue', 'ease', 'lastReviewed', 'createdAt'],
    default: 'nextDue'
  }
}
