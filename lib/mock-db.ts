import type { Paper, Note, Collection, Review } from "./types"

// In-memory mock database
class MockDB {
  private papers: Map<string, Paper> = new Map()
  private notes: Map<string, Note> = new Map()
  private collections: Map<string, Collection> = new Map()
  private reviews: Map<string, Review> = new Map()

  constructor() {
    try {
      this.seedData()
    } catch (error) {
      console.error("Error seeding mock database:", error)
    }
  }

  private seedData() {
    // Sample papers
    const samplePapers: Paper[] = [
      {
        id: "1",
        title: "Attention Is All You Need",
        authors: ["Ashish Vaswani", "<PERSON><PERSON>", "<PERSON><PERSON>"],
        venue: "NeurIPS",
        year: 2017,
        tags: ["transformers", "attention", "nlp"],
        starred: true,
        createdAt: "2024-01-01T00:00:00Z",
        updatedAt: "2024-01-01T00:00:00Z",
      },
      {
        id: "2",
        title: "BERT: Pre-training of Deep Bidirectional Transformers",
        authors: ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"],
        venue: "NAACL",
        year: 2019,
        tags: ["bert", "transformers", "nlp", "pretraining"],
        starred: false,
        createdAt: "2024-01-02T00:00:00Z",
        updatedAt: "2024-01-02T00:00:00Z",
      },
    ]

    // Sample notes
    const sampleNotes: Note[] = [
      {
        id: "note-1",
        paperId: "1",
        quickSummary: "This paper revolutionized NLP by showing that attention mechanisms alone are sufficient for sequence modeling, leading to the development of BERT, GPT, and other transformer-based models.",
        keyIdeas: [
          "Introduces the Transformer architecture based solely on attention mechanisms",
          "Eliminates recurrence and convolutions entirely, achieving state-of-the-art results on machine translation tasks",
          "Multi-head attention allows the model to attend to different positions and is significantly faster to train than RNN-based models",
        ],
      },
    ]

    // Sample collections
    const sampleCollections: Collection[] = [
      {
        id: "col-1",
        name: "Transformer Papers",
        paperIds: ["1", "2"],
      },
    ]

    // Sample reviews
    const sampleReviews: Review[] = [
      {
        paperId: "1",
        ease: 2.5,
        nextDue: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(), // Due yesterday
      },
    ]

    // Populate maps with error handling
    try {
      samplePapers.forEach((paper) => this.papers.set(paper.id, paper))
      sampleNotes.forEach((note) => this.notes.set(note.paperId, note))
      sampleCollections.forEach((collection) => this.collections.set(collection.id, collection))
      sampleReviews.forEach((review) => this.reviews.set(review.paperId, review))
    } catch (error) {
      console.error("Error populating mock data:", error)
    }
  }

  // Papers CRUD
  papers = {
    getAll: (): Paper[] => {
      try {
        return Array.from(this.papers.values())
      } catch (error) {
        console.error("Error getting all papers:", error)
        return []
      }
    },
    getById: (id: string): Paper | undefined => {
      try {
        return this.papers.get(id)
      } catch (error) {
        console.error("Error getting paper by id:", error)
        return undefined
      }
    },
    create: (paper: Paper): Paper => {
      try {
        this.papers.set(paper.id, paper)
        return paper
      } catch (error) {
        console.error("Error creating paper:", error)
        throw error
      }
    },
    update: (id: string, updates: Partial<Paper>): Paper | null => {
      try {
        const existing = this.papers.get(id)
        if (!existing) return null
        const updated = { ...existing, ...updates }
        this.papers.set(id, updated)
        return updated
      } catch (error) {
        console.error("Error updating paper:", error)
        return null
      }
    },
    delete: (id: string): boolean => {
      try {
        return this.papers.delete(id)
      } catch (error) {
        console.error("Error deleting paper:", error)
        return false
      }
    },
  }

  // Notes CRUD
  notes = {
    getAll: (): Note[] => {
      try {
        return Array.from(this.notes.values())
      } catch (error) {
        console.error("Error getting all notes:", error)
        return []
      }
    },
    getByPaperId: (paperId: string): Note | undefined => {
      try {
        return this.notes.get(paperId)
      } catch (error) {
        console.error("Error getting note by paper id:", error)
        return undefined
      }
    },
    create: (note: Note): Note => {
      try {
        this.notes.set(note.paperId, note)
        return note
      } catch (error) {
        console.error("Error creating note:", error)
        throw error
      }
    },
    update: (id: string, updates: Partial<Note>): Note | null => {
      try {
        const existing = Array.from(this.notes.values()).find((n) => n.id === id)
        if (!existing) return null
        const updated = { ...existing, ...updates }
        this.notes.set(existing.paperId, updated)
        return updated
      } catch (error) {
        console.error("Error updating note:", error)
        return null
      }
    },
    delete: (paperId: string): boolean => {
      try {
        return this.notes.delete(paperId)
      } catch (error) {
        console.error("Error deleting note:", error)
        return false
      }
    },
  }

  // Collections CRUD
  collections = {
    getAll: (): Collection[] => {
      try {
        return Array.from(this.collections.values())
      } catch (error) {
        console.error("Error getting all collections:", error)
        return []
      }
    },
    getById: (id: string): Collection | undefined => {
      try {
        return this.collections.get(id)
      } catch (error) {
        console.error("Error getting collection by id:", error)
        return undefined
      }
    },
    create: (collection: Collection): Collection => {
      try {
        this.collections.set(collection.id, collection)
        return collection
      } catch (error) {
        console.error("Error creating collection:", error)
        throw error
      }
    },
    update: (id: string, updates: Partial<Collection>): Collection | null => {
      try {
        const existing = this.collections.get(id)
        if (!existing) return null
        const updated = { ...existing, ...updates }
        this.collections.set(id, updated)
        return updated
      } catch (error) {
        console.error("Error updating collection:", error)
        return null
      }
    },
    delete: (id: string): boolean => {
      try {
        return this.collections.delete(id)
      } catch (error) {
        console.error("Error deleting collection:", error)
        return false
      }
    },
  }

  // Reviews CRUD
  reviews = {
    getAll: (): Review[] => {
      try {
        return Array.from(this.reviews.values())
      } catch (error) {
        console.error("Error getting all reviews:", error)
        return []
      }
    },
    getByPaperId: (paperId: string): Review | undefined => {
      try {
        return this.reviews.get(paperId)
      } catch (error) {
        console.error("Error getting review by paper id:", error)
        return undefined
      }
    },
    create: (review: Review): Review => {
      try {
        this.reviews.set(review.paperId, review)
        return review
      } catch (error) {
        console.error("Error creating review:", error)
        throw error
      }
    },
    update: (paperId: string, updates: Partial<Review>): Review | null => {
      try {
        const existing = this.reviews.get(paperId)
        if (!existing) return null
        const updated = { ...existing, ...updates }
        this.reviews.set(paperId, updated)
        return updated
      } catch (error) {
        console.error("Error updating review:", error)
        return null
      }
    },
    delete: (paperId: string): boolean => {
      try {
        return this.reviews.delete(paperId)
      } catch (error) {
        console.error("Error deleting review:", error)
        return false
      }
    },
  }
}

export const mockDB = new MockDB()
