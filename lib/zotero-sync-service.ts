import { ZoteroClient } from './zotero-client'
import { papers, notes, collections } from './database'
import { Paper, Note, ZoteroSyncResult, ZoteroSettings, Collection } from './types'

export class ZoteroSyncService {
  private client: ZoteroClient
  private settings: ZoteroSettings

  constructor(apiKey: string, settings: ZoteroSettings) {
    this.client = new ZoteroClient(apiKey)
    this.settings = settings
  }

  /**
   * Determines the appropriate Zotero destination for a paper based on its collections.
   * If a paper belongs to multiple collections with different destinations, uses the first one found.
   * Falls back to global user settings if no collection-specific destination is configured.
   */
  private async getZoteroDestinationForPaper(paperId: string, userId: string): Promise<ZoteroSettings> {
    try {
      // Get all collections for the user
      const userCollections = await collections.getByUserId(userId)

      // Find collections that contain this paper and have Zotero destination configured
      const collectionsWithPaper = userCollections.filter(collection =>
        collection.paperIds.includes(paperId) &&
        collection.zoteroLibraryType &&
        // For group libraries, ensure libraryId is also set
        (collection.zoteroLibraryType === 'user' || collection.zoteroLibraryId)
      )

      // If we found a collection with specific Zotero destination, use it
      if (collectionsWithPaper.length > 0) {
        const targetCollection = collectionsWithPaper[0] // Use first match if multiple

        return {
          ...this.settings,
          libraryType: targetCollection.zoteroLibraryType!,
          libraryId: targetCollection.zoteroLibraryType === 'user' ? 'user' : targetCollection.zoteroLibraryId!
        }
      }

      // Fall back to global settings
      return this.settings
    } catch (error) {
      console.error('Error determining Zotero destination for paper:', error)
      // Fall back to global settings on error
      return this.settings
    }
  }

  async syncPaper(paperId: string): Promise<ZoteroSyncResult> {
    try {
      // Get paper and note data
      const paper = await papers.getById(paperId)
      if (!paper) {
        return {
          success: false,
          error: 'Paper not found'
        }
      }

      const note = await notes.getByPaperId(paperId)
      if (!note) {
        return {
          success: false,
          error: 'Note not found for paper'
        }
      }

      // Determine the appropriate Zotero destination for this paper
      const zoteroDestination = await this.getZoteroDestinationForPaper(paperId, paper.userId)

      // Update sync status to pending
      await papers.update(paperId, {
        zoteroSyncStatus: 'pending'
      })

      let itemKey = paper.zoteroItemKey
      let existingItem = null
      let existingItemVersion: number | undefined

      // If we don't have an item key, try to find existing item by DOI first
      if (!itemKey && paper.doi) {
        existingItem = await this.client.findItemByDOI(
          zoteroDestination.libraryType,
          zoteroDestination.libraryId || 'user',
          paper.doi
        )
        if (existingItem) {
          itemKey = existingItem.key
          existingItemVersion = existingItem.version
          console.log(`Found existing Zotero item by DOI: ${paper.doi}`)
        }
      }

      // If no item found by DOI, try to find by title and year
      if (!itemKey && paper.title) {
        existingItem = await this.client.findItemByTitle(
          zoteroDestination.libraryType,
          zoteroDestination.libraryId || 'user',
          paper.title,
          paper.year
        )
        if (existingItem) {
          itemKey = existingItem.key
          existingItemVersion = existingItem.version
          console.log(`Found existing Zotero item by title: "${paper.title}"${paper.year ? ` (${paper.year})` : ''}`)
        }
      }

      // Create or update the Zotero item
      if (itemKey) {
        console.log(`Updating existing Zotero item with key: ${itemKey} (version: ${existingItemVersion})`)
      } else {
        console.log(`Creating new Zotero item for paper: "${paper.title}"`)
      }

      const itemResult = await this.client.createOrUpdateItem(
        zoteroDestination.libraryType,
        zoteroDestination.libraryId || 'user',
        paper,
        itemKey,
        existingItemVersion
      )

      if (!itemResult.success) {
        await papers.update(paperId, {
          zoteroSyncStatus: 'error'
        })
        return itemResult
      }

      itemKey = itemResult.itemKey!

      // Create or update the note
      const noteResult = await this.client.createOrUpdateNote(
        zoteroDestination.libraryType,
        zoteroDestination.libraryId || 'user',
        itemKey,
        note,
        paper.zoteroNoteKey
      )

      if (!noteResult.success) {
        await papers.update(paperId, {
          zoteroSyncStatus: 'error',
          zoteroItemKey: itemKey
        })
        return noteResult
      }

      // Update paper with sync information
      await papers.update(paperId, {
        zoteroItemKey: itemKey,
        zoteroNoteKey: noteResult.noteKey!,
        zoteroLastSynced: new Date().toISOString(),
        zoteroSyncStatus: 'synced'
      })

      return {
        success: true,
        itemKey,
        noteKey: noteResult.noteKey,
        message: 'Paper synced successfully to Zotero'
      }
    } catch (error) {
      console.error('Error syncing paper to Zotero:', error)

      // Update sync status to error
      await papers.update(paperId, {
        zoteroSyncStatus: 'error'
      })

      // Provide more specific error messages for common issues
      let errorMessage = 'Unknown error occurred'
      if (error instanceof Error) {
        if (error.message.includes('is not a valid field for type')) {
          errorMessage = 'Field validation error: Some fields are not compatible with the selected item type'
        } else if (error.message.includes('Precondition Required')) {
          errorMessage = 'Version conflict: Unable to update existing item due to version mismatch'
        } else if (error.message.includes('Cannot read properties of undefined')) {
          errorMessage = 'Data structure error: Missing required data for Zotero sync'
        } else {
          errorMessage = error.message
        }
      }

      return {
        success: false,
        error: errorMessage
      }
    }
  }

  async syncMultiplePapers(paperIds: string[]): Promise<{
    successful: string[]
    failed: Array<{ paperId: string; error: string }>
    total: number
  }> {
    const successful: string[] = []
    const failed: Array<{ paperId: string; error: string }> = []

    for (const paperId of paperIds) {
      try {
        const result = await this.syncPaper(paperId)
        if (result.success) {
          successful.push(paperId)
        } else {
          failed.push({
            paperId,
            error: result.error || 'Unknown error'
          })
        }
      } catch (error) {
        failed.push({
          paperId,
          error: error instanceof Error ? error.message : 'Unknown error'
        })
      }
    }

    return {
      successful,
      failed,
      total: paperIds.length
    }
  }

  async getChangedPapers(userId: string): Promise<Paper[]> {
    try {
      const userPapers = await papers.getByUserId(userId)
      
      return userPapers.filter(paper => {
        // Include papers that have never been synced
        if (!paper.zoteroLastSynced) {
          return true
        }
        
        // Include papers that have been updated since last sync
        const lastSynced = new Date(paper.zoteroLastSynced)
        const lastUpdated = new Date(paper.updatedAt)
        
        return lastUpdated > lastSynced
      })
    } catch (error) {
      console.error('Error getting changed papers:', error)
      return []
    }
  }

  async testConnection(): Promise<boolean> {
    return await this.client.testConnection(this.settings.libraryType, this.settings.libraryId)
  }

  async getUserLibraries() {
    return await this.client.getUserLibraries()
  }

  /**
   * Get available Zotero groups for the user
   */
  async getZoteroGroups(): Promise<Array<{
    id: string
    name: string
    description?: string
    type: string
    memberCount?: number
  }>> {
    try {
      return await this.client.getGroups()
    } catch (error) {
      console.error('Error fetching Zotero groups:', error)
      throw error
    }
  }

  /**
   * Search for a paper in all accessible Zotero libraries
   * Returns enriched metadata if found, null otherwise
   */
  async searchPaperInZotero(title: string, doi?: string, authors?: string[]): Promise<{
    metadata: any
    source: string
    libraryType: 'user' | 'group'
    libraryId: string
    libraryName: string
  } | null> {
    try {
      console.log('ZoteroSyncService: Starting paper search', {
        title,
        doi,
        authors,
        settingsLibraryType: this.settings.libraryType,
        settingsLibraryId: this.settings.libraryId,
        hasApiKey: !!this.settings.apiKey,
        apiKeyLength: this.settings.apiKey?.length
      })

      const result = await this.client.searchPaperInAllLibraries(title, doi, authors)

      if (!result) {
        console.log('ZoteroSyncService: Paper not found in any Zotero library', {
          title,
          doi,
          searchedLibraryType: this.settings.libraryType,
          searchedLibraryId: this.settings.libraryId
        })
        return null
      }

      console.log('ZoteroSyncService: Paper found in Zotero', {
        title,
        doi,
        foundInLibrary: result.libraryName,
        foundInLibraryType: result.libraryType,
        foundInLibraryId: result.libraryId,
        itemKey: result.item.key,
        itemTitle: result.item.title
      })

      // Convert Zotero item to our metadata format
      const metadata = this.client.convertZoteroItemToPaperMetadata(result.item)

      console.log('ZoteroSyncService: Metadata conversion completed', {
        title,
        metadataFields: Object.keys(metadata),
        hasTitle: !!metadata.title,
        hasDoi: !!metadata.doi,
        hasAbstract: !!metadata.abstract
      })

      return {
        metadata,
        source: `Zotero - ${result.libraryName}`,
        libraryType: result.libraryType,
        libraryId: result.libraryId,
        libraryName: result.libraryName
      }
    } catch (error: any) {
      console.error('ZoteroSyncService: Error searching paper in Zotero', {
        error: error.message,
        errorName: error.name,
        errorStack: error.stack,
        title,
        doi,
        authors,
        settingsLibraryType: this.settings.libraryType,
        settingsLibraryId: this.settings.libraryId,
        hasApiKey: !!this.settings.apiKey
      })
      return null
    }
  }
}

export async function createZoteroSyncService(
  apiKey: string,
  settings: ZoteroSettings
): Promise<ZoteroSyncService> {
  return new ZoteroSyncService(apiKey, settings)
}

// Helper function to get Zotero settings from user preferences
export function getZoteroSettingsFromPreferences(preferences: Record<string, any>): ZoteroSettings {
  const zotero = preferences.zotero || {}
  
  return {
    apiKey: zotero.apiKey,
    libraryType: zotero.libraryType || 'user',
    libraryId: zotero.libraryId,
    enabled: zotero.enabled || false
  }
}

// Helper function to validate Zotero settings
export function validateZoteroSettings(settings: ZoteroSettings): { valid: boolean; errors: string[] } {
  const errors: string[] = []

  if (!settings.apiKey) {
    errors.push('API key is required')
  }

  if (settings.libraryType === 'group' && !settings.libraryId) {
    errors.push('Library ID is required for group libraries')
  }

  return {
    valid: errors.length === 0,
    errors
  }
}
