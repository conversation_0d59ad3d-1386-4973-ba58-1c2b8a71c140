import { NextRequest, NextResponse } from 'next/server'
import { createErrorResponse } from './validation'
import { logSecurityEvent } from './logging'

/**
 * Enhanced rate limiting system with middleware support
 */

interface RateLimitRecord {
  count: number
  resetTime: number
  firstRequest: number
}

interface RateLimitConfig {
  maxRequests: number
  windowMs: number
  keyGenerator?: (request: NextRequest) => string
  skipSuccessfulRequests?: boolean
  skipFailedRequests?: boolean
  message?: string
  headers?: boolean
}

interface RateLimitResult {
  allowed: boolean
  remaining: number
  resetTime: number
  totalHits: number
}

// In-memory store (use Redis in production)
const rateLimitStore = new Map<string, RateLimitRecord>()

// Cleanup expired entries periodically
setInterval(() => {
  const now = Date.now()
  for (const [key, record] of rateLimitStore.entries()) {
    if (now > record.resetTime) {
      rateLimitStore.delete(key)
    }
  }
}, 60000) // Cleanup every minute

export class EnhancedRateLimiter {
  private config: RateLimitConfig

  constructor(config: RateLimitConfig) {
    this.config = {
      headers: true,
      ...config
    }
  }

  check(key: string): RateLimitResult {
    const now = Date.now()
    const record = rateLimitStore.get(key)

    if (!record || now > record.resetTime) {
      // Create new record
      const newRecord: RateLimitRecord = {
        count: 1,
        resetTime: now + this.config.windowMs,
        firstRequest: now
      }
      rateLimitStore.set(key, newRecord)
      
      return {
        allowed: true,
        remaining: this.config.maxRequests - 1,
        resetTime: newRecord.resetTime,
        totalHits: 1
      }
    }

    // Update existing record
    record.count++
    
    const remaining = Math.max(0, this.config.maxRequests - record.count)
    const allowed = record.count <= this.config.maxRequests

    return {
      allowed,
      remaining,
      resetTime: record.resetTime,
      totalHits: record.count
    }
  }

  /**
   * Create rate limit middleware
   */
  middleware() {
    return (request: NextRequest, correlationId: string): NextResponse | null => {
      const key = this.config.keyGenerator 
        ? this.config.keyGenerator(request)
        : this.getDefaultKey(request)

      const result = this.check(key)

      if (!result.allowed) {
        // Log rate limit violation
        logSecurityEvent('rate_limit_exceeded', {
          key,
          totalHits: result.totalHits,
          maxRequests: this.config.maxRequests,
          windowMs: this.config.windowMs,
          resetTime: new Date(result.resetTime).toISOString()
        }, request, correlationId)

        const retryAfter = Math.ceil((result.resetTime - Date.now()) / 1000)
        
        const response = createErrorResponse(
          'Rate limit exceeded',
          429,
          correlationId,
          {
            retryAfter,
            limit: this.config.maxRequests,
            remaining: result.remaining,
            resetTime: new Date(result.resetTime).toISOString()
          },
          this.config.message || 'Too many requests. Please try again later.',
          new URL(request.url).pathname
        )

        // Add rate limit headers
        if (this.config.headers) {
          response.headers.set('X-RateLimit-Limit', this.config.maxRequests.toString())
          response.headers.set('X-RateLimit-Remaining', result.remaining.toString())
          response.headers.set('X-RateLimit-Reset', Math.ceil(result.resetTime / 1000).toString())
          response.headers.set('Retry-After', retryAfter.toString())
        }

        return response
      }

      return null // No rate limit violation
    }
  }

  private getDefaultKey(request: NextRequest): string {
    const ip = request.headers.get('x-forwarded-for') || 
               request.headers.get('x-real-ip') || 
               'unknown'
    const url = new URL(request.url)
    return `${ip}:${request.method}:${url.pathname}`
  }

  reset(key: string): void {
    rateLimitStore.delete(key)
  }
}

/**
 * Rate limiting middleware that can be applied to any route
 */
export function withRateLimit(
  handler: (request: NextRequest, context: any, routeParams?: any) => Promise<NextResponse>,
  rateLimitConfig: RateLimitConfig
) {
  const limiter = new EnhancedRateLimiter(rateLimitConfig)
  
  return async (request: NextRequest, context: any, routeParams?: any) => {
    const correlationId = context.correlationId || 'unknown'
    
    // Check rate limit
    const rateLimitResponse = limiter.middleware()(request, correlationId)
    if (rateLimitResponse) {
      return rateLimitResponse
    }

    // Add rate limit headers to successful responses
    const response = await handler(request, context, routeParams)
    
    if (rateLimitConfig.headers !== false) {
      const key = rateLimitConfig.keyGenerator
        ? rateLimitConfig.keyGenerator(request)
        : limiter['getDefaultKey'](request)

      const result = limiter.check(key)
      if (result && typeof result.remaining === 'number' && typeof result.resetTime === 'number') {
        response.headers.set('X-RateLimit-Limit', rateLimitConfig.maxRequests.toString())
        response.headers.set('X-RateLimit-Remaining', Math.max(0, result.remaining - 1).toString())
        response.headers.set('X-RateLimit-Reset', Math.ceil(result.resetTime / 1000).toString())
      }
    }

    return response
  }
}

// Pre-configured rate limiters for different operations
export const rateLimitConfigs = {
  // Authentication endpoints
  login: {
    maxRequests: 5,
    windowMs: 15 * 60 * 1000, // 15 minutes
    message: 'Too many login attempts. Please try again later.',
    keyGenerator: (req: NextRequest) => {
      const ip = req.headers.get('x-forwarded-for') || req.headers.get('x-real-ip') || 'unknown'
      return `login:${ip}`
    }
  },

  register: {
    maxRequests: 3,
    windowMs: 60 * 60 * 1000, // 1 hour
    message: 'Too many registration attempts. Please try again later.',
    keyGenerator: (req: NextRequest) => {
      const ip = req.headers.get('x-forwarded-for') || req.headers.get('x-real-ip') || 'unknown'
      return `register:${ip}`
    }
  },

  passwordReset: {
    maxRequests: 3,
    windowMs: 60 * 60 * 1000, // 1 hour
    message: 'Too many password reset attempts. Please try again later.',
    keyGenerator: (req: NextRequest) => {
      const ip = req.headers.get('x-forwarded-for') || req.headers.get('x-real-ip') || 'unknown'
      return `password-reset:${ip}`
    }
  },

  emailVerification: {
    maxRequests: 5,
    windowMs: 60 * 60 * 1000, // 1 hour
    message: 'Too many verification email requests. Please try again later.',
    keyGenerator: (req: NextRequest) => {
      const ip = req.headers.get('x-forwarded-for') || req.headers.get('x-real-ip') || 'unknown'
      return `email-verification:${ip}`
    }
  },

  // API endpoints
  apiGeneral: {
    maxRequests: 100,
    windowMs: 15 * 60 * 1000, // 15 minutes
    message: 'API rate limit exceeded. Please slow down your requests.',
    keyGenerator: (req: NextRequest) => {
      const ip = req.headers.get('x-forwarded-for') || req.headers.get('x-real-ip') || 'unknown'
      const authHeader = req.headers.get('authorization')
      const userId = authHeader ? 'authenticated' : 'anonymous'
      return `api:${ip}:${userId}`
    }
  },

  // Authenticated user endpoints (higher limits)
  apiAuthenticated: {
    maxRequests: 200,
    windowMs: 15 * 60 * 1000, // 15 minutes
    message: 'API rate limit exceeded. Please slow down your requests.',
    keyGenerator: (req: NextRequest) => {
      const authHeader = req.headers.get('authorization')
      const ip = req.headers.get('x-forwarded-for') || req.headers.get('x-real-ip') || 'unknown'
      return `api-auth:${ip}:${authHeader || 'no-auth'}`
    }
  },

  // File upload endpoints
  upload: {
    maxRequests: 10,
    windowMs: 60 * 60 * 1000, // 1 hour
    message: 'Upload rate limit exceeded. Please try again later.',
    keyGenerator: (req: NextRequest) => {
      const ip = req.headers.get('x-forwarded-for') || req.headers.get('x-real-ip') || 'unknown'
      return `upload:${ip}`
    }
  }
}
