/**
 * Database transaction utilities for ensuring data consistency
 */

import { query, getPool } from './db'

/**
 * Transaction wrapper for database operations
 */
export class DatabaseTransaction {
  private client: any
  private isInTransaction: boolean = false

  constructor(client?: any) {
    this.client = client
  }
  
  /**
   * Execute operations within a transaction
   */
  async withTransaction<T>(
    operations: (tx: DatabaseTransaction) => Promise<T>
  ): Promise<T> {
    if (this.isInTransaction && this.client) {
      // Already in a transaction, just execute operations
      return await operations(this)
    }

    const pool = getPool()
    const client = await pool.connect()

    try {
      await client.query('BEGIN')
      this.client = client
      this.isInTransaction = true

      const result = await operations(this)

      await client.query('COMMIT')
      this.isInTransaction = false

      return result
    } catch (error) {
      await client.query('ROLLBACK')
      this.isInTransaction = false
      throw error
    } finally {
      client.release()
      this.client = null
    }
  }
  
  /**
   * Execute a query
   */
  async query(sql: string, params?: any[]): Promise<any> {
    if (this.client) {
      return await this.client.query(sql, params)
    } else {
      return await query(sql, params)
    }
  }

  /**
   * Get a single row
   */
  async get(sql: string, params?: any[]): Promise<any> {
    const result = await this.query(sql, params)
    return result.rows[0] || null
  }

  /**
   * Get all rows
   */
  async all(sql: string, params?: any[]): Promise<any[]> {
    const result = await this.query(sql, params)
    return result.rows || []
  }
}

/**
 * Database constraints and indexes for data integrity
 */
export class DatabaseConstraints {

  constructor() {
    // PostgreSQL-based constraints
  }
  
  /**
   * Add missing constraints and indexes
   */
  async addConstraintsAndIndexes(): Promise<void> {
    const constraints = [
      // Users table constraints
      `CREATE UNIQUE INDEX IF NOT EXISTS idx_users_email ON users(email)`,
      `CREATE INDEX IF NOT EXISTS idx_users_created_at ON users(created_at)`,
      `CREATE INDEX IF NOT EXISTS idx_users_last_login ON users(last_login)`,
      `CREATE INDEX IF NOT EXISTS idx_users_role ON users(role)`,
      
      // Papers table constraints
      `CREATE INDEX IF NOT EXISTS idx_papers_user_id ON papers(user_id)`,
      `CREATE INDEX IF NOT EXISTS idx_papers_created_at ON papers(created_at)`,
      `CREATE INDEX IF NOT EXISTS idx_papers_year ON papers(year)`,
      `CREATE INDEX IF NOT EXISTS idx_papers_starred ON papers(starred)`,
      `CREATE INDEX IF NOT EXISTS idx_papers_doi ON papers(doi)`,
      `CREATE INDEX IF NOT EXISTS idx_papers_title ON papers(title)`,
      
      // Collections table constraints
      `CREATE INDEX IF NOT EXISTS idx_collections_user_id ON collections(userId)`,
      `CREATE INDEX IF NOT EXISTS idx_collections_created_at ON collections(createdAt)`,
      `CREATE INDEX IF NOT EXISTS idx_collections_name ON collections(name)`,
      
      // Collection papers table constraints
      `CREATE INDEX IF NOT EXISTS idx_collection_papers_collection_id ON collectionPapers(collectionId)`,
      `CREATE INDEX IF NOT EXISTS idx_collection_papers_paper_id ON collectionPapers(paperId)`,
      `CREATE UNIQUE INDEX IF NOT EXISTS idx_collection_papers_unique ON collectionPapers(collectionId, paperId)`,
      
      // Reviews table constraints
      `CREATE INDEX IF NOT EXISTS idx_reviews_paper_id ON reviews(paperId)`,
      `CREATE INDEX IF NOT EXISTS idx_reviews_next_due ON reviews(nextDue)`,
      `CREATE INDEX IF NOT EXISTS idx_reviews_created_at ON reviews(createdAt)`,
      `CREATE UNIQUE INDEX IF NOT EXISTS idx_reviews_paper_unique ON reviews(paperId)`,
      
      // Notes table constraints
      `CREATE INDEX IF NOT EXISTS idx_notes_paper_id ON notes(paperId)`,
      `CREATE INDEX IF NOT EXISTS idx_notes_created_at ON notes(createdAt)`,
      `CREATE UNIQUE INDEX IF NOT EXISTS idx_notes_paper_unique ON notes(paperId)`,
      
      // User sessions table constraints
      `CREATE INDEX IF NOT EXISTS idx_user_sessions_user_id ON userSessions(userId)`,
      `CREATE INDEX IF NOT EXISTS idx_user_sessions_token_hash ON userSessions(tokenHash)`,
      `CREATE INDEX IF NOT EXISTS idx_user_sessions_expires_at ON userSessions(expiresAt)`,
      `CREATE INDEX IF NOT EXISTS idx_user_sessions_created_at ON userSessions(createdAt)`,
      
      // Email verification tokens table constraints
      `CREATE INDEX IF NOT EXISTS idx_email_verification_tokens_user_id ON emailVerificationTokens(userId)`,
      `CREATE INDEX IF NOT EXISTS idx_email_verification_tokens_token_hash ON emailVerificationTokens(tokenHash)`,
      `CREATE INDEX IF NOT EXISTS idx_email_verification_tokens_expires_at ON emailVerificationTokens(expiresAt)`,
      
      // Password reset tokens table constraints
      `CREATE INDEX IF NOT EXISTS idx_password_reset_tokens_user_id ON passwordResetTokens(userId)`,
      `CREATE INDEX IF NOT EXISTS idx_password_reset_tokens_token_hash ON passwordResetTokens(tokenHash)`,
      `CREATE INDEX IF NOT EXISTS idx_password_reset_tokens_expires_at ON passwordResetTokens(expiresAt)`,
      
      // Audit logs table constraints
      `CREATE INDEX IF NOT EXISTS idx_audit_logs_user_id ON auditLogs(userId)`,
      `CREATE INDEX IF NOT EXISTS idx_audit_logs_action ON auditLogs(action)`,
      `CREATE INDEX IF NOT EXISTS idx_audit_logs_resource_type ON auditLogs(resourceType)`,
      `CREATE INDEX IF NOT EXISTS idx_audit_logs_resource_id ON auditLogs(resourceId)`,
      `CREATE INDEX IF NOT EXISTS idx_audit_logs_created_at ON auditLogs(createdAt)`,
      `CREATE INDEX IF NOT EXISTS idx_audit_logs_ip_address ON auditLogs(ipAddress)`
    ]
    
    // Execute all constraints
    for (const constraint of constraints) {
      try {
        await query(constraint)
      } catch (error) {
        console.warn(`Failed to add constraint: ${constraint}`, error)
      }
    }
  }
  
  /**
   * Add foreign key constraints (if not already present)
   */
  addForeignKeyConstraints(): void {
    // Note: SQLite foreign key constraints need to be added during table creation
    // This method documents the expected foreign key relationships
    const foreignKeys = [
      // Papers -> Users
      'papers.userId REFERENCES users(id) ON DELETE CASCADE',
      
      // Collections -> Users
      'collections.userId REFERENCES users(id) ON DELETE CASCADE',
      
      // Collection Papers -> Collections
      'collectionPapers.collectionId REFERENCES collections(id) ON DELETE CASCADE',
      
      // Collection Papers -> Papers
      'collectionPapers.paperId REFERENCES papers(id) ON DELETE CASCADE',
      
      // Reviews -> Papers
      'reviews.paperId REFERENCES papers(id) ON DELETE CASCADE',
      
      // Notes -> Papers
      'notes.paperId REFERENCES papers(id) ON DELETE CASCADE',
      
      // User Sessions -> Users
      'userSessions.userId REFERENCES users(id) ON DELETE CASCADE',
      
      // Email Verification Tokens -> Users
      'emailVerificationTokens.userId REFERENCES users(id) ON DELETE CASCADE',
      
      // Password Reset Tokens -> Users
      'passwordResetTokens.userId REFERENCES users(id) ON DELETE CASCADE',
      
      // Audit Logs -> Users
      'auditLogs.userId REFERENCES users(id) ON DELETE SET NULL'
    ]
    
    console.log('Foreign key relationships documented:', foreignKeys)
  }
  
  /**
   * Validate data integrity
   */
  async validateDataIntegrity(): Promise<{ valid: boolean; issues: string[] }> {
    const issues: string[] = []

    try {
      // Check for orphaned papers
      const orphanedPapers = await query(`
        SELECT COUNT(*) as count FROM papers
        WHERE user_id NOT IN (SELECT id FROM users)
      `)
      const orphanedPapersCount = parseInt(orphanedPapers.rows[0].count)
      
      if (orphanedPapersCount > 0) {
        issues.push(`Found ${orphanedPapersCount} orphaned papers`)
      }

      // Check for orphaned collections
      const orphanedCollections = await query(`
        SELECT COUNT(*) as count FROM collections
        WHERE user_id NOT IN (SELECT id FROM users)
      `)
      const orphanedCollectionsCount = parseInt(orphanedCollections.rows[0].count)

      if (orphanedCollectionsCount > 0) {
        issues.push(`Found ${orphanedCollectionsCount} orphaned collections`)
      }
      
      // Check for orphaned collection papers
      const orphanedCollectionPapers = this.db.prepare(`
        SELECT COUNT(*) as count FROM collectionPapers 
        WHERE collectionId NOT IN (SELECT id FROM collections)
           OR paperId NOT IN (SELECT id FROM papers)
      `).get() as { count: number }
      
      if (orphanedCollectionPapers.count > 0) {
        issues.push(`Found ${orphanedCollectionPapers.count} orphaned collection papers`)
      }
      
      // Check for orphaned reviews
      const orphanedReviews = this.db.prepare(`
        SELECT COUNT(*) as count FROM reviews 
        WHERE paperId NOT IN (SELECT id FROM papers)
      `).get() as { count: number }
      
      if (orphanedReviews.count > 0) {
        issues.push(`Found ${orphanedReviews.count} orphaned reviews`)
      }
      
      // Check for orphaned notes
      const orphanedNotes = this.db.prepare(`
        SELECT COUNT(*) as count FROM notes 
        WHERE paperId NOT IN (SELECT id FROM papers)
      `).get() as { count: number }
      
      if (orphanedNotes.count > 0) {
        issues.push(`Found ${orphanedNotes.count} orphaned notes`)
      }
      
      // Check for duplicate emails
      const duplicateEmails = this.db.prepare(`
        SELECT email, COUNT(*) as count FROM users 
        GROUP BY email HAVING COUNT(*) > 1
      `).all() as { email: string; count: number }[]
      
      if (duplicateEmails.length > 0) {
        issues.push(`Found ${duplicateEmails.length} duplicate email addresses`)
      }
      
    } catch (error) {
      issues.push(`Error during integrity check: ${error.message}`)
    }
    
    return {
      valid: issues.length === 0,
      issues
    }
  }
  
  /**
   * Clean up orphaned data
   */
  cleanupOrphanedData(): { cleaned: number; errors: string[] } {
    let cleaned = 0
    const errors: string[] = []
    
    try {
      // Clean up orphaned papers
      const orphanedPapersResult = this.db.prepare(`
        DELETE FROM papers WHERE userId NOT IN (SELECT id FROM users)
      `).run()
      cleaned += orphanedPapersResult.changes
      
      // Clean up orphaned collections
      const orphanedCollectionsResult = this.db.prepare(`
        DELETE FROM collections WHERE userId NOT IN (SELECT id FROM users)
      `).run()
      cleaned += orphanedCollectionsResult.changes
      
      // Clean up orphaned collection papers
      const orphanedCollectionPapersResult = this.db.prepare(`
        DELETE FROM collectionPapers 
        WHERE collectionId NOT IN (SELECT id FROM collections)
           OR paperId NOT IN (SELECT id FROM papers)
      `).run()
      cleaned += orphanedCollectionPapersResult.changes
      
      // Clean up orphaned reviews
      const orphanedReviewsResult = this.db.prepare(`
        DELETE FROM reviews WHERE paperId NOT IN (SELECT id FROM papers)
      `).run()
      cleaned += orphanedReviewsResult.changes
      
      // Clean up orphaned notes
      const orphanedNotesResult = this.db.prepare(`
        DELETE FROM notes WHERE paperId NOT IN (SELECT id FROM papers)
      `).run()
      cleaned += orphanedNotesResult.changes
      
      // Clean up expired sessions
      const expiredSessionsResult = this.db.prepare(`
        DELETE FROM userSessions WHERE expiresAt < datetime('now')
      `).run()
      cleaned += expiredSessionsResult.changes
      
      // Clean up expired tokens
      const expiredTokensResult = this.db.prepare(`
        DELETE FROM emailVerificationTokens WHERE expiresAt < datetime('now')
      `).run()
      cleaned += expiredTokensResult.changes
      
      const expiredResetTokensResult = this.db.prepare(`
        DELETE FROM passwordResetTokens WHERE expiresAt < datetime('now')
      `).run()
      cleaned += expiredResetTokensResult.changes
      
    } catch (error) {
      errors.push(`Error during cleanup: ${error.message}`)
    }
    
    return { cleaned, errors }
  }
}
