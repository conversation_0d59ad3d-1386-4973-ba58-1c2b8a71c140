import { NextRequest, NextResponse } from 'next/server'
import { createErrorResponse } from './validation'

/**
 * Request size and content limiting middleware
 */

interface RequestLimitsOptions {
  // Maximum request body size in bytes
  maxBodySize?: number
  
  // Maximum URL length
  maxUrlLength?: number
  
  // Maximum number of headers
  maxHeaders?: number
  
  // Maximum header value length
  maxHeaderLength?: number
  
  // Maximum query parameter count
  maxQueryParams?: number
  
  // Allowed content types (if specified, only these are allowed)
  allowedContentTypes?: string[]
  
  // Blocked content types (these are explicitly blocked)
  blockedContentTypes?: string[]
  
  // Maximum file upload size (for multipart/form-data)
  maxFileSize?: number
  
  // Maximum number of files in upload
  maxFiles?: number
}

/**
 * Default limits for different endpoint types
 */
export const requestLimits = {
  // Small API requests (auth, simple operations)
  small: {
    maxBodySize: 1024, // 1KB
    maxUrlLength: 2048, // 2KB
    maxHeaders: 20,
    maxHeaderLength: 1024,
    maxQueryParams: 10,
    allowedContentTypes: ['application/json', 'application/x-www-form-urlencoded']
  },
  
  // Medium API requests (paper creation, updates)
  medium: {
    maxBodySize: 50 * 1024, // 50KB
    maxUrlLength: 2048,
    maxHeaders: 30,
    maxHeaderLength: 2048,
    maxQueryParams: 20,
    allowedContentTypes: ['application/json', 'application/x-www-form-urlencoded']
  },
  
  // Large API requests (bulk operations)
  large: {
    maxBodySize: 500 * 1024, // 500KB
    maxUrlLength: 4096,
    maxHeaders: 50,
    maxHeaderLength: 4096,
    maxQueryParams: 50,
    allowedContentTypes: ['application/json', 'application/x-www-form-urlencoded']
  },
  
  // File upload requests
  upload: {
    maxBodySize: 10 * 1024 * 1024, // 10MB
    maxUrlLength: 2048,
    maxHeaders: 30,
    maxHeaderLength: 2048,
    maxQueryParams: 10,
    maxFileSize: 5 * 1024 * 1024, // 5MB per file
    maxFiles: 5,
    allowedContentTypes: [
      'multipart/form-data',
      'application/pdf',
      'image/jpeg',
      'image/png',
      'image/gif',
      'text/plain',
      'application/json'
    ]
  }
}

/**
 * Check request against size and content limits
 */
function checkRequestLimits(
  request: NextRequest,
  options: RequestLimitsOptions,
  correlationId: string
): NextResponse | null {
  const url = new URL(request.url)
  
  // Check URL length
  if (options.maxUrlLength && request.url.length > options.maxUrlLength) {
    return createErrorResponse(
      'URL too long',
      414,
      correlationId,
      { maxLength: options.maxUrlLength, actualLength: request.url.length },
      `URL must be shorter than ${options.maxUrlLength} characters`,
      url.pathname
    )
  }
  
  // Check number of query parameters
  if (options.maxQueryParams) {
    const queryParamCount = url.searchParams.size
    if (queryParamCount > options.maxQueryParams) {
      return createErrorResponse(
        'Too many query parameters',
        400,
        correlationId,
        { maxParams: options.maxQueryParams, actualParams: queryParamCount },
        `Maximum ${options.maxQueryParams} query parameters allowed`,
        url.pathname
      )
    }
  }
  
  // Check number of headers
  if (options.maxHeaders) {
    const headerCount = Array.from(request.headers.keys()).length
    if (headerCount > options.maxHeaders) {
      return createErrorResponse(
        'Too many headers',
        400,
        correlationId,
        { maxHeaders: options.maxHeaders, actualHeaders: headerCount },
        `Maximum ${options.maxHeaders} headers allowed`,
        url.pathname
      )
    }
  }
  
  // Check header lengths
  if (options.maxHeaderLength) {
    for (const [name, value] of request.headers.entries()) {
      if (value.length > options.maxHeaderLength) {
        return createErrorResponse(
          'Header value too long',
          400,
          correlationId,
          { 
            header: name, 
            maxLength: options.maxHeaderLength, 
            actualLength: value.length 
          },
          `Header '${name}' value must be shorter than ${options.maxHeaderLength} characters`,
          url.pathname
        )
      }
    }
  }
  
  // Check content type
  const contentType = request.headers.get('content-type')
  if (contentType) {
    const baseContentType = contentType.split(';')[0].trim().toLowerCase()
    
    // Check blocked content types
    if (options.blockedContentTypes?.includes(baseContentType)) {
      return createErrorResponse(
        'Content type not allowed',
        415,
        correlationId,
        { contentType: baseContentType, blockedTypes: options.blockedContentTypes },
        `Content type '${baseContentType}' is not allowed`,
        url.pathname
      )
    }
    
    // Check allowed content types
    if (options.allowedContentTypes && !options.allowedContentTypes.includes(baseContentType)) {
      return createErrorResponse(
        'Content type not supported',
        415,
        correlationId,
        { contentType: baseContentType, allowedTypes: options.allowedContentTypes },
        `Content type '${baseContentType}' is not supported. Allowed types: ${options.allowedContentTypes.join(', ')}`,
        url.pathname
      )
    }
  }
  
  // Check body size (if content-length header is present)
  if (options.maxBodySize && ['POST', 'PUT', 'PATCH'].includes(request.method)) {
    const contentLength = request.headers.get('content-length')
    if (contentLength) {
      const bodySize = parseInt(contentLength, 10)
      if (bodySize > options.maxBodySize) {
        return createErrorResponse(
          'Request body too large',
          413,
          correlationId,
          { 
            maxSize: options.maxBodySize, 
            actualSize: bodySize,
            maxSizeFormatted: formatBytes(options.maxBodySize),
            actualSizeFormatted: formatBytes(bodySize)
          },
          `Request body must be smaller than ${formatBytes(options.maxBodySize)}`,
          url.pathname
        )
      }
    }
  }
  
  return null // No violations
}

/**
 * Format bytes for human-readable display
 */
function formatBytes(bytes: number): string {
  if (bytes === 0) return '0 Bytes'
  
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

/**
 * Request limits middleware
 */
export function withRequestLimits(
  handler: (request: NextRequest, context: any, routeParams?: any) => Promise<NextResponse>,
  options: RequestLimitsOptions
) {
  return async (request: NextRequest, context: any, routeParams?: any) => {
    const { correlationId } = context
    
    // Check request limits
    const limitViolation = checkRequestLimits(request, options, correlationId)
    if (limitViolation) {
      return limitViolation
    }
    
    // Continue with handler
    return handler(request, context, routeParams)
  }
}

/**
 * Convenience functions for common request limit patterns
 */

export function withSmallRequestLimits(handler: any) {
  return withRequestLimits(handler, requestLimits.small)
}

export function withMediumRequestLimits(handler: any) {
  return withRequestLimits(handler, requestLimits.medium)
}

export function withLargeRequestLimits(handler: any) {
  return withRequestLimits(handler, requestLimits.large)
}

export function withUploadLimits(handler: any) {
  return withRequestLimits(handler, requestLimits.upload)
}

/**
 * Custom request limits for specific use cases
 */
export function withCustomLimits(
  handler: any,
  customLimits: RequestLimitsOptions
) {
  return withRequestLimits(handler, customLimits)
}
