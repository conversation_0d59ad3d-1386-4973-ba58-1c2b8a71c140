# Dependabot configuration for automated dependency updates
version: 2
updates:
  # Enable version updates for npm
  - package-ecosystem: "npm"
    directory: "/"
    schedule:
      interval: "weekly"
      day: "monday"
      time: "09:00"
    open-pull-requests-limit: 10
    reviewers:
      - "papernugget-team"
    assignees:
      - "papernugget-team"
    commit-message:
      prefix: "deps"
      prefix-development: "deps-dev"
      include: "scope"
    labels:
      - "dependencies"
      - "automated"
    ignore:
      # Ignore major version updates for critical dependencies
      - dependency-name: "next"
        update-types: ["version-update:semver-major"]
      - dependency-name: "react"
        update-types: ["version-update:semver-major"]
      - dependency-name: "react-dom"
        update-types: ["version-update:semver-major"]

  # Enable version updates for Docker
  - package-ecosystem: "docker"
    directory: "/"
    schedule:
      interval: "weekly"
      day: "tuesday"
      time: "09:00"
    open-pull-requests-limit: 5
    reviewers:
      - "papernugget-team"
    assignees:
      - "papernugget-team"
    commit-message:
      prefix: "docker"
      include: "scope"
    labels:
      - "docker"
      - "dependencies"
      - "automated"

  # Enable version updates for GitHub Actions
  - package-ecosystem: "github-actions"
    directory: "/"
    schedule:
      interval: "weekly"
      day: "wednesday"
      time: "09:00"
    open-pull-requests-limit: 5
    reviewers:
      - "papernugget-team"
    assignees:
      - "papernugget-team"
    commit-message:
      prefix: "ci"
      include: "scope"
    labels:
      - "github-actions"
      - "dependencies"
      - "automated"
