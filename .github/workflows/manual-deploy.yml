name: Manual Deployment

on:
  workflow_dispatch:
    inputs:
      environment:
        description: 'Deployment environment'
        required: true
        default: 'staging'
        type: choice
        options:
          - staging
          - production
      skip_tests:
        description: 'Skip tests (not recommended for production)'
        required: false
        default: false
        type: boolean
      force_deploy:
        description: 'Force deployment even if checks fail'
        required: false
        default: false
        type: boolean

jobs:
  # Pre-deployment validation
  pre-deployment:
    name: Pre-deployment Validation
    runs-on: ubuntu-latest
    outputs:
      should_deploy: ${{ steps.validation.outputs.should_deploy }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Validate deployment request
        id: validation
        run: |
          echo "🔍 Validating deployment request..."
          echo "Environment: ${{ github.event.inputs.environment }}"
          echo "Skip tests: ${{ github.event.inputs.skip_tests }}"
          echo "Force deploy: ${{ github.event.inputs.force_deploy }}"
          
          # Check if this is a production deployment
          if [ "${{ github.event.inputs.environment }}" = "production" ]; then
            echo "🚨 Production deployment requested"
            
            # Ensure we're on main branch for production
            if [ "${{ github.ref_name }}" != "main" ]; then
              echo "❌ Production deployments must be from main branch"
              echo "Current branch: ${{ github.ref_name }}"
              exit 1
            fi
            
            # Don't allow skipping tests for production
            if [ "${{ github.event.inputs.skip_tests }}" = "true" ]; then
              echo "❌ Cannot skip tests for production deployment"
              exit 1
            fi
          fi
          
          echo "✅ Deployment request validated"
          echo "should_deploy=true" >> $GITHUB_OUTPUT

  # Run tests (unless skipped)
  test:
    name: Run Tests
    runs-on: ubuntu-latest
    needs: pre-deployment
    if: github.event.inputs.skip_tests != 'true'
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_DB: papernugget_test
          POSTGRES_USER: papernugget
          POSTGRES_PASSWORD: test_password
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Run tests
        env:
          DATABASE_URL: postgresql://papernugget:test_password@localhost:5432/papernugget_test
          NODE_ENV: test
        run: |
          npm run migrate
          npm run test:ci

  # Build and validate
  build:
    name: Build Application
    runs-on: ubuntu-latest
    needs: [pre-deployment, test]
    if: always() && (needs.test.result == 'success' || needs.test.result == 'skipped')
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Build application
        run: npm run build

      - name: Create deployment package
        run: |
          echo "📦 Creating deployment package..."
          
          # Create deployment info
          cat > deployment-info.json << EOF
          {
            "version": "$(date +%Y%m%d-%H%M%S)",
            "commit": "${{ github.sha }}",
            "branch": "${{ github.ref_name }}",
            "environment": "${{ github.event.inputs.environment }}",
            "timestamp": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
            "workflow_run": "${{ github.run_id }}",
            "deployed_by": "${{ github.actor }}"
          }
          EOF
          
          # Package application
          tar -czf papernugget-deployment.tar.gz \
            .next \
            public \
            package.json \
            package-lock.json \
            deployment-info.json \
            lib \
            app \
            scripts \
            init-db \
            Dockerfile \
            docker-compose.yml \
            bootstrap.sh \
            Makefile

      - name: Upload deployment package
        uses: actions/upload-artifact@v4
        with:
          name: papernugget-deployment-${{ github.event.inputs.environment }}
          path: papernugget-deployment.tar.gz
          retention-days: 30

  # Deployment simulation (replace with actual deployment)
  deploy:
    name: Deploy to ${{ github.event.inputs.environment }}
    runs-on: ubuntu-latest
    needs: [pre-deployment, test, build]
    if: always() && needs.pre-deployment.outputs.should_deploy == 'true' && (needs.build.result == 'success' || github.event.inputs.force_deploy == 'true')
    environment: ${{ github.event.inputs.environment }}
    steps:
      - name: Download deployment package
        uses: actions/download-artifact@v4
        with:
          name: papernugget-deployment-${{ github.event.inputs.environment }}

      - name: Simulate deployment
        run: |
          echo "🚀 Deploying to ${{ github.event.inputs.environment }}..."
          echo "📦 Deployment package: papernugget-deployment.tar.gz"
          
          # Extract package
          tar -xzf papernugget-deployment.tar.gz
          
          # Show deployment info
          if [ -f deployment-info.json ]; then
            echo "📋 Deployment Information:"
            cat deployment-info.json | jq '.'
          fi
          
          echo "✅ Deployment to ${{ github.event.inputs.environment }} completed"
          
          # In a real deployment, you would:
          # 1. Upload to your server/cloud provider
          # 2. Run database migrations
          # 3. Update environment configuration
          # 4. Restart services
          # 5. Run health checks
          # 6. Update load balancer/DNS

      - name: Post-deployment verification
        run: |
          echo "🔍 Running post-deployment verification..."
          
          # In a real deployment, you would:
          # 1. Check application health endpoints
          # 2. Verify database connectivity
          # 3. Test critical user flows
          # 4. Monitor error rates
          
          echo "✅ Post-deployment verification completed"

  # Notification
  notify:
    name: Send Deployment Notification
    runs-on: ubuntu-latest
    needs: [deploy]
    if: always()
    steps:
      - name: Determine deployment status
        id: status
        run: |
          if [ "${{ needs.deploy.result }}" = "success" ]; then
            echo "status=success" >> $GITHUB_OUTPUT
            echo "message=✅ Deployment to ${{ github.event.inputs.environment }} completed successfully" >> $GITHUB_OUTPUT
          else
            echo "status=failure" >> $GITHUB_OUTPUT
            echo "message=❌ Deployment to ${{ github.event.inputs.environment }} failed" >> $GITHUB_OUTPUT
          fi

      - name: Create deployment summary
        run: |
          echo "# Deployment Summary" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "**Environment:** ${{ github.event.inputs.environment }}" >> $GITHUB_STEP_SUMMARY
          echo "**Status:** ${{ steps.status.outputs.status }}" >> $GITHUB_STEP_SUMMARY
          echo "**Commit:** ${{ github.sha }}" >> $GITHUB_STEP_SUMMARY
          echo "**Branch:** ${{ github.ref_name }}" >> $GITHUB_STEP_SUMMARY
          echo "**Deployed by:** ${{ github.actor }}" >> $GITHUB_STEP_SUMMARY
          echo "**Timestamp:** $(date -u +%Y-%m-%dT%H:%M:%SZ)" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "${{ steps.status.outputs.message }}" >> $GITHUB_STEP_SUMMARY

      # In a real setup, you might want to send notifications to:
      # - Slack/Discord
      # - Email
      # - PagerDuty
      # - Monitoring systems
