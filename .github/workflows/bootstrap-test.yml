name: Bootstrap Process Test

on:
  push:
    branches: [ main, develop ]
    paths:
      - 'bootstrap.sh'
      - 'Dockerfile'
      - 'docker-compose.yml'
      - 'init-db/**'
      - 'lib/db-migrations.ts'
      - 'lib/startup-checks.ts'
  pull_request:
    branches: [ main, develop ]
    paths:
      - 'bootstrap.sh'
      - 'Dockerfile'
      - 'docker-compose.yml'
      - 'init-db/**'
      - 'lib/db-migrations.ts'
      - 'lib/startup-checks.ts'
  workflow_dispatch:

jobs:
  # Test bootstrap process from scratch
  bootstrap-test:
    name: Test Bootstrap Process
    runs-on: ubuntu-latest
    strategy:
      matrix:
        os: [ubuntu-latest, ubuntu-20.04]
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Docker
        uses: docker/setup-buildx-action@v3

      - name: Verify Docker installation
        run: |
          docker --version
          docker compose version

      - name: Clean up any existing containers
        run: |
          docker system prune -f
          docker volume prune -f

      - name: Make bootstrap script executable
        run: chmod +x bootstrap.sh

      - name: Run bootstrap process
        timeout-minutes: 10
        run: |
          echo "🚀 Testing bootstrap process..."
          ./bootstrap.sh

      - name: Verify application is running
        run: |
          echo "🔍 Verifying application health..."
          
          # Wait a bit more for full startup
          sleep 10
          
          # Check if containers are running
          docker compose ps
          
          # Check application health
          curl -f http://localhost:3000/api/health || {
            echo "❌ Health check failed"
            echo "=== Application Logs ==="
            docker compose logs app
            echo "=== Database Logs ==="
            docker compose logs db
            exit 1
          }

      - name: Test detailed health check
        run: |
          echo "🔍 Running detailed health check..."
          curl -f "http://localhost:3000/api/health?detailed=true" | jq '.'

      - name: Test admin login
        run: |
          echo "🔐 Testing admin login..."
          
          # Test login endpoint
          response=$(curl -s -X POST http://localhost:3000/api/auth/login \
            -H "Content-Type: application/json" \
            -d '{"email":"<EMAIL>","password":"admin123"}')
          
          echo "Login response: $response"
          
          # Check if login was successful
          if echo "$response" | jq -e '.token' > /dev/null; then
            echo "✅ Admin login successful"
          else
            echo "❌ Admin login failed"
            echo "Response: $response"
            exit 1
          fi

      - name: Test database seeding
        run: |
          echo "🌱 Verifying database seeding..."
          
          # Check if sample papers exist
          docker compose exec -T db psql -U papernugget -d papernugget -c "SELECT COUNT(*) FROM papers;" | grep -q "3" || {
            echo "❌ Sample papers not found"
            docker compose exec -T db psql -U papernugget -d papernugget -c "SELECT * FROM papers;"
            exit 1
          }
          
          echo "✅ Database seeding verified"

      - name: Test email system
        run: |
          echo "📧 Testing email system..."
          
          # Check if Mailpit is accessible
          curl -f http://localhost:8025/ || {
            echo "❌ Mailpit not accessible"
            docker compose logs mailpit
            exit 1
          }
          
          echo "✅ Email system accessible"

      - name: Run comprehensive tests
        run: |
          echo "🧪 Running comprehensive tests..."
          docker compose exec -T app npm test || {
            echo "❌ Tests failed"
            docker compose logs app
            exit 1
          }

      - name: Test reset functionality
        run: |
          echo "🔄 Testing reset functionality..."
          
          # Stop services
          docker compose down -v
          
          # Run bootstrap again
          ./bootstrap.sh
          
          # Verify it works again
          sleep 10
          curl -f http://localhost:3000/api/health

      - name: Cleanup
        if: always()
        run: |
          echo "🧹 Cleaning up..."
          docker compose down -v
          docker system prune -f

  # Test bootstrap on different environments
  environment-test:
    name: Test Different Environments
    runs-on: ubuntu-latest
    strategy:
      matrix:
        env_config:
          - name: "minimal"
            env_vars: ""
          - name: "custom-ports"
            env_vars: "PORT=3001"
          - name: "custom-db"
            env_vars: "POSTGRES_DB=custom_db POSTGRES_USER=custom_user"
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup environment
        run: |
          echo "🔧 Setting up environment: ${{ matrix.env_config.name }}"
          cp .env.example .env
          
          # Apply custom environment variables
          if [ -n "${{ matrix.env_config.env_vars }}" ]; then
            echo "${{ matrix.env_config.env_vars }}" | tr ' ' '\n' >> .env
          fi

      - name: Make bootstrap script executable
        run: chmod +x bootstrap.sh

      - name: Run bootstrap with custom environment
        timeout-minutes: 10
        run: ./bootstrap.sh

      - name: Verify custom configuration
        run: |
          # Check if application is running
          if [ "${{ matrix.env_config.name }}" = "custom-ports" ]; then
            curl -f http://localhost:3001/api/health
          else
            curl -f http://localhost:3000/api/health
          fi

      - name: Cleanup
        if: always()
        run: docker compose down -v

  # Test bootstrap failure scenarios
  failure-scenarios:
    name: Test Failure Scenarios
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Test with missing Docker
        run: |
          echo "🧪 Testing bootstrap behavior without Docker..."
          
          # Temporarily rename docker to simulate missing Docker
          sudo mv /usr/bin/docker /usr/bin/docker.bak || true
          
          # Run bootstrap and expect it to fail gracefully
          ./bootstrap.sh && {
            echo "❌ Bootstrap should have failed without Docker"
            exit 1
          } || {
            echo "✅ Bootstrap correctly detected missing Docker"
          }
          
          # Restore docker
          sudo mv /usr/bin/docker.bak /usr/bin/docker || true

      - name: Test with invalid environment
        run: |
          echo "🧪 Testing bootstrap with invalid environment..."
          
          # Create invalid .env file
          echo "INVALID_CONFIG=true" > .env
          echo "DATABASE_URL=" >> .env
          
          # Bootstrap should handle this gracefully
          timeout 60 ./bootstrap.sh || {
            echo "✅ Bootstrap correctly handled invalid environment"
          }

  # Performance test
  performance-test:
    name: Bootstrap Performance Test
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Measure bootstrap time
        run: |
          echo "⏱️ Measuring bootstrap performance..."
          
          start_time=$(date +%s)
          ./bootstrap.sh
          end_time=$(date +%s)
          
          duration=$((end_time - start_time))
          echo "Bootstrap completed in ${duration} seconds"
          
          # Bootstrap should complete within 5 minutes
          if [ $duration -gt 300 ]; then
            echo "❌ Bootstrap took too long: ${duration}s (max: 300s)"
            exit 1
          else
            echo "✅ Bootstrap performance acceptable: ${duration}s"
          fi

      - name: Cleanup
        if: always()
        run: docker compose down -v
