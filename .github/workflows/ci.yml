name: CI Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]
  workflow_dispatch:

env:
  NODE_VERSION: '18'
  POSTGRES_VERSION: '15'

jobs:
  # Lint and type checking
  lint:
    name: Lint & Type Check
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Run ESLint
        run: npm run lint

      - name: Run TypeScript type check
        run: npx tsc --noEmit

  # Unit tests (no external dependencies)
  unit-tests:
    name: Unit Tests
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Run unit tests
        run: npm run test:unit

  # Integration tests (with database and services)
  integration-tests:
    name: Integration Tests
    runs-on: ubuntu-latest
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_DB: papernugget_test
          POSTGRES_USER: papernugget
          POSTGRES_PASSWORD: test_password
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

      mailpit:
        image: axllent/mailpit:latest
        ports:
          - 1025:1025
          - 8025:8025

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Setup test environment
        run: |
          cp .env.example .env.test
          cat >> .env.test << EOF
          NODE_ENV=test
          DATABASE_URL=postgresql://papernugget:test_password@localhost:5432/papernugget_test
          APP_URL=http://localhost:3000
          EMAIL_FROM=<EMAIL>
          SMTP_HOST=localhost
          SMTP_PORT=1025
          SMTP_USER=
          SMTP_PASS=
          SMTP_TLS=false
          JWT_SECRET=test-jwt-secret-key-for-ci
          EOF

      - name: Wait for PostgreSQL
        run: |
          until pg_isready -h localhost -p 5432 -U papernugget; do
            echo "Waiting for PostgreSQL..."
            sleep 2
          done

      - name: Run database migrations
        env:
          DATABASE_URL: postgresql://papernugget:test_password@localhost:5432/papernugget_test
        run: npm run migrate

      - name: Run integration tests
        env:
          DATABASE_URL: postgresql://papernugget:test_password@localhost:5432/papernugget_test
          NODE_ENV: test
        run: npm run test:integration

  # Full application test with Docker
  docker-test:
    name: Docker Integration Test
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Create test environment file
        run: |
          cp .env.example .env
          sed -i 's/password/ci_test_password/g' .env
          sed -i 's/localhost:3000/localhost:3000/g' .env

      - name: Build and start services
        run: |
          docker compose up --build -d
          
      - name: Wait for services to be ready
        run: |
          echo "Waiting for services to start..."
          timeout 120 bash -c 'until curl -f http://localhost:3000/api/health; do sleep 5; done'

      - name: Run health checks
        run: |
          docker compose exec -T app npm run health:detailed

      - name: Run all tests in Docker
        run: |
          docker compose exec -T app npm test

      - name: Check application logs
        if: failure()
        run: |
          echo "=== Application Logs ==="
          docker compose logs app
          echo "=== Database Logs ==="
          docker compose logs db

      - name: Cleanup
        if: always()
        run: |
          docker compose down -v

  # Security scanning
  security:
    name: Security Scan
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Run npm audit
        run: npm audit --audit-level=moderate

      - name: Run Snyk security scan
        uses: snyk/actions/node@master
        continue-on-error: true
        env:
          SNYK_TOKEN: ${{ secrets.SNYK_TOKEN }}
        with:
          args: --severity-threshold=high

  # Build verification
  build:
    name: Build Verification
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Build application
        run: npm run build

      - name: Verify build artifacts
        run: |
          if [ ! -d ".next" ]; then
            echo "Build failed: .next directory not found"
            exit 1
          fi
          echo "Build successful: .next directory exists"

  # Deployment readiness check
  deployment-check:
    name: Deployment Readiness
    runs-on: ubuntu-latest
    needs: [lint, unit-tests, integration-tests, docker-test, security, build]
    if: github.ref == 'refs/heads/main'
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Deployment readiness summary
        run: |
          echo "🎉 All CI checks passed!"
          echo "✅ Linting and type checking"
          echo "✅ Unit tests"
          echo "✅ Integration tests"
          echo "✅ Docker integration test"
          echo "✅ Security scanning"
          echo "✅ Build verification"
          echo ""
          echo "🚀 Ready for deployment!"

      - name: Create deployment artifact
        run: |
          echo "Creating deployment metadata..."
          cat > deployment-info.json << EOF
          {
            "commit": "${{ github.sha }}",
            "branch": "${{ github.ref_name }}",
            "timestamp": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
            "workflow_run": "${{ github.run_id }}",
            "tests_passed": true
          }
          EOF

      - name: Upload deployment artifact
        uses: actions/upload-artifact@v4
        with:
          name: deployment-info
          path: deployment-info.json
          retention-days: 30
