name: Security & Dependency Monitoring

on:
  schedule:
    # Run daily at 2 AM UTC
    - cron: '0 2 * * *'
  workflow_dispatch:
  push:
    paths:
      - 'package.json'
      - 'package-lock.json'
      - 'Dockerfile'
      - 'docker-compose.yml'

jobs:
  # Dependency vulnerability scanning
  dependency-scan:
    name: Dependency Vulnerability Scan
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Run npm audit
        run: |
          npm audit --audit-level=low --json > audit-results.json || true
          npm audit --audit-level=moderate

      - name: Upload audit results
        uses: actions/upload-artifact@v4
        with:
          name: npm-audit-results
          path: audit-results.json
          retention-days: 30

  # Docker image security scanning
  docker-security:
    name: Docker Security Scan
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Build Docker image
        run: |
          docker build -t papernugget:security-scan .

      - name: Run Trivy vulnerability scanner
        uses: aquasecurity/trivy-action@master
        with:
          image-ref: 'papernugget:security-scan'
          format: 'sarif'
          output: 'trivy-results.sarif'

      - name: Upload Trivy scan results
        uses: github/codeql-action/upload-sarif@v3
        if: always()
        with:
          sarif_file: 'trivy-results.sarif'

  # CodeQL security analysis
  codeql:
    name: CodeQL Security Analysis
    runs-on: ubuntu-latest
    permissions:
      actions: read
      contents: read
      security-events: write
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Initialize CodeQL
        uses: github/codeql-action/init@v3
        with:
          languages: javascript

      - name: Autobuild
        uses: github/codeql-action/autobuild@v3

      - name: Perform CodeQL Analysis
        uses: github/codeql-action/analyze@v3

  # License compliance check
  license-check:
    name: License Compliance
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Install license checker
        run: npm install -g license-checker

      - name: Check licenses
        run: |
          license-checker --json --out licenses.json
          license-checker --summary

      - name: Upload license report
        uses: actions/upload-artifact@v4
        with:
          name: license-report
          path: licenses.json
          retention-days: 30

  # Dependency update check
  dependency-update:
    name: Check for Dependency Updates
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'

      - name: Check for outdated packages
        run: |
          npm outdated --json > outdated.json || true
          echo "Outdated packages:"
          npm outdated || true

      - name: Upload outdated packages report
        uses: actions/upload-artifact@v4
        with:
          name: outdated-packages
          path: outdated.json
          retention-days: 30

  # Security summary
  security-summary:
    name: Security Summary
    runs-on: ubuntu-latest
    needs: [dependency-scan, docker-security, license-check, dependency-update]
    if: always()
    steps:
      - name: Download all artifacts
        uses: actions/download-artifact@v4

      - name: Generate security summary
        run: |
          echo "# Security Scan Summary" > security-summary.md
          echo "Generated on: $(date -u +%Y-%m-%dT%H:%M:%SZ)" >> security-summary.md
          echo "" >> security-summary.md
          
          echo "## Dependency Vulnerabilities" >> security-summary.md
          if [ -f npm-audit-results/audit-results.json ]; then
            echo "✅ NPM audit completed" >> security-summary.md
          else
            echo "❌ NPM audit failed" >> security-summary.md
          fi
          echo "" >> security-summary.md
          
          echo "## Docker Security" >> security-summary.md
          echo "✅ Docker image security scan completed" >> security-summary.md
          echo "" >> security-summary.md
          
          echo "## License Compliance" >> security-summary.md
          if [ -f license-report/licenses.json ]; then
            echo "✅ License compliance check completed" >> security-summary.md
          else
            echo "❌ License compliance check failed" >> security-summary.md
          fi
          echo "" >> security-summary.md
          
          echo "## Dependency Updates" >> security-summary.md
          if [ -f outdated-packages/outdated.json ]; then
            echo "✅ Dependency update check completed" >> security-summary.md
          else
            echo "❌ Dependency update check failed" >> security-summary.md
          fi

      - name: Upload security summary
        uses: actions/upload-artifact@v4
        with:
          name: security-summary
          path: security-summary.md
          retention-days: 90

      - name: Comment on PR (if applicable)
        if: github.event_name == 'pull_request'
        uses: actions/github-script@v7
        with:
          script: |
            const fs = require('fs');
            if (fs.existsSync('security-summary.md')) {
              const summary = fs.readFileSync('security-summary.md', 'utf8');
              github.rest.issues.createComment({
                issue_number: context.issue.number,
                owner: context.repo.owner,
                repo: context.repo.repo,
                body: summary
              });
            }
