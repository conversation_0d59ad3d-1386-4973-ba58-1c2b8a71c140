#!/bin/bash

# PaperNugget Setup Script (Legacy)
# This script is deprecated. Please use the new bootstrap script instead.

echo "⚠️  This setup script is deprecated!"
echo ""
echo "🚀 Please use the new bootstrap script for a better experience:"
echo "   ./bootstrap.sh"
echo ""
echo "Or use the Make command:"
echo "   make bootstrap"
echo ""
echo "The new bootstrap script provides:"
echo "   ✅ Complete environment setup"
echo "   ✅ Database schema migration"
echo "   ✅ Test data seeding"
echo "   ✅ Health verification"
echo "   ✅ Better error handling"
echo ""

read -p "Would you like to run the new bootstrap script now? (y/N): " choice

case $choice in
    [Yy]* )
        echo "🚀 Running bootstrap script..."
        exec ./bootstrap.sh
        ;;
    * )
        echo "You can run it later with: ./bootstrap.sh"
        echo "📖 See README.md for more information"
        ;;
esac
