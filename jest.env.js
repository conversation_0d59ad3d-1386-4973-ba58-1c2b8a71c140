// Test environment variables
process.env.NODE_ENV = 'test'
process.env.DATABASE_URL = 'postgresql://test:test@localhost:5432/papernugget_test'
process.env.JWT_SECRET = 'test-jwt-secret-key-for-testing-only-must-be-at-least-32-characters'
process.env.NEXTAUTH_SECRET = 'test-nextauth-secret-for-testing-must-be-at-least-32-characters'
process.env.NEXTAUTH_URL = 'http://localhost:3000'
process.env.SMTP_HOST = 'localhost'
process.env.SMTP_PORT = '1025'
process.env.SMTP_USER = 'test'
process.env.SMTP_PASS = 'test'
process.env.FROM_EMAIL = '<EMAIL>'
