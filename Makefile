.PHONY: help bootstrap start stop build clean logs backup restore test

# Default target
help:
	@echo "PaperNugget Commands"
	@echo ""
	@echo "Quick Start:"
	@echo "  make bootstrap - One-command setup (recommended for new installations)"
	@echo ""
	@echo "Application:"
	@echo "  make start     - Start PaperNugget"
	@echo "  make stop      - Stop PaperNugget"
	@echo "  make logs      - View application logs"
	@echo "  make restart   - Restart PaperNugget"
	@echo ""
	@echo "Development:"
	@echo "  make test             - Run all tests"
	@echo "  make health           - Run health check"
	@echo "  make health-detailed  - Run detailed health check"
	@echo "  make email-test       - Test email system"
	@echo "  make email-test-send  - Test email system with test email"
	@echo "  make validate         - Run acceptance criteria validation"
	@echo "  make validate-quick   - Run quick validation (skip clone)"
	@echo "  make seed             - Seed test data"
	@echo ""
	@echo "Maintenance:"
	@echo "  make build        - Build images without starting"
	@echo "  make clean        - Remove containers and volumes"
	@echo "  make reset        - Complete reset (clean + bootstrap)"
	@echo "  make backup       - Create database backup"
	@echo "  make restore BACKUP_FILE=<file> - Restore database from backup"
	@echo "  make list-backups - List available backup files"

# Quick start command
bootstrap:
	@echo "🚀 Running PaperNugget bootstrap..."
	./bootstrap.sh

# Application commands
start:
	@echo "Starting PaperNugget..."
	docker compose up --build -d

stop:
	@echo "Stopping PaperNugget..."
	docker compose down

restart:
	@echo "Restarting PaperNugget..."
	docker compose down
	docker compose up --build -d

logs:
	@echo "Viewing application logs..."
	docker compose logs -f

# Development commands
test:
	@echo "Running tests..."
	docker compose exec app npm test

health:
	@echo "Running health check..."
	docker compose exec app npm run health

health-detailed:
	@echo "Running detailed health check..."
	docker compose exec app npm run health:detailed

email-test:
	@echo "Testing email system..."
	docker compose exec app npm run email:test

email-test-send:
	@echo "Testing email system with test email..."
	docker compose exec app npm run email:test-send

validate:
	@echo "Running acceptance criteria validation..."
	npm run validate

validate-quick:
	@echo "Running quick acceptance criteria validation..."
	npm run validate:quick

seed:
	@echo "Seeding test data..."
	docker compose exec app npm run seed:test-users

# Build commands
build:
	@echo "Building images..."
	docker compose build

# Maintenance commands
clean:
	@echo "Removing containers and volumes..."
	docker compose down -v --remove-orphans
	docker system prune -f

reset:
	@echo "Performing complete reset..."
	@$(MAKE) clean
	@$(MAKE) bootstrap

backup:
	@./scripts/backup-restore.sh backup

restore:
	@./scripts/backup-restore.sh restore $(BACKUP_FILE)

list-backups:
	@./scripts/backup-restore.sh list

# Setup commands
setup:
	@echo "Setting up environment..."
	@if [ ! -f .env ]; then \
		cp .env.example .env; \
		echo "Created .env file from .env.example"; \
		echo "Please review and update the .env file before starting the application"; \
	else \
		echo ".env file already exists"; \
	fi
