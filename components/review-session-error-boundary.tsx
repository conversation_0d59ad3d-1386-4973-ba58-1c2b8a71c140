"use client"

import React from "react"
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>it<PERSON> } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON><PERSON>riangle, RefreshCw, ArrowLeft } from "lucide-react"
import { logUIError } from "@/lib/utils"

interface ReviewSessionErrorBoundaryState {
  hasError: boolean
  error?: Error
}

interface ReviewSessionErrorBoundaryProps {
  children: React.ReactNode
  onReset?: () => void
  onGoBack?: () => void
}

export class ReviewSessionErrorBoundary extends React.Component<
  ReviewSessionErrorBoundaryProps,
  ReviewSessionErrorBoundaryState
> {
  constructor(props: ReviewSessionErrorBoundaryProps) {
    super(props)
    this.state = { hasError: false }
  }

  static getDerivedStateFromError(error: Error): ReviewSessionErrorBoundaryState {
    return { hasError: true, error }
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    // Log to server logs via our centralized error logging
    logUIError({
      component: 'ReviewSessionErrorBoundary',
      action: 'react_error_boundary',
      errorMessage: error.message,
      errorStack: error.stack,
      errorType: error.name,
      additionalContext: {
        componentStack: errorInfo.componentStack,
        errorBoundary: true
      }
    })

    // Keep console.error for development debugging
    console.error("Review session error:", error, errorInfo)
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="fixed inset-0 bg-background z-50 flex items-center justify-center p-4">
          <Card className="w-full max-w-md">
            <CardHeader className="text-center">
              <CardTitle className="flex items-center justify-center gap-2 text-destructive">
                <AlertTriangle className="h-6 w-6" />
                Review Session Error
              </CardTitle>
            </CardHeader>
            <CardContent className="text-center space-y-4">
              <p className="text-muted-foreground">
                Something went wrong during your review session. Your progress may have been saved.
              </p>
              
              {process.env.NODE_ENV === "development" && this.state.error && (
                <details className="text-left text-sm bg-muted p-2 rounded">
                  <summary className="cursor-pointer font-medium">Error Details</summary>
                  <pre className="mt-2 whitespace-pre-wrap">
                    {this.state.error.message}
                    {this.state.error.stack && `\n\n${this.state.error.stack}`}
                  </pre>
                </details>
              )}
              
              <div className="flex gap-2 justify-center">
                {this.props.onGoBack && (
                  <Button variant="outline" onClick={this.props.onGoBack}>
                    <ArrowLeft className="h-4 w-4 mr-2" />
                    Go Back
                  </Button>
                )}
                <Button
                  onClick={() => {
                    this.setState({ hasError: false, error: undefined })
                    this.props.onReset?.()
                  }}
                >
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Try Again
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      )
    }

    return this.props.children
  }
}
