'use client'

import React, { useState } from 'react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Mail, RefreshCw, CheckCircle, Clock } from 'lucide-react'
import { useToast } from '@/hooks/use-toast'

interface EmailVerificationPromptProps {
  email: string
  onBackToLogin?: () => void
}

export function EmailVerificationPrompt({ email, onBackToLogin }: EmailVerificationPromptProps) {
  const { toast } = useToast()
  const [isResending, setIsResending] = useState(false)
  const [lastSentTime, setLastSentTime] = useState<Date | null>(null)

  const handleResendVerification = async () => {
    setIsResending(true)
    try {
      const response = await fetch('/api/auth/resend-verification', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email }),
      })

      const data = await response.json()

      if (response.ok) {
        setLastSentTime(new Date())
        toast({
          title: 'Verification email sent',
          description: 'Please check your inbox for a new verification link.',
        })
      } else {
        toast({
          title: 'Failed to resend',
          description: data.error || 'Failed to resend verification email.',
          variant: 'destructive',
        })
      }
    } catch (error) {
      console.error('Resend verification error:', error)
      toast({
        title: 'Network error',
        description: 'Please check your connection and try again.',
        variant: 'destructive',
      })
    } finally {
      setIsResending(false)
    }
  }

  const getTimeAgo = (date: Date) => {
    const seconds = Math.floor((Date.now() - date.getTime()) / 1000)
    if (seconds < 60) return 'just now'
    const minutes = Math.floor(seconds / 60)
    if (minutes < 60) return `${minutes} minute${minutes > 1 ? 's' : ''} ago`
    const hours = Math.floor(minutes / 60)
    return `${hours} hour${hours > 1 ? 's' : ''} ago`
  }

  return (
    <div className="flex items-center justify-center min-h-screen p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="flex justify-center mb-4">
            <Mail className="h-16 w-16 text-blue-500" />
          </div>
          <CardTitle className="text-2xl">Check Your Email</CardTitle>
          <CardDescription>
            We've sent a verification link to your email address
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <Alert>
            <CheckCircle className="h-4 w-4" />
            <AlertDescription>
              A verification email has been sent to{' '}
              <span className="font-medium">{email}</span>
            </AlertDescription>
          </Alert>

          <div className="space-y-3">
            <div className="text-sm text-muted-foreground space-y-2">
              <p>• Click the verification link in your email to activate your account</p>
              <p>• The link will expire in 24 hours for security</p>
              <p>• Check your spam folder if you don't see the email</p>
            </div>
          </div>

          {lastSentTime && (
            <Alert>
              <Clock className="h-4 w-4" />
              <AlertDescription>
                Last verification email sent {getTimeAgo(lastSentTime)}
              </AlertDescription>
            </Alert>
          )}

          <div className="space-y-3">
            <p className="text-sm text-muted-foreground text-center">
              Didn't receive the email?
            </p>
            <Button
              onClick={handleResendVerification}
              disabled={isResending}
              variant="outline"
              className="w-full"
            >
              {isResending ? (
                <>
                  <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                  Sending...
                </>
              ) : (
                <>
                  <Mail className="mr-2 h-4 w-4" />
                  Resend Verification Email
                </>
              )}
            </Button>
          </div>

          <div className="pt-4 space-y-2">
            <Button
              onClick={onBackToLogin}
              variant="ghost"
              className="w-full"
            >
              Back to Sign In
            </Button>
            
            <p className="text-xs text-muted-foreground text-center">
              You'll need to verify your email before you can sign in
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
