'use client'

import React, { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { CheckCircle, XCircle, Clock, Mail, RefreshCw, AlertTriangle } from 'lucide-react'
import { useToast } from '@/hooks/use-toast'

interface EmailVerificationStatusProps {
  status: 'ok' | 'expired' | 'invalid' | 'already' | 'error'
  email?: string
  onBackToLogin?: () => void
}

export function EmailVerificationStatus({ status, email, onBackToLogin }: EmailVerificationStatusProps) {
  const { toast } = useToast()
  const [isResending, setIsResending] = useState(false)

  const handleResendVerification = async () => {
    if (!email) {
      toast({
        title: 'Error',
        description: 'Email address is required to resend verification.',
        variant: 'destructive',
      })
      return
    }

    setIsResending(true)
    try {
      const response = await fetch('/api/auth/resend-verification', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email }),
      })

      const data = await response.json()

      if (response.ok) {
        toast({
          title: 'Verification email sent',
          description: 'Please check your inbox for a new verification link.',
        })
      } else {
        toast({
          title: 'Failed to resend',
          description: data.error || 'Failed to resend verification email.',
          variant: 'destructive',
        })
      }
    } catch (error) {
      console.error('Resend verification error:', error)
      toast({
        title: 'Network error',
        description: 'Please check your connection and try again.',
        variant: 'destructive',
      })
    } finally {
      setIsResending(false)
    }
  }

  const getStatusConfig = () => {
    switch (status) {
      case 'ok':
        return {
          icon: <CheckCircle className="h-16 w-16 text-green-500" />,
          title: 'Email Verified Successfully!',
          description: 'Your email address has been verified. You can now sign in to your account.',
          variant: 'default' as const,
          showResend: false,
        }
      case 'expired':
        return {
          icon: <Clock className="h-16 w-16 text-orange-500" />,
          title: 'Verification Link Expired',
          description: 'Your verification link has expired for security reasons. Please request a new one.',
          variant: 'destructive' as const,
          showResend: true,
        }
      case 'invalid':
        return {
          icon: <XCircle className="h-16 w-16 text-red-500" />,
          title: 'Invalid Verification Link',
          description: 'This verification link is invalid or has already been used. Please request a new one.',
          variant: 'destructive' as const,
          showResend: true,
        }
      case 'already':
        return {
          icon: <CheckCircle className="h-16 w-16 text-blue-500" />,
          title: 'Already Verified',
          description: 'Your email address is already verified. You can sign in to your account.',
          variant: 'default' as const,
          showResend: false,
        }
      case 'error':
        return {
          icon: <AlertTriangle className="h-16 w-16 text-red-500" />,
          title: 'Verification Error',
          description: 'An error occurred during verification. Please try again or contact support.',
          variant: 'destructive' as const,
          showResend: true,
        }
      default:
        return {
          icon: <Mail className="h-16 w-16 text-gray-500" />,
          title: 'Email Verification',
          description: 'Please check your email for verification instructions.',
          variant: 'default' as const,
          showResend: false,
        }
    }
  }

  const config = getStatusConfig()

  return (
    <div className="flex items-center justify-center min-h-screen p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="flex justify-center mb-4">
            {config.icon}
          </div>
          <CardTitle className="text-2xl">{config.title}</CardTitle>
          <CardDescription>{config.description}</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {config.variant === 'destructive' && (
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                {config.description}
              </AlertDescription>
            </Alert>
          )}

          {config.showResend && email && (
            <div className="space-y-3">
              <p className="text-sm text-muted-foreground text-center">
                Need a new verification link?
              </p>
              <Button
                onClick={handleResendVerification}
                disabled={isResending}
                variant="outline"
                className="w-full"
              >
                {isResending ? (
                  <>
                    <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                    Sending...
                  </>
                ) : (
                  <>
                    <Mail className="mr-2 h-4 w-4" />
                    Resend Verification Email
                  </>
                )}
              </Button>
            </div>
          )}

          <div className="pt-4">
            <Button
              onClick={onBackToLogin}
              variant={status === 'ok' || status === 'already' ? 'default' : 'outline'}
              className="w-full"
            >
              {status === 'ok' || status === 'already' ? 'Sign In Now' : 'Back to Sign In'}
            </Button>
          </div>

          {email && (
            <p className="text-xs text-muted-foreground text-center">
              Verification email sent to: <span className="font-medium">{email}</span>
            </p>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
