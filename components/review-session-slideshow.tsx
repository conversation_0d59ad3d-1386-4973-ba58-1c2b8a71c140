"use client"

import { useState, useEffect, useCallback } from "react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { Badge } from "@/components/ui/badge"
import { 
  ChevronLeft, 
  ChevronRight, 
  X, 
  Play, 
  Pause, 
  RotateCcw,
  CheckCircle
} from "lucide-react"
import { useToast } from "@/hooks/use-toast"
import type { Paper, Note } from "@/lib/types"

interface ReviewSessionPaper extends Paper {
  note?: Note | null
}

interface ReviewSessionSlideshowProps {
  papers: ReviewSessionPaper[]
  sessionId: string
  onComplete: () => void
  onCancel: () => void
}

export function ReviewSessionSlideshow({ 
  papers, 
  sessionId, 
  onComplete, 
  onCancel 
}: ReviewSessionSlideshowProps) {
  const { toast } = useToast()
  const [currentIndex, setCurrentIndex] = useState(0)
  const [viewedPapers, setViewedPapers] = useState<Set<string>>(new Set())
  const [isAutoPlay, setIsAutoPlay] = useState(false)
  const [isCompleting, setIsCompleting] = useState(false)

  const currentPaper = papers[currentIndex]
  const progress = ((currentIndex + 1) / papers.length) * 100

  // Mark current paper as viewed
  useEffect(() => {
    if (currentPaper) {
      setViewedPapers(prev => new Set([...prev, currentPaper.id]))
    }
  }, [currentPaper])

  // Auto-play functionality
  useEffect(() => {
    if (!isAutoPlay) return

    const timer = setTimeout(() => {
      if (currentIndex < papers.length - 1) {
        setCurrentIndex(prev => prev + 1)
      } else {
        setIsAutoPlay(false)
      }
    }, 5000) // 5 seconds per slide

    return () => clearTimeout(timer)
  }, [currentIndex, isAutoPlay, papers.length])

  // Keyboard navigation
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      switch (e.key) {
        case "ArrowLeft":
        case "ArrowUp":
          e.preventDefault()
          goToPrevious()
          break
        case "ArrowRight":
        case "ArrowDown":
        case " ": // Space bar
          e.preventDefault()
          goToNext()
          break
        case "Escape":
          e.preventDefault()
          handleCancel()
          break
        case "Enter":
          e.preventDefault()
          if (currentIndex === papers.length - 1) {
            handleComplete()
          } else {
            goToNext()
          }
          break
        case "p":
          e.preventDefault()
          setIsAutoPlay(!isAutoPlay)
          break
        case "r":
          e.preventDefault()
          setCurrentIndex(0)
          break
      }
    }

    window.addEventListener("keydown", handleKeyDown)
    return () => window.removeEventListener("keydown", handleKeyDown)
  }, [currentIndex, papers.length, isAutoPlay])

  const goToNext = useCallback(() => {
    setCurrentIndex(prev => prev < papers.length - 1 ? prev + 1 : prev)
  }, [papers.length])

  const goToPrevious = useCallback(() => {
    setCurrentIndex(prev => prev > 0 ? prev - 1 : prev)
  }, [])

  const handleComplete = async () => {
    setIsCompleting(true)
    try {
      const response = await fetch("/api/review/session", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          sessionId,
          viewedPaperIds: Array.from(viewedPapers)
        })
      })

      if (response.ok) {
        const result = await response.json()
        toast({
          title: "Review session completed! 🎉",
          description: `Successfully reviewed ${result.summary.successful} papers`
        })
        onComplete()
      } else {
        throw new Error("Failed to complete session")
      }
    } catch (error) {
      console.error("Error completing review session:", error)
      toast({
        title: "Error completing session",
        description: "Please try again",
        variant: "destructive"
      })
    } finally {
      setIsCompleting(false)
    }
  }

  const handleCancel = () => {
    if (viewedPapers.size > 0) {
      if (confirm(`You've viewed ${viewedPapers.size} papers. Are you sure you want to cancel without saving progress?`)) {
        onCancel()
      }
    } else {
      onCancel()
    }
  }

  if (!currentPaper) {
    return null
  }

  return (
    <div
      className="fixed inset-0 bg-background z-50 flex flex-col"
      role="application"
      aria-label="Review Session"
    >
      {/* Header */}
      <div className="border-b p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <h1 className="text-xl font-semibold">Review Session</h1>
            <Badge variant="outline" aria-label={`Paper ${currentIndex + 1} of ${papers.length}`}>
              {currentIndex + 1} of {papers.length}
            </Badge>
          </div>
          
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setIsAutoPlay(!isAutoPlay)}
              aria-label={isAutoPlay ? "Pause auto-play" : "Start auto-play"}
            >
              {isAutoPlay ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentIndex(0)}
              aria-label="Go to first paper"
            >
              <RotateCcw className="h-4 w-4" />
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={handleCancel}
              aria-label="Cancel review session"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        </div>
        
        <div className="mt-4">
          <Progress value={progress} className="w-full" />
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex items-center justify-center p-8">
        <Card className="w-full max-w-4xl shadow-lg">
          <CardHeader className="text-center pb-6">
            <CardTitle className="text-3xl leading-tight mb-4">
              {currentPaper.title}
            </CardTitle>
            <div className="space-y-2">
              <p className="text-xl text-muted-foreground">
                {currentPaper.authors.join(", ")}
              </p>
              <div className="flex justify-center gap-4 text-sm text-muted-foreground">
                {currentPaper.year && <span>{currentPaper.year}</span>}
                {currentPaper.venue && <span>• {currentPaper.venue}</span>}
                {currentPaper.citationCount !== undefined && (
                  <span>• {currentPaper.citationCount} citations</span>
                )}
                {currentPaper.referenceCount !== undefined && (
                  <span>• {currentPaper.referenceCount} references</span>
                )}
              </div>
            </div>
          </CardHeader>

          <CardContent className="space-y-6">
            {currentPaper.note && (
              <>
                {/* Quick Summary */}
                {currentPaper.note.quickSummary && (
                  <div className="p-4 bg-blue-50 dark:bg-blue-950/30 rounded-lg border-l-4 border-blue-500">
                    <h3 className="text-lg font-semibold text-blue-900 dark:text-blue-100 mb-2">
                      📝 Quick Summary
                    </h3>
                    <p className="text-lg leading-relaxed text-blue-800 dark:text-blue-200">
                      {currentPaper.note.quickSummary}
                    </p>
                  </div>
                )}

                {/* Key Ideas */}
                {currentPaper.note.keyIdeas && currentPaper.note.keyIdeas.some(Boolean) && (
                  <div className="space-y-4">
                    <h3 className="text-lg font-semibold text-center">💡 Key Ideas</h3>
                    <div className="space-y-4">
                      {currentPaper.note.keyIdeas.filter(Boolean).map((idea, index) => (
                        <div key={index} className="flex items-start gap-4 p-4 bg-muted/30 rounded-lg">
                          <span className="bg-red-500 text-white rounded-full w-8 h-8 flex items-center justify-center text-lg font-bold shrink-0 mt-1">
                            {index + 1}
                          </span>
                          <p className="text-lg leading-relaxed">{idea}</p>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </>
            )}

            {!currentPaper.note && (
              <div className="text-center py-8 text-muted-foreground">
                <p className="text-lg">No notes available for this paper.</p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Navigation Footer */}
      <div className="border-t p-4">
        <div className="flex items-center justify-between">
          <Button
            variant="outline"
            onClick={goToPrevious}
            disabled={currentIndex === 0}
          >
            <ChevronLeft className="h-4 w-4 mr-2" />
            Previous
          </Button>

          <div className="flex items-center gap-2">
            <span className="text-sm text-muted-foreground">
              Use arrow keys or space to navigate
            </span>
          </div>

          {currentIndex === papers.length - 1 ? (
            <Button
              onClick={handleComplete}
              disabled={isCompleting}
              className="bg-green-600 hover:bg-green-700"
            >
              <CheckCircle className="h-4 w-4 mr-2" />
              {isCompleting ? "Completing..." : "Complete Session"}
            </Button>
          ) : (
            <Button onClick={goToNext}>
              Next
              <ChevronRight className="h-4 w-4 ml-2" />
            </Button>
          )}
        </div>
      </div>
    </div>
  )
}
