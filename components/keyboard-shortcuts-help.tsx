"use client"

import { useState } from "react"
import { Keyboard } from "lucide-react"
import { Button } from "@/components/ui/button"
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"

export function KeyboardShortcutsHelp() {
  const [open, setOpen] = useState(false)

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant="ghost" size="sm">
          <Keyboard className="h-4 w-4" />
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>Keyboard Shortcuts</DialogTitle>
        </DialogHeader>
        <div className="space-y-6">
          <div>
            <h3 className="font-semibold mb-2">Papers List</h3>
            <div className="space-y-1 text-sm">
              <div className="flex justify-between">
                <span>New paper</span>
                <kbd className="px-2 py-1 bg-muted rounded text-xs">N</kbd>
              </div>
              <div className="flex justify-between">
                <span>Clear filters</span>
                <kbd className="px-2 py-1 bg-muted rounded text-xs">ESC</kbd>
              </div>
              <div className="flex justify-between">
                <span>Star paper (on focused card)</span>
                <kbd className="px-2 py-1 bg-muted rounded text-xs">S</kbd>
              </div>
              <div className="flex justify-between">
                <span>Edit paper (on focused card)</span>
                <kbd className="px-2 py-1 bg-muted rounded text-xs">E</kbd>
              </div>
              <div className="flex justify-between">
                <span>Delete paper (on focused card)</span>
                <kbd className="px-2 py-1 bg-muted rounded text-xs">Shift + Del</kbd>
              </div>
            </div>
          </div>

          <div>
            <h3 className="font-semibold mb-2">Paper Details</h3>
            <div className="space-y-1 text-sm">
              <div className="flex justify-between">
                <span>Star paper</span>
                <kbd className="px-2 py-1 bg-muted rounded text-xs">S</kbd>
              </div>
              <div className="flex justify-between">
                <span>Add/remove from review</span>
                <kbd className="px-2 py-1 bg-muted rounded text-xs">R</kbd>
              </div>
              <div className="flex justify-between">
                <span>Edit (focus title)</span>
                <kbd className="px-2 py-1 bg-muted rounded text-xs">E</kbd>
              </div>
              <div className="flex justify-between">
                <span>Back to papers</span>
                <kbd className="px-2 py-1 bg-muted rounded text-xs">ESC</kbd>
              </div>
              <div className="flex justify-between">
                <span>Delete paper</span>
                <kbd className="px-2 py-1 bg-muted rounded text-xs">Shift + Del</kbd>
              </div>
              <div className="flex justify-between">
                <span>Focus bullet point</span>
                <kbd className="px-2 py-1 bg-muted rounded text-xs">1-6</kbd>
              </div>
            </div>
          </div>

          <div>
            <h3 className="font-semibold mb-2">Collections</h3>
            <div className="space-y-1 text-sm">
              <div className="flex justify-between">
                <span>New collection</span>
                <kbd className="px-2 py-1 bg-muted rounded text-xs">N</kbd>
              </div>
              <div className="flex justify-between">
                <span>Present collection (on focused card)</span>
                <kbd className="px-2 py-1 bg-muted rounded text-xs">P</kbd>
              </div>
              <div className="flex justify-between">
                <span>Delete collection (on focused card)</span>
                <kbd className="px-2 py-1 bg-muted rounded text-xs">Shift + Del</kbd>
              </div>
            </div>
          </div>

          <div>
            <h3 className="font-semibold mb-2">Collection Details</h3>
            <div className="space-y-1 text-sm">
              <div className="flex justify-between">
                <span>Edit collection name</span>
                <kbd className="px-2 py-1 bg-muted rounded text-xs">E</kbd>
              </div>
              <div className="flex justify-between">
                <span>Add/remove from review</span>
                <kbd className="px-2 py-1 bg-muted rounded text-xs">R</kbd>
              </div>
              <div className="flex justify-between">
                <span>Present collection</span>
                <kbd className="px-2 py-1 bg-muted rounded text-xs">P</kbd>
              </div>
              <div className="flex justify-between">
                <span>Back to collections</span>
                <kbd className="px-2 py-1 bg-muted rounded text-xs">ESC</kbd>
              </div>
              <div className="flex justify-between">
                <span>Delete collection</span>
                <kbd className="px-2 py-1 bg-muted rounded text-xs">Shift + Del</kbd>
              </div>
            </div>
          </div>

          <div>
            <h3 className="font-semibold mb-2">Review Page</h3>
            <div className="space-y-1 text-sm">
              <div className="flex justify-between">
                <span>Mark as reviewed</span>
                <kbd className="px-2 py-1 bg-muted rounded text-xs">G</kbd>
              </div>
              <div className="flex justify-between">
                <span>Mark for revisit</span>
                <kbd className="px-2 py-1 bg-muted rounded text-xs">R</kbd>
              </div>
              <div className="flex justify-between">
                <span>Previous paper</span>
                <kbd className="px-2 py-1 bg-muted rounded text-xs">← / H</kbd>
              </div>
              <div className="flex justify-between">
                <span>Next paper</span>
                <kbd className="px-2 py-1 bg-muted rounded text-xs">→ / L</kbd>
              </div>
              <div className="flex justify-between">
                <span>Exit review</span>
                <kbd className="px-2 py-1 bg-muted rounded text-xs">ESC</kbd>
              </div>
            </div>
          </div>

          <div className="text-xs text-muted-foreground">
            <p>💡 Tip: Use Tab to navigate between focusable elements, then use the shortcuts above.</p>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
