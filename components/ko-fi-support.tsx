"use client"

import { Coffee, ExternalLink } from "lucide-react"
import { Button } from "@/components/ui/button"
import { useConfig } from "@/hooks/use-config"

interface KofiSupportProps {
  variant?: "default" | "outline" | "ghost" | "link"
  size?: "default" | "sm" | "lg" | "icon"
  className?: string
  showIcon?: boolean
  showText?: boolean
}

/**
 * Ko-fi support button component
 * Only renders when KO_FI_URL is configured
 * Opens Ko-fi link in new tab with proper security attributes
 */
export function KofiSupport({ 
  variant = "outline", 
  size = "sm", 
  className = "",
  showIcon = true,
  showText = true
}: KofiSupportProps) {
  const { config, loading, error } = useConfig()

  // Don't render anything while loading or if there's no Ko-fi URL configured
  if (loading || error || !config?.kofiUrl) {
    return null
  }

  const handleClick = () => {
    if (config.kofiUrl) {
      window.open(config.kofiUrl, '_blank', 'noopener,noreferrer')
    }
  }

  return (
    <Button
      variant={variant}
      size={size}
      onClick={handleClick}
      className={`transition-all hover:scale-105 ${className}`}
      aria-label="Support this project on Ko-fi"
      title="Support this project on Ko-fi"
    >
      {showIcon && <Coffee className="h-4 w-4" />}
      {showText && (
        <span className="flex items-center gap-1">
          Support Project
          <ExternalLink className="h-3 w-3" />
        </span>
      )}
    </Button>
  )
}

/**
 * Compact Ko-fi support button for tight spaces
 */
export function KofiSupportCompact({ className = "" }: { className?: string }) {
  return (
    <KofiSupport
      variant="ghost"
      size="sm"
      showText={false}
      className={`h-8 w-8 p-0 ${className}`}
    />
  )
}

/**
 * Ko-fi support link for text-based contexts
 */
export function KofiSupportLink({ className = "" }: { className?: string }) {
  const { config, loading, error } = useConfig()

  if (loading || error || !config?.kofiUrl) {
    return null
  }

  return (
    <a
      href={config.kofiUrl}
      target="_blank"
      rel="noopener noreferrer"
      className={`inline-flex items-center gap-1 text-sm text-muted-foreground hover:text-foreground transition-colors ${className}`}
      aria-label="Support this project on Ko-fi"
    >
      <Coffee className="h-3 w-3" />
      Support Project
      <ExternalLink className="h-3 w-3" />
    </a>
  )
}
