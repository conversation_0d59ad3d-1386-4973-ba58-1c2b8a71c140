'use client'

import React, { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Switch } from '@/components/ui/switch'
import { Separator } from '@/components/ui/separator'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Loader2, Save, User, Shield, Key, Download, Trash2, Link, RefreshCw, CheckCircle, AlertCircle, Eye, EyeOff } from 'lucide-react'
import { useAuth } from '@/lib/auth-context'
import { useToast } from '@/hooks/use-toast'
import { authenticatedFetch } from '@/lib/utils'

interface UserProfile {
  id: string
  email: string
  displayName: string
  role: string
  emailVerified: boolean
  isActive: boolean
  preferences: Record<string, any>
  privacySettings: Record<string, any>
  createdAt: string
  lastLogin?: string
}

export function UserSettings() {
  const { user, refreshUser } = useAuth()
  const { toast } = useToast()

  // Helper function to check if a string is a masked API key (all asterisks)
  const isMaskedApiKey = (value: string): boolean => {
    return /^\*+$/.test(value)
  }

  const [profile, setProfile] = useState<UserProfile | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [isSaving, setIsSaving] = useState(false)
  const [error, setError] = useState('')

  // Form states
  const [displayName, setDisplayName] = useState('')
  const [currentPassword, setCurrentPassword] = useState('')
  const [newPassword, setNewPassword] = useState('')
  const [confirmPassword, setConfirmPassword] = useState('')

  // Privacy settings
  const [profilePublic, setProfilePublic] = useState(false)
  const [allowDataCollection, setAllowDataCollection] = useState(true)
  const [emailNotifications, setEmailNotifications] = useState(true)

  // Zotero settings
  const [zoteroApiKey, setZoteroApiKey] = useState('')
  const [hasStoredApiKey, setHasStoredApiKey] = useState(false)
  const [zoteroLibraryType, setZoteroLibraryType] = useState<'user' | 'group'>('user')
  const [zoteroLibraryId, setZoteroLibraryId] = useState('')
  const [zoteroEnabled, setZoteroEnabled] = useState(false)
  const [zoteroLibraries, setZoteroLibraries] = useState<any[]>([])
  const [isTestingConnection, setIsTestingConnection] = useState(false)
  const [connectionStatus, setConnectionStatus] = useState<'idle' | 'success' | 'error'>('idle')
  const [isLoadingLibraries, setIsLoadingLibraries] = useState(false)
  const [showApiKey, setShowApiKey] = useState(false)
  const [isLoadingApiKey, setIsLoadingApiKey] = useState(false)
  const [isSyncingAll, setIsSyncingAll] = useState(false)
  const [syncResults, setSyncResults] = useState<{
    successful: string[]
    failed: Array<{ paperId: string; error: string }>
    total: number
  } | null>(null)

  useEffect(() => {
    fetchProfile()
  }, [])

  // Auto-load libraries when switching to group type and API key is available
  useEffect(() => {
    if (zoteroLibraryType === 'group' && zoteroApiKey && zoteroApiKey !== '***' && zoteroApiKey.trim() !== '') {
      loadZoteroLibraries()
    }
  }, [zoteroLibraryType, zoteroApiKey])

  const fetchProfile = async () => {
    try {
      const response = await fetch('/api/user/profile')
      if (response.ok) {
        let data
        try {
          data = await response.json()
        } catch (jsonError) {
          console.error('Failed to parse JSON response:', jsonError)
          setError('Invalid response from server')
          return
        }

        setProfile(data)
        setDisplayName(data.displayName || '')

        // Set privacy settings from profile
        const privacy = data.privacySettings || {}
        setProfilePublic(privacy.profilePublic || false)
        setAllowDataCollection(privacy.allowDataCollection !== false)
        setEmailNotifications(privacy.emailNotifications !== false)

        // Set Zotero settings from profile
        const zotero = data.preferences?.zotero || {}
        const hasApiKey = !!zotero.apiKey
        setHasStoredApiKey(hasApiKey)
        // Use the actual masked API key from server (preserves length)
        setZoteroApiKey(zotero.apiKey || '')
        setZoteroLibraryType(zotero.libraryType || 'user')
        setZoteroLibraryId(zotero.libraryId || '')
        setZoteroEnabled(zotero.enabled || false)
      } else {
        setError('Failed to load profile')
      }
    } catch (error) {
      console.error('Error fetching profile:', error)
      setError('Network error')
    } finally {
      setIsLoading(false)
    }
  }

  const handleProfileUpdate = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSaving(true)
    setError('')

    try {
      const response = await fetch('/api/user/profile', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          displayName,
          privacySettings: {
            profilePublic,
            allowDataCollection,
            emailNotifications,
          },
        }),
      })

      const data = await response.json()

      if (response.ok) {
        setProfile(data)
        await refreshUser()
        toast({
          title: 'Profile updated',
          description: 'Your profile has been updated successfully.',
        })
      } else {
        setError(data.error || 'Failed to update profile')
      }
    } catch (error) {
      console.error('Error updating profile:', error)
      setError('Network error')
    } finally {
      setIsSaving(false)
    }
  }

  const handlePasswordChange = async (e: React.FormEvent) => {
    e.preventDefault()

    if (newPassword !== confirmPassword) {
      setError('New passwords do not match')
      return
    }

    if (newPassword.length < 8) {
      setError('New password must be at least 8 characters long')
      return
    }

    setIsSaving(true)
    setError('')

    try {
      const response = await fetch('/api/auth/change-password', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          currentPassword,
          newPassword,
        }),
      })

      const data = await response.json()

      if (response.ok) {
        setCurrentPassword('')
        setNewPassword('')
        setConfirmPassword('')
        toast({
          title: 'Password changed',
          description: 'Your password has been changed successfully.',
        })
      } else {
        setError(data.error || 'Failed to change password')
      }
    } catch (error) {
      console.error('Error changing password:', error)
      setError('Network error')
    } finally {
      setIsSaving(false)
    }
  }

  const handleZoteroSettingsUpdate = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSaving(true)
    setError('')

    try {
      const response = await authenticatedFetch('/api/user/zotero-settings', {
        method: 'PUT',
        body: JSON.stringify({
          apiKey: zoteroApiKey,
          libraryType: zoteroLibraryType,
          libraryId: zoteroLibraryId,
          enabled: zoteroEnabled,
        }),
      })

      let data
      try {
        data = await response.json()
      } catch (jsonError) {
        console.error('Failed to parse JSON response:', jsonError)
        setError('Invalid response from server')
        setConnectionStatus('error')
        return
      }

      if (response.ok) {
        // Check connection test result
        const connectionTest = data.data?.connectionTest

        if (connectionTest === true) {
          toast({
            title: 'Zotero settings updated',
            description: 'Your Zotero settings have been updated and connection test was successful.',
          })
          setConnectionStatus('success')
        } else if (connectionTest === false) {
          toast({
            title: 'Zotero settings updated',
            description: 'Settings saved, but connection test failed. Please verify your API key.',
            variant: 'destructive'
          })
          setConnectionStatus('error')
        } else {
          toast({
            title: 'Zotero settings updated',
            description: 'Your Zotero settings have been updated successfully.',
          })
          setConnectionStatus('success')
        }
      } else {
        setError(data.error || 'Failed to update Zotero settings')
        setConnectionStatus('error')
      }
    } catch (error) {
      console.error('Error updating Zotero settings:', error)
      setError('Network error')
      setConnectionStatus('error')
    } finally {
      setIsSaving(false)
    }
  }

  const testZoteroConnection = async () => {
    if (!zoteroApiKey || isMaskedApiKey(zoteroApiKey)) {
      setError('Please enter a valid API key')
      return
    }

    setIsTestingConnection(true)
    setError('')

    try {
      const response = await authenticatedFetch('/api/user/zotero-settings', {
        method: 'PUT',
        body: JSON.stringify({
          apiKey: zoteroApiKey,
          libraryType: zoteroLibraryType,
          libraryId: zoteroLibraryId,
          enabled: false, // Just test, don't enable yet
        }),
      })

      let data
      try {
        data = await response.json()
      } catch (jsonError) {
        console.error('Failed to parse JSON response:', jsonError)
        setConnectionStatus('error')
        setError('Invalid response from server')
        return
      }

      if (response.ok) {
        setConnectionStatus('success')
        toast({
          title: 'Connection successful',
          description: 'Successfully connected to Zotero.',
        })
        // Load libraries after successful connection
        loadZoteroLibraries()
      } else {
        setConnectionStatus('error')
        setError(data.error || 'Failed to connect to Zotero')
      }
    } catch (error) {
      console.error('Error testing Zotero connection:', error)
      setConnectionStatus('error')
      setError('Network error')
    } finally {
      setIsTestingConnection(false)
    }
  }

  const loadZoteroLibraries = async () => {
    setIsLoadingLibraries(true)
    setError('')
    try {
      const response = await authenticatedFetch('/api/user/zotero-libraries')
      if (response.ok) {
        const data = await response.json()
        setZoteroLibraries(data.data || [])
      } else {
        const errorData = await response.json().catch(() => ({ error: 'Unknown error' }))
        const errorMessage = errorData.error || `Failed to load Zotero libraries (${response.status})`
        console.error('Failed to load Zotero libraries:', errorMessage)
        setError(`Failed to load libraries: ${errorMessage}`)
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Network error'
      console.error('Error loading Zotero libraries:', error)
      setError(`Error loading libraries: ${errorMessage}`)
    } finally {
      setIsLoadingLibraries(false)
    }
  }

  const toggleApiKeyVisibility = async () => {
    if (!showApiKey && hasStoredApiKey && isMaskedApiKey(zoteroApiKey)) {
      // Need to fetch the actual API key from the server
      setIsLoadingApiKey(true)
      try {
        const response = await authenticatedFetch('/api/user/zotero-settings?showActual=true')
        if (response.ok) {
          const data = await response.json()
          if (data.data && data.data.apiKey && !isMaskedApiKey(data.data.apiKey)) {
            setZoteroApiKey(data.data.apiKey)
          }
        }
      } catch (error) {
        console.error('Error fetching actual API key:', error)
        setError('Failed to fetch API key')
        return
      } finally {
        setIsLoadingApiKey(false)
      }
    } else if (showApiKey && hasStoredApiKey) {
      // Hide the key - return to masked state
      setIsLoadingApiKey(true)
      try {
        const response = await authenticatedFetch('/api/user/zotero-settings')
        if (response.ok) {
          const data = await response.json()
          if (data.data && data.data.apiKey) {
            setZoteroApiKey(data.data.apiKey) // This will be the masked version
          }
        }
      } finally {
        setIsLoadingApiKey(false)
      }
    }
    setShowApiKey(!showApiKey)
  }

  const syncAllChangedPapers = async () => {
    setIsSyncingAll(true)
    setSyncResults(null)
    setError('')

    try {
      const response = await authenticatedFetch('/api/papers/sync-zotero', {
        method: 'POST',
        body: JSON.stringify({
          mode: 'changed'
        }),
      })

      let data
      try {
        data = await response.json()
      } catch (jsonError) {
        console.error('Failed to parse JSON response:', jsonError)
        throw new Error('Invalid response format from server')
      }

      if (response.ok) {
        const results = data.data
        setSyncResults(results)

        toast({
          title: 'Sync completed',
          description: `${results.successful.length} papers synced successfully, ${results.failed.length} failed`,
        })
      } else {
        const errorMessage = data.errors?.[0] || data.error || 'Failed to sync papers to Zotero'
        setError(errorMessage)
        toast({
          title: 'Sync failed',
          description: errorMessage,
          variant: 'destructive'
        })
      }
    } catch (error) {
      console.error('Error syncing papers to Zotero:', error)
      setError('Network error occurred while syncing to Zotero')
      toast({
        title: 'Sync failed',
        description: 'Network error occurred while syncing to Zotero',
        variant: 'destructive'
      })
    } finally {
      setIsSyncingAll(false)
    }
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    )
  }

  if (!profile) {
    return (
      <Alert variant="destructive">
        <AlertDescription>Failed to load user profile</AlertDescription>
      </Alert>
    )
  }

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      <div>
        <h1 className="text-3xl font-bold">Account Settings</h1>
        <p className="text-muted-foreground">
          Manage your account settings and preferences
        </p>
      </div>

      <Tabs defaultValue="profile" className="space-y-6">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="profile">
            <User className="mr-2 h-4 w-4" />
            Profile
          </TabsTrigger>
          <TabsTrigger value="security">
            <Key className="mr-2 h-4 w-4" />
            Security
          </TabsTrigger>
          <TabsTrigger value="privacy">
            <Shield className="mr-2 h-4 w-4" />
            Privacy
          </TabsTrigger>
          <TabsTrigger value="zotero">
            <Link className="mr-2 h-4 w-4" />
            Zotero
          </TabsTrigger>
          <TabsTrigger value="data">
            <Download className="mr-2 h-4 w-4" />
            Data
          </TabsTrigger>
        </TabsList>

        <TabsContent value="profile" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Profile Information</CardTitle>
              <CardDescription>
                Update your personal information and display preferences
              </CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleProfileUpdate} className="space-y-4">
                {error && (
                  <Alert variant="destructive">
                    <AlertDescription>{error}</AlertDescription>
                  </Alert>
                )}

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="email">Email</Label>
                    <Input
                      id="email"
                      type="email"
                      value={profile.email}
                      disabled
                      className="bg-muted"
                    />
                    <p className="text-xs text-muted-foreground">
                      Email cannot be changed. Contact support if needed.
                    </p>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="displayName">Display Name</Label>
                    <Input
                      id="displayName"
                      value={displayName}
                      onChange={(e) => setDisplayName(e.target.value)}
                      placeholder="Enter your display name"
                      disabled={isSaving}
                    />
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label>Account Status</Label>
                    <div className="flex items-center space-x-2">
                      <div className={`w-2 h-2 rounded-full ${profile.isActive ? 'bg-green-500' : 'bg-red-500'}`} />
                      <span className="text-sm">
                        {profile.isActive ? 'Active' : 'Inactive'}
                      </span>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label>Email Verification</Label>
                    <div className="flex items-center space-x-2">
                      <div className={`w-2 h-2 rounded-full ${profile.emailVerified ? 'bg-green-500' : 'bg-yellow-500'}`} />
                      <span className="text-sm">
                        {profile.emailVerified ? 'Verified' : 'Pending verification'}
                      </span>
                    </div>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label>Role</Label>
                    <Input value={profile.role} disabled className="bg-muted" />
                  </div>

                  <div className="space-y-2">
                    <Label>Member Since</Label>
                    <Input
                      value={new Date(profile.createdAt).toLocaleDateString()}
                      disabled
                      className="bg-muted"
                    />
                  </div>
                </div>

                <Button type="submit" disabled={isSaving}>
                  {isSaving ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Saving...
                    </>
                  ) : (
                    <>
                      <Save className="mr-2 h-4 w-4" />
                      Save Changes
                    </>
                  )}
                </Button>
              </form>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="security" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Change Password</CardTitle>
              <CardDescription>
                Update your password to keep your account secure
              </CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handlePasswordChange} className="space-y-4">
                {error && (
                  <Alert variant="destructive">
                    <AlertDescription>{error}</AlertDescription>
                  </Alert>
                )}

                <div className="space-y-2">
                  <Label htmlFor="currentPassword">Current Password</Label>
                  <Input
                    id="currentPassword"
                    type="password"
                    value={currentPassword}
                    onChange={(e) => setCurrentPassword(e.target.value)}
                    disabled={isSaving}
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="newPassword">New Password</Label>
                  <Input
                    id="newPassword"
                    type="password"
                    value={newPassword}
                    onChange={(e) => setNewPassword(e.target.value)}
                    disabled={isSaving}
                    required
                  />
                  <p className="text-xs text-muted-foreground">
                    Password must be at least 8 characters long
                  </p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="confirmPassword">Confirm New Password</Label>
                  <Input
                    id="confirmPassword"
                    type="password"
                    value={confirmPassword}
                    onChange={(e) => setConfirmPassword(e.target.value)}
                    disabled={isSaving}
                    required
                  />
                </div>

                <Button type="submit" disabled={isSaving}>
                  {isSaving ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Changing Password...
                    </>
                  ) : (
                    <>
                      <Key className="mr-2 h-4 w-4" />
                      Change Password
                    </>
                  )}
                </Button>
              </form>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Security Information</CardTitle>
              <CardDescription>
                View your account security details
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex justify-between items-center">
                <div>
                  <p className="font-medium">Last Login</p>
                  <p className="text-sm text-muted-foreground">
                    {profile.lastLogin
                      ? new Date(profile.lastLogin).toLocaleString()
                      : 'Never'
                    }
                  </p>
                </div>
              </div>

              <Separator />

              <div className="flex justify-between items-center">
                <div>
                  <p className="font-medium">Two-Factor Authentication</p>
                  <p className="text-sm text-muted-foreground">
                    Add an extra layer of security to your account
                  </p>
                </div>
                <Button variant="outline" disabled>
                  Coming Soon
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="privacy" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Privacy Settings</CardTitle>
              <CardDescription>
                Control how your data is used and shared
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Public Profile</Label>
                  <p className="text-sm text-muted-foreground">
                    Allow others to see your profile information
                  </p>
                </div>
                <Switch
                  checked={profilePublic}
                  onCheckedChange={setProfilePublic}
                />
              </div>

              <Separator />

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Data Collection</Label>
                  <p className="text-sm text-muted-foreground">
                    Allow collection of usage data to improve the service
                  </p>
                </div>
                <Switch
                  checked={allowDataCollection}
                  onCheckedChange={setAllowDataCollection}
                />
              </div>

              <Separator />

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Email Notifications</Label>
                  <p className="text-sm text-muted-foreground">
                    Receive email notifications about your account
                  </p>
                </div>
                <Switch
                  checked={emailNotifications}
                  onCheckedChange={setEmailNotifications}
                />
              </div>

              <Button onClick={handleProfileUpdate} disabled={isSaving}>
                {isSaving ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Saving...
                  </>
                ) : (
                  <>
                    <Save className="mr-2 h-4 w-4" />
                    Save Privacy Settings
                  </>
                )}
              </Button>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="zotero" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Zotero Integration</CardTitle>
              <CardDescription>
                Connect your Zotero account to sync papers and notes
              </CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleZoteroSettingsUpdate} className="space-y-6">
                {error && (
                  <Alert variant="destructive">
                    <AlertDescription>{error}</AlertDescription>
                  </Alert>
                )}

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>Enable Zotero Sync</Label>
                    <p className="text-sm text-muted-foreground">
                      Sync your papers and notes to Zotero
                    </p>
                  </div>
                  <Switch
                    checked={zoteroEnabled}
                    onCheckedChange={setZoteroEnabled}
                  />
                </div>

                <Separator />

                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="zoteroApiKey">Zotero API Key</Label>
                    <div className="flex gap-2">
                      <div className="relative flex-1">
                        <Input
                          id="zoteroApiKey"
                          type={showApiKey ? "text" : "password"}
                          value={zoteroApiKey}
                          onChange={(e) => {
                            const newValue = e.target.value
                            setZoteroApiKey(newValue)
                            // If user starts typing and it's not the masked value, reset stored key flag
                            if (!isMaskedApiKey(newValue) && newValue !== '') {
                              setHasStoredApiKey(false)
                            }
                          }}
                          placeholder={hasStoredApiKey && isMaskedApiKey(zoteroApiKey) ? "API key is stored (clear to enter new)" : "Enter your Zotero API key"}
                          disabled={isSaving || isTestingConnection}
                          className="pr-10"
                        />
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                          onClick={toggleApiKeyVisibility}
                          disabled={isSaving || isTestingConnection || isLoadingApiKey}
                          title="Toggle API key visibility"
                        >
                          {isLoadingApiKey ? (
                            <Loader2 className="h-4 w-4 animate-spin text-muted-foreground" />
                          ) : showApiKey ? (
                            <EyeOff className="h-4 w-4 text-muted-foreground" />
                          ) : (
                            <Eye className="h-4 w-4 text-muted-foreground" />
                          )}
                        </Button>
                      </div>
                      <Button
                        type="button"
                        variant="outline"
                        onClick={testZoteroConnection}
                        disabled={!zoteroApiKey || isMaskedApiKey(zoteroApiKey) || isTestingConnection}
                      >
                        {isTestingConnection ? (
                          <Loader2 className="h-4 w-4 animate-spin" />
                        ) : connectionStatus === 'success' ? (
                          <CheckCircle className="h-4 w-4 text-green-600" />
                        ) : connectionStatus === 'error' ? (
                          <AlertCircle className="h-4 w-4 text-red-600" />
                        ) : (
                          <RefreshCw className="h-4 w-4" />
                        )}
                      </Button>
                    </div>
                    <p className="text-xs text-muted-foreground">
                      Get your API key from{' '}
                      <a
                        href="https://www.zotero.org/settings/keys"
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-blue-600 hover:underline"
                      >
                        Zotero Settings
                      </a>
                    </p>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="libraryType">Library Type</Label>
                    <Select
                      value={zoteroLibraryType}
                      onValueChange={(value) => setZoteroLibraryType(value as 'user' | 'group')}
                      disabled={isSaving}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select library type" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="user">Personal Library</SelectItem>
                        <SelectItem value="group">Group Library</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  {zoteroLibraryType === 'group' && (
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <Label htmlFor="libraryId">Group Library</Label>
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          onClick={loadZoteroLibraries}
                          disabled={isLoadingLibraries || !zoteroApiKey || isMaskedApiKey(zoteroApiKey)}
                          className="h-6 px-2"
                        >
                          <RefreshCw className={`h-3 w-3 ${isLoadingLibraries ? 'animate-spin' : ''}`} />
                        </Button>
                      </div>
                      {isLoadingLibraries ? (
                        <div className="flex items-center gap-2">
                          <Loader2 className="h-4 w-4 animate-spin" />
                          <span className="text-sm text-muted-foreground">Loading libraries...</span>
                        </div>
                      ) : (
                        <>
                          {zoteroLibraries.filter(lib => lib.type === 'group').length === 0 ? (
                            <div className="text-sm text-muted-foreground p-2 border rounded">
                              No group libraries found. Make sure your API key has access to group libraries.
                            </div>
                          ) : (
                            <Select
                              value={zoteroLibraryId}
                              onValueChange={setZoteroLibraryId}
                              disabled={isSaving}
                            >
                              <SelectTrigger>
                                <SelectValue placeholder="Select a group library" />
                              </SelectTrigger>
                              <SelectContent>
                                {zoteroLibraries
                                  .filter(lib => lib.type === 'group')
                                  .map((library) => (
                                    <SelectItem key={library.id} value={library.id}>
                                      {library.name}
                                      {library.description && (
                                        <span className="text-xs text-muted-foreground ml-2">
                                          - {library.description}
                                        </span>
                                      )}
                                    </SelectItem>
                                  ))}
                              </SelectContent>
                            </Select>
                          )}
                        </>
                      )}
                    </div>
                  )}
                </div>

                <div className="flex gap-2">
                  <Button type="submit" disabled={isSaving}>
                    {isSaving ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Saving...
                      </>
                    ) : (
                      <>
                        <Save className="mr-2 h-4 w-4" />
                        Save Zotero Settings
                      </>
                    )}
                  </Button>

                  {zoteroEnabled && (
                    <Button
                      type="button"
                      variant="outline"
                      onClick={syncAllChangedPapers}
                      disabled={isSyncingAll || !zoteroEnabled}
                      className="text-purple-600 border-purple-200 hover:bg-purple-50"
                    >
                      {isSyncingAll ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          Syncing...
                        </>
                      ) : (
                        <>
                          <RefreshCw className="mr-2 h-4 w-4" />
                          Sync All Changed
                        </>
                      )}
                    </Button>
                  )}
                </div>
              </form>

              {syncResults && (
                <div className="mt-6 p-4 border rounded-lg">
                  <h4 className="font-medium mb-2">Sync Results</h4>
                  <div className="space-y-2 text-sm">
                    <div className="flex items-center gap-2">
                      <CheckCircle className="h-4 w-4 text-green-600" />
                      <span>{syncResults.successful.length} papers synced successfully</span>
                    </div>
                    {syncResults.failed.length > 0 && (
                      <div className="flex items-center gap-2">
                        <AlertCircle className="h-4 w-4 text-red-600" />
                        <span>{syncResults.failed.length} papers failed to sync</span>
                      </div>
                    )}
                    <div className="text-muted-foreground">
                      Total: {syncResults.total} papers processed
                    </div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="data" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Data Management</CardTitle>
              <CardDescription>
                Export or delete your account data
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <div>
                  <h4 className="font-medium">Export Your Data</h4>
                  <p className="text-sm text-muted-foreground mb-4">
                    Download a copy of all your data including papers, notes, and collections.
                  </p>
                  <Button variant="outline" disabled>
                    <Download className="mr-2 h-4 w-4" />
                    Export Data (Coming Soon)
                  </Button>
                </div>

                <Separator />

                <div>
                  <h4 className="font-medium text-destructive">Delete Account</h4>
                  <p className="text-sm text-muted-foreground mb-4">
                    Permanently delete your account and all associated data. This action cannot be undone.
                  </p>
                  <Button variant="destructive" disabled>
                    <Trash2 className="mr-2 h-4 w-4" />
                    Delete Account (Coming Soon)
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}