"use client"

import { Card, CardContent, CardHeader } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import type { Paper, Note } from "@/lib/types"

interface PaperCompactViewProps {
  paper: Paper
  note?: Note | null
  className?: string
}

export function PaperCompactView({ paper, note, className }: PaperCompactViewProps) {
  return (
    <Card className={`hover:shadow-md transition-shadow ${className}`}>
      <CardHeader className="pb-3">
        <div className="space-y-2">
          <h3 className="text-lg font-semibold leading-tight line-clamp-2">
            {paper.title}
          </h3>
          <p className="text-sm text-muted-foreground">
            {(paper.authors || []).join(", ")}
          </p>
          <div className="flex items-center gap-4 text-xs text-muted-foreground">
            {paper.year && <span>{paper.year}</span>}
            {paper.venue && <span>• {paper.venue}</span>}
            {paper.citationCount !== undefined && (
              <span>• {paper.citationCount} citations</span>
            )}
            {paper.referenceCount !== undefined && (
              <span>• {paper.referenceCount} references</span>
            )}
          </div>
        </div>
      </CardHeader>

      <CardContent className="pt-0">
        {note && (
          <div className="space-y-3">
            {/* Quick Summary */}
            {note.quickSummary && (
              <div className="p-3 bg-blue-50 dark:bg-blue-950/30 rounded-md border-l-4 border-blue-500">
                <h4 className="text-sm font-medium text-blue-900 dark:text-blue-100 mb-1">
                  📝 Quick Summary
                </h4>
                <p className="text-sm text-blue-800 dark:text-blue-200 leading-relaxed">
                  {note.quickSummary}
                </p>
              </div>
            )}

            {/* Key Ideas */}
            {note.keyIdeas && note.keyIdeas.some(Boolean) && (
              <div className="space-y-2">
                <h4 className="text-sm font-medium text-muted-foreground">💡 Key Ideas</h4>
                <div className="space-y-2">
                  {note.keyIdeas.filter(Boolean).map((idea, index) => (
                    <div key={index} className="flex items-start gap-2">
                      <span className="bg-red-500 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs font-bold shrink-0 mt-0.5">
                        {index + 1}
                      </span>
                      <p className="text-sm leading-relaxed">{idea}</p>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}

        {!note && (
          <div className="text-center py-4 text-muted-foreground">
            <p className="text-sm">No notes available</p>
          </div>
        )}

        {/* Tags */}
        {paper.tags && paper.tags.length > 0 && (
          <div className="flex flex-wrap gap-1 mt-3">
            {paper.tags.map((tag) => (
              <Badge key={tag} variant="outline" className="text-xs">
                {tag}
              </Badge>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  )
}
