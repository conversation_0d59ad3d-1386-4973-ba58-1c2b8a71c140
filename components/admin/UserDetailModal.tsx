'use client'

import React, { useState, useEffect } from 'react'
import { <PERSON>alog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { useToast } from '@/hooks/use-toast'
import { authenticatedFetch } from '@/lib/utils'
import { 
  User, 
  Mail, 
  Calendar, 
  Shield, 
  FileText, 
  BookOpen, 
  Activity,
  Lock,
  Unlock,
  Key,
  Trash2,
  Save,
  X
} from 'lucide-react'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog'

interface UserDetail {
  id: string
  email: string
  displayName?: string
  role: 'admin' | 'user' | 'readonly'
  emailVerified: boolean
  isActive: boolean
  createdAt: string
  updatedAt: string
  lastLogin?: string
  paperCount: number
  collectionCount: number
  sessionCount: number
  lastSession?: string
}

interface UserDetailModalProps {
  userId: string | null
  isOpen: boolean
  onClose: () => void
  onUserUpdated: () => void
}

export function UserDetailModal({ userId, isOpen, onClose, onUserUpdated }: UserDetailModalProps) {
  const { toast } = useToast()
  const [user, setUser] = useState<UserDetail | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [isEditing, setIsEditing] = useState(false)
  const [editForm, setEditForm] = useState({
    displayName: '',
    role: 'user' as 'admin' | 'user' | 'readonly',
    isActive: true,
    emailVerified: true
  })
  const [passwordReset, setPasswordReset] = useState({
    isOpen: false,
    newPassword: '',
    confirmPassword: ''
  })
  const [deleteConfirm, setDeleteConfirm] = useState(false)
  const [lockAction, setLockAction] = useState({
    isOpen: false,
    action: 'lock' as 'lock' | 'unlock',
    reason: ''
  })

  useEffect(() => {
    if (userId && isOpen) {
      fetchUserDetails()
    }
  }, [userId, isOpen])

  const fetchUserDetails = async () => {
    if (!userId) return
    
    try {
      setIsLoading(true)
      const response = await authenticatedFetch(`/api/admin/users/${userId}`)
      if (!response.ok) {
        throw new Error('Failed to fetch user details')
      }
      const data = await response.json()
      setUser(data.data)
      setEditForm({
        displayName: data.data.displayName || '',
        role: data.data.role,
        isActive: data.data.isActive,
        emailVerified: data.data.emailVerified
      })
    } catch (error) {
      console.error('Failed to fetch user details:', error)
      toast({
        title: 'Error',
        description: 'Failed to load user details',
        variant: 'destructive',
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleSaveChanges = async () => {
    if (!userId) return

    try {
      const response = await authenticatedFetch(`/api/admin/users/${userId}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(editForm)
      })

      if (!response.ok) {
        throw new Error('Failed to update user')
      }

      toast({
        title: 'Success',
        description: 'User updated successfully',
      })

      setIsEditing(false)
      fetchUserDetails()
      onUserUpdated()
    } catch (error) {
      console.error('Failed to update user:', error)
      toast({
        title: 'Error',
        description: 'Failed to update user',
        variant: 'destructive',
      })
    }
  }

  const handlePasswordReset = async () => {
    if (!userId || passwordReset.newPassword !== passwordReset.confirmPassword) {
      toast({
        title: 'Error',
        description: 'Passwords do not match',
        variant: 'destructive',
      })
      return
    }

    try {
      const response = await authenticatedFetch(`/api/admin/users/${userId}/password`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ newPassword: passwordReset.newPassword })
      })

      if (!response.ok) {
        throw new Error('Failed to reset password')
      }

      toast({
        title: 'Success',
        description: 'Password reset successfully',
      })

      setPasswordReset({ isOpen: false, newPassword: '', confirmPassword: '' })
    } catch (error) {
      console.error('Failed to reset password:', error)
      toast({
        title: 'Error',
        description: 'Failed to reset password',
        variant: 'destructive',
      })
    }
  }

  const handleLockAction = async () => {
    if (!userId) return

    try {
      const response = await authenticatedFetch(`/api/admin/users/${userId}/lock`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          locked: lockAction.action === 'lock',
          reason: lockAction.reason 
        })
      })

      if (!response.ok) {
        throw new Error(`Failed to ${lockAction.action} user`)
      }

      toast({
        title: 'Success',
        description: `User ${lockAction.action}ed successfully`,
      })

      setLockAction({ isOpen: false, action: 'lock', reason: '' })
      fetchUserDetails()
      onUserUpdated()
    } catch (error) {
      console.error(`Failed to ${lockAction.action} user:`, error)
      toast({
        title: 'Error',
        description: `Failed to ${lockAction.action} user`,
        variant: 'destructive',
      })
    }
  }

  const handleDeleteUser = async () => {
    if (!userId) return

    try {
      const response = await authenticatedFetch(`/api/admin/users/${userId}`, {
        method: 'DELETE'
      })

      if (!response.ok) {
        throw new Error('Failed to delete user')
      }

      toast({
        title: 'Success',
        description: 'User deleted successfully',
      })

      setDeleteConfirm(false)
      onClose()
      onUserUpdated()
    } catch (error) {
      console.error('Failed to delete user:', error)
      toast({
        title: 'Error',
        description: 'Failed to delete user',
        variant: 'destructive',
      })
    }
  }

  const getRoleBadge = (role: string) => {
    switch (role) {
      case 'admin':
        return <Badge variant="destructive">Admin</Badge>
      case 'user':
        return <Badge variant="default">User</Badge>
      case 'readonly':
        return <Badge variant="secondary">Read Only</Badge>
      default:
        return <Badge variant="outline">{role}</Badge>
    }
  }

  const getStatusBadge = (isActive: boolean, emailVerified: boolean) => {
    if (!isActive) {
      return <Badge variant="destructive">Locked</Badge>
    }
    if (!emailVerified) {
      return <Badge variant="secondary">Unverified</Badge>
    }
    return <Badge variant="default" className="bg-green-500">Active</Badge>
  }

  if (!user && !isLoading) {
    return null
  }

  return (
    <>
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <User className="h-5 w-5" />
              User Details
            </DialogTitle>
            <DialogDescription>
              View and manage user account information
            </DialogDescription>
          </DialogHeader>

          {isLoading ? (
            <div className="flex items-center justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            </div>
          ) : user ? (
            <div className="space-y-6">
              {/* Basic Info */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Basic Information</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label>Email</Label>
                      <div className="flex items-center gap-2 mt-1">
                        <Mail className="h-4 w-4 text-muted-foreground" />
                        <span className="text-sm">{user.email}</span>
                      </div>
                    </div>
                    <div>
                      <Label>Display Name</Label>
                      {isEditing ? (
                        <Input
                          value={editForm.displayName}
                          onChange={(e) => setEditForm(prev => ({ ...prev, displayName: e.target.value }))}
                          placeholder="Enter display name"
                        />
                      ) : (
                        <div className="flex items-center gap-2 mt-1">
                          <User className="h-4 w-4 text-muted-foreground" />
                          <span className="text-sm">{user.displayName || 'Not set'}</span>
                        </div>
                      )}
                    </div>
                    <div>
                      <Label>Role</Label>
                      {isEditing ? (
                        <select
                          value={editForm.role}
                          onChange={(e) => setEditForm(prev => ({ ...prev, role: e.target.value as any }))}
                          className="w-full mt-1 px-3 py-2 border border-input bg-background rounded-md"
                        >
                          <option value="user">User</option>
                          <option value="admin">Admin</option>
                          <option value="readonly">Read Only</option>
                        </select>
                      ) : (
                        <div className="mt-1">{getRoleBadge(user.role)}</div>
                      )}
                    </div>
                    <div>
                      <Label>Status</Label>
                      <div className="mt-1">{getStatusBadge(user.isActive, user.emailVerified)}</div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Statistics */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Statistics</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-3 gap-4">
                    <div className="text-center">
                      <FileText className="h-8 w-8 mx-auto text-muted-foreground mb-2" />
                      <div className="text-2xl font-bold">{user.paperCount}</div>
                      <div className="text-sm text-muted-foreground">Papers</div>
                    </div>
                    <div className="text-center">
                      <BookOpen className="h-8 w-8 mx-auto text-muted-foreground mb-2" />
                      <div className="text-2xl font-bold">{user.collectionCount}</div>
                      <div className="text-sm text-muted-foreground">Collections</div>
                    </div>
                    <div className="text-center">
                      <Activity className="h-8 w-8 mx-auto text-muted-foreground mb-2" />
                      <div className="text-2xl font-bold">{user.sessionCount}</div>
                      <div className="text-sm text-muted-foreground">Sessions</div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Account Dates */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Account Timeline</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="flex items-center gap-2">
                    <Calendar className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm">Created: {new Date(user.createdAt).toLocaleString()}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Calendar className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm">Updated: {new Date(user.updatedAt).toLocaleString()}</span>
                  </div>
                  {user.lastLogin && (
                    <div className="flex items-center gap-2">
                      <Activity className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm">Last Login: {new Date(user.lastLogin).toLocaleString()}</span>
                    </div>
                  )}
                  {user.lastSession && (
                    <div className="flex items-center gap-2">
                      <Activity className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm">Last Session: {new Date(user.lastSession).toLocaleString()}</span>
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* Actions */}
              <div className="flex flex-wrap gap-2 pt-4 border-t">
                {isEditing ? (
                  <>
                    <Button onClick={handleSaveChanges}>
                      <Save className="mr-2 h-4 w-4" />
                      Save Changes
                    </Button>
                    <Button variant="outline" onClick={() => setIsEditing(false)}>
                      <X className="mr-2 h-4 w-4" />
                      Cancel
                    </Button>
                  </>
                ) : (
                  <>
                    <Button onClick={() => setIsEditing(true)}>
                      Edit User
                    </Button>
                    <Button 
                      variant="outline" 
                      onClick={() => setPasswordReset({ isOpen: true, newPassword: '', confirmPassword: '' })}
                    >
                      <Key className="mr-2 h-4 w-4" />
                      Reset Password
                    </Button>
                    <Button 
                      variant="outline" 
                      onClick={() => setLockAction({ 
                        isOpen: true, 
                        action: user.isActive ? 'lock' : 'unlock',
                        reason: '' 
                      })}
                    >
                      {user.isActive ? (
                        <>
                          <Lock className="mr-2 h-4 w-4" />
                          Lock Account
                        </>
                      ) : (
                        <>
                          <Unlock className="mr-2 h-4 w-4" />
                          Unlock Account
                        </>
                      )}
                    </Button>
                    <Button 
                      variant="destructive" 
                      onClick={() => setDeleteConfirm(true)}
                    >
                      <Trash2 className="mr-2 h-4 w-4" />
                      Delete User
                    </Button>
                  </>
                )}
              </div>
            </div>
          ) : null}
        </DialogContent>
      </Dialog>

      {/* Password Reset Dialog */}
      <Dialog open={passwordReset.isOpen} onOpenChange={(open) => 
        setPasswordReset(prev => ({ ...prev, isOpen: open }))
      }>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Reset Password</DialogTitle>
            <DialogDescription>
              Set a new password for this user
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="newPassword">New Password</Label>
              <Input
                id="newPassword"
                type="password"
                value={passwordReset.newPassword}
                onChange={(e) => setPasswordReset(prev => ({ ...prev, newPassword: e.target.value }))}
                placeholder="Enter new password"
              />
            </div>
            <div>
              <Label htmlFor="confirmPassword">Confirm Password</Label>
              <Input
                id="confirmPassword"
                type="password"
                value={passwordReset.confirmPassword}
                onChange={(e) => setPasswordReset(prev => ({ ...prev, confirmPassword: e.target.value }))}
                placeholder="Confirm new password"
              />
            </div>
          </div>
          <div className="flex justify-end gap-2 pt-4">
            <Button variant="outline" onClick={() => setPasswordReset({ isOpen: false, newPassword: '', confirmPassword: '' })}>
              Cancel
            </Button>
            <Button onClick={handlePasswordReset}>
              Reset Password
            </Button>
          </div>
        </DialogContent>
      </Dialog>

      {/* Lock/Unlock Dialog */}
      <Dialog open={lockAction.isOpen} onOpenChange={(open) => 
        setLockAction(prev => ({ ...prev, isOpen: open }))
      }>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{lockAction.action === 'lock' ? 'Lock' : 'Unlock'} Account</DialogTitle>
            <DialogDescription>
              {lockAction.action === 'lock' 
                ? 'This will prevent the user from accessing their account'
                : 'This will restore the user\'s access to their account'
              }
            </DialogDescription>
          </DialogHeader>
          {lockAction.action === 'lock' && (
            <div>
              <Label htmlFor="reason">Reason (optional)</Label>
              <Textarea
                id="reason"
                value={lockAction.reason}
                onChange={(e) => setLockAction(prev => ({ ...prev, reason: e.target.value }))}
                placeholder="Enter reason for locking account"
              />
            </div>
          )}
          <div className="flex justify-end gap-2 pt-4">
            <Button variant="outline" onClick={() => setLockAction({ isOpen: false, action: 'lock', reason: '' })}>
              Cancel
            </Button>
            <Button 
              variant={lockAction.action === 'lock' ? 'destructive' : 'default'}
              onClick={handleLockAction}
            >
              {lockAction.action === 'lock' ? 'Lock Account' : 'Unlock Account'}
            </Button>
          </div>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={deleteConfirm} onOpenChange={setDeleteConfirm}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete User</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete the user account
              and all associated data including papers, collections, and reviews.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handleDeleteUser} className="bg-destructive text-destructive-foreground">
              Delete User
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  )
}
