"use client"

import { useState, useEffect } from "react"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Library, Users, AlertCircle } from "lucide-react"
import { authenticatedFetch } from "@/lib/utils"

interface ZoteroGroup {
  id: string
  name: string
  description?: string
  type: 'PublicOpen' | 'PublicClosed' | 'Private'
  memberCount?: number
}

interface ZoteroDestinationSelectorProps {
  value?: {
    libraryType?: 'user' | 'group'
    libraryId?: string
  }
  onChange: (destination: { libraryType?: 'user' | 'group'; libraryId?: string }) => void
  disabled?: boolean
  showLabel?: boolean
  className?: string
}

/**
 * Component for selecting Zotero sync destination (My Library or specific group)
 */
export function ZoteroDestinationSelector({
  value,
  onChange,
  disabled = false,
  showLabel = true,
  className = ""
}: ZoteroDestinationSelectorProps) {
  const [groups, setGroups] = useState<ZoteroGroup[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    fetchZoteroGroups()
  }, [])

  const fetchZoteroGroups = async () => {
    try {
      setLoading(true)
      setError(null)
      
      const response = await authenticatedFetch('/api/zotero/groups')
      
      if (response.ok) {
        const data = await response.json()
        setGroups(data.data || [])
      } else if (response.status === 400) {
        // Zotero not configured - this is expected for some users
        setGroups([])
      } else {
        throw new Error('Failed to fetch Zotero groups')
      }
    } catch (err) {
      console.error('Error fetching Zotero groups:', err)
      setError('Failed to load Zotero groups')
      setGroups([])
    } finally {
      setLoading(false)
    }
  }

  const handleValueChange = (selectedValue: string) => {
    if (selectedValue === 'default') {
      onChange({ libraryType: undefined, libraryId: undefined })
    } else if (selectedValue === 'user') {
      onChange({ libraryType: 'user', libraryId: undefined })
    } else {
      // Group selection - format is "group:groupId"
      const groupId = selectedValue.replace('group:', '')
      onChange({ libraryType: 'group', libraryId: groupId })
    }
  }

  const getCurrentValue = () => {
    if (!value?.libraryType) return 'default'
    if (value.libraryType === 'user') return 'user'
    if (value.libraryType === 'group' && value.libraryId) return `group:${value.libraryId}`
    return 'default'
  }

  const getDisplayName = (group: ZoteroGroup) => {
    const truncated = group.name.length > 40 ? `${group.name.substring(0, 40)}...` : group.name
    return truncated
  }

  return (
    <div className={className}>
      {showLabel && (
        <Label htmlFor="zotero-destination" className="text-sm font-medium">
          Zotero Sync Destination (Optional)
        </Label>
      )}
      
      <Select
        value={getCurrentValue()}
        onValueChange={handleValueChange}
        disabled={disabled || loading}
      >
        <SelectTrigger className="w-full">
          <SelectValue placeholder="Use default Zotero settings" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="default">
            <div className="flex items-center gap-2">
              <AlertCircle className="h-4 w-4 text-muted-foreground" />
              <span>Use default settings</span>
            </div>
          </SelectItem>
          
          <SelectItem value="user">
            <div className="flex items-center gap-2">
              <Library className="h-4 w-4 text-blue-500" />
              <span>My Library</span>
            </div>
          </SelectItem>
          
          {groups.length > 0 && (
            <>
              {groups.map((group) => (
                <SelectItem key={group.id} value={`group:${group.id}`}>
                  <div className="flex items-center gap-2 w-full">
                    <Users className="h-4 w-4 text-green-500" />
                    <div className="flex-1 min-w-0">
                      <div className="truncate">{getDisplayName(group)}</div>
                      {group.type && (
                        <Badge variant="outline" className="text-xs">
                          {group.type}
                        </Badge>
                      )}
                    </div>
                  </div>
                </SelectItem>
              ))}
            </>
          )}
        </SelectContent>
      </Select>
      
      {error && (
        <p className="text-sm text-destructive mt-1">{error}</p>
      )}
      
      {!loading && groups.length === 0 && !error && (
        <p className="text-sm text-muted-foreground mt-1">
          No Zotero groups available. Configure Zotero in settings to see groups.
        </p>
      )}
      
      <p className="text-xs text-muted-foreground mt-1">
        If not specified, papers from this collection will sync to your default Zotero destination.
      </p>
    </div>
  )
}
